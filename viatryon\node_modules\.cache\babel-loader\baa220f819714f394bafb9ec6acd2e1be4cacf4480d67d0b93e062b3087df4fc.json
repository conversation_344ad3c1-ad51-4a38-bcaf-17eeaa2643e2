{"ast": null, "code": "// Standalone U-shape cutter function\n// Usage: uShapeCutter(imageSrc).then(dataUrl => ...)\nfunction uShapeCutter(src) {\n  return new Promise((resolve, reject) => {\n    const img = new window.Image();\n    img.crossOrigin = 'Anonymous';\n    img.src = src;\n    img.onload = () => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n      // U-shape params (from bracelet.html) - Adjusted for better bracelet curve preservation\n      const openingWidthPercent = 55; // Reduced from 70 to preserve more bracelet curve\n      const cHeightPercent = 31;\n      const thicknessPercent = 13;\n      const verticalPosPercent = 50;\n      const horizontalPosPercent = 33;\n      const canvasWidth = canvas.width;\n      const canvasHeight = canvas.height;\n      const uWidth = canvasWidth * openingWidthPercent / 100;\n      const uHeight = canvasHeight * cHeightPercent / 100;\n      const uThickness = Math.min(canvasWidth, canvasHeight) * thicknessPercent / 100;\n      const centerX = canvasWidth * verticalPosPercent / 100;\n      const centerY = canvasHeight * horizontalPosPercent / 100;\n      ctx.fillStyle = 'white';\n      ctx.beginPath();\n      const outerRadius = uThickness / 2;\n      const outerLeft = centerX - uWidth / 2;\n      const outerTop = centerY - uHeight / 2;\n      const outerRight = centerX + uWidth / 2;\n      const outerBottom = centerY + uHeight / 2;\n      ctx.moveTo(outerLeft + outerRadius, outerBottom);\n      ctx.lineTo(outerRight - outerRadius, outerBottom);\n      ctx.quadraticCurveTo(outerRight, outerBottom, outerRight, outerBottom - outerRadius);\n      ctx.lineTo(outerRight, outerTop + outerRadius);\n      ctx.quadraticCurveTo(outerRight, outerTop, outerRight - outerRadius, outerTop);\n      ctx.lineTo(centerX + uThickness / 2 + outerRadius, outerTop);\n      ctx.quadraticCurveTo(centerX + uThickness / 2, outerTop, centerX + uThickness / 2, outerTop + outerRadius);\n      ctx.lineTo(centerX + uThickness / 2, centerY - uThickness / 2);\n      ctx.lineTo(centerX - uThickness / 2, centerY - uThickness / 2);\n      ctx.lineTo(centerX - uThickness / 2, outerTop + outerRadius);\n      ctx.quadraticCurveTo(centerX - uThickness / 2, outerTop, centerX - uThickness / 2 - outerRadius, outerTop);\n      ctx.lineTo(outerLeft + outerRadius, outerTop);\n      ctx.quadraticCurveTo(outerLeft, outerTop, outerLeft, outerTop + outerRadius);\n      ctx.lineTo(outerLeft, outerBottom - outerRadius);\n      ctx.quadraticCurveTo(outerLeft, outerBottom, outerLeft + outerRadius, outerBottom);\n      ctx.closePath();\n      ctx.fill();\n      resolve(canvas.toDataURL('image/png'));\n    };\n    img.onerror = reject;\n  });\n}\nexport default uShapeCutter;", "map": {"version": 3, "names": ["uShapeCutter", "src", "Promise", "resolve", "reject", "img", "window", "Image", "crossOrigin", "onload", "canvas", "document", "createElement", "ctx", "getContext", "width", "naturalWidth", "height", "naturalHeight", "drawImage", "openingWidthPercent", "cHeightPercent", "thicknessPercent", "verticalPosPercent", "horizontalPosPercent", "canvasWidth", "canvasHeight", "uWidth", "uHeight", "uThickness", "Math", "min", "centerX", "centerY", "fillStyle", "beginPath", "outerRadius", "outerLeft", "outerTop", "outerRight", "outerBottom", "moveTo", "lineTo", "quadraticCurveTo", "closePath", "fill", "toDataURL", "onerror"], "sources": ["D:/Via/test/viatryon/src/utils/uShapeCutter.js"], "sourcesContent": ["// Standalone U-shape cutter function\r\n// Usage: uShapeCutter(imageSrc).then(dataUrl => ...)\r\nfunction uShapeCutter(src) {\r\n  return new Promise((resolve, reject) => {\r\n    const img = new window.Image();\r\n    img.crossOrigin = 'Anonymous';\r\n    img.src = src;\r\n    img.onload = () => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      canvas.width = img.naturalWidth;\r\n      canvas.height = img.naturalHeight;\r\n      ctx.drawImage(img, 0, 0);\r\n      // U-shape params (from bracelet.html) - Adjusted for better bracelet curve preservation\r\n      const openingWidthPercent = 55; // Reduced from 70 to preserve more bracelet curve\r\n      const cHeightPercent = 31;\r\n      const thicknessPercent = 13;\r\n      const verticalPosPercent = 50;\r\n      const horizontalPosPercent = 33;\r\n      const canvasWidth = canvas.width;\r\n      const canvasHeight = canvas.height;\r\n      const uWidth = (canvasWidth * openingWidthPercent) / 100;\r\n      const uHeight = (canvasHeight * cHeightPercent) / 100;\r\n      const uThickness = (Math.min(canvasWidth, canvasHeight) * thicknessPercent) / 100;\r\n      const centerX = (canvasWidth * verticalPosPercent) / 100;\r\n      const centerY = (canvasHeight * horizontalPosPercent) / 100;\r\n      ctx.fillStyle = 'white';\r\n      ctx.beginPath();\r\n      const outerRadius = uThickness / 2;\r\n      const outerLeft = centerX - uWidth / 2;\r\n      const outerTop = centerY - uHeight / 2;\r\n      const outerRight = centerX + uWidth / 2;\r\n      const outerBottom = centerY + uHeight / 2;\r\n      ctx.moveTo(outerLeft + outerRadius, outerBottom);\r\n      ctx.lineTo(outerRight - outerRadius, outerBottom);\r\n      ctx.quadraticCurveTo(outerRight, outerBottom, outerRight, outerBottom - outerRadius);\r\n      ctx.lineTo(outerRight, outerTop + outerRadius);\r\n      ctx.quadraticCurveTo(outerRight, outerTop, outerRight - outerRadius, outerTop);\r\n      ctx.lineTo(centerX + uThickness/2 + outerRadius, outerTop);\r\n      ctx.quadraticCurveTo(centerX + uThickness/2, outerTop, centerX + uThickness/2, outerTop + outerRadius);\r\n      ctx.lineTo(centerX + uThickness/2, centerY - uThickness/2);\r\n      ctx.lineTo(centerX - uThickness/2, centerY - uThickness/2);\r\n      ctx.lineTo(centerX - uThickness/2, outerTop + outerRadius);\r\n      ctx.quadraticCurveTo(centerX - uThickness/2, outerTop, centerX - uThickness/2 - outerRadius, outerTop);\r\n      ctx.lineTo(outerLeft + outerRadius, outerTop);\r\n      ctx.quadraticCurveTo(outerLeft, outerTop, outerLeft, outerTop + outerRadius);\r\n      ctx.lineTo(outerLeft, outerBottom - outerRadius);\r\n      ctx.quadraticCurveTo(outerLeft, outerBottom, outerLeft + outerRadius, outerBottom);\r\n      ctx.closePath();\r\n      ctx.fill();\r\n      resolve(canvas.toDataURL('image/png'));\r\n    };\r\n    img.onerror = reject;\r\n  });\r\n}\r\n\r\nexport default uShapeCutter; "], "mappings": "AAAA;AACA;AACA,SAASA,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;IAC9BF,GAAG,CAACG,WAAW,GAAG,WAAW;IAC7BH,GAAG,CAACJ,GAAG,GAAGA,GAAG;IACbI,GAAG,CAACI,MAAM,GAAG,MAAM;MACjB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MACnCJ,MAAM,CAACK,KAAK,GAAGV,GAAG,CAACW,YAAY;MAC/BN,MAAM,CAACO,MAAM,GAAGZ,GAAG,CAACa,aAAa;MACjCL,GAAG,CAACM,SAAS,CAACd,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MACxB;MACA,MAAMe,mBAAmB,GAAG,EAAE,CAAC,CAAC;MAChC,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,gBAAgB,GAAG,EAAE;MAC3B,MAAMC,kBAAkB,GAAG,EAAE;MAC7B,MAAMC,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,WAAW,GAAGf,MAAM,CAACK,KAAK;MAChC,MAAMW,YAAY,GAAGhB,MAAM,CAACO,MAAM;MAClC,MAAMU,MAAM,GAAIF,WAAW,GAAGL,mBAAmB,GAAI,GAAG;MACxD,MAAMQ,OAAO,GAAIF,YAAY,GAAGL,cAAc,GAAI,GAAG;MACrD,MAAMQ,UAAU,GAAIC,IAAI,CAACC,GAAG,CAACN,WAAW,EAAEC,YAAY,CAAC,GAAGJ,gBAAgB,GAAI,GAAG;MACjF,MAAMU,OAAO,GAAIP,WAAW,GAAGF,kBAAkB,GAAI,GAAG;MACxD,MAAMU,OAAO,GAAIP,YAAY,GAAGF,oBAAoB,GAAI,GAAG;MAC3DX,GAAG,CAACqB,SAAS,GAAG,OAAO;MACvBrB,GAAG,CAACsB,SAAS,CAAC,CAAC;MACf,MAAMC,WAAW,GAAGP,UAAU,GAAG,CAAC;MAClC,MAAMQ,SAAS,GAAGL,OAAO,GAAGL,MAAM,GAAG,CAAC;MACtC,MAAMW,QAAQ,GAAGL,OAAO,GAAGL,OAAO,GAAG,CAAC;MACtC,MAAMW,UAAU,GAAGP,OAAO,GAAGL,MAAM,GAAG,CAAC;MACvC,MAAMa,WAAW,GAAGP,OAAO,GAAGL,OAAO,GAAG,CAAC;MACzCf,GAAG,CAAC4B,MAAM,CAACJ,SAAS,GAAGD,WAAW,EAAEI,WAAW,CAAC;MAChD3B,GAAG,CAAC6B,MAAM,CAACH,UAAU,GAAGH,WAAW,EAAEI,WAAW,CAAC;MACjD3B,GAAG,CAAC8B,gBAAgB,CAACJ,UAAU,EAAEC,WAAW,EAAED,UAAU,EAAEC,WAAW,GAAGJ,WAAW,CAAC;MACpFvB,GAAG,CAAC6B,MAAM,CAACH,UAAU,EAAED,QAAQ,GAAGF,WAAW,CAAC;MAC9CvB,GAAG,CAAC8B,gBAAgB,CAACJ,UAAU,EAAED,QAAQ,EAAEC,UAAU,GAAGH,WAAW,EAAEE,QAAQ,CAAC;MAC9EzB,GAAG,CAAC6B,MAAM,CAACV,OAAO,GAAGH,UAAU,GAAC,CAAC,GAAGO,WAAW,EAAEE,QAAQ,CAAC;MAC1DzB,GAAG,CAAC8B,gBAAgB,CAACX,OAAO,GAAGH,UAAU,GAAC,CAAC,EAAES,QAAQ,EAAEN,OAAO,GAAGH,UAAU,GAAC,CAAC,EAAES,QAAQ,GAAGF,WAAW,CAAC;MACtGvB,GAAG,CAAC6B,MAAM,CAACV,OAAO,GAAGH,UAAU,GAAC,CAAC,EAAEI,OAAO,GAAGJ,UAAU,GAAC,CAAC,CAAC;MAC1DhB,GAAG,CAAC6B,MAAM,CAACV,OAAO,GAAGH,UAAU,GAAC,CAAC,EAAEI,OAAO,GAAGJ,UAAU,GAAC,CAAC,CAAC;MAC1DhB,GAAG,CAAC6B,MAAM,CAACV,OAAO,GAAGH,UAAU,GAAC,CAAC,EAAES,QAAQ,GAAGF,WAAW,CAAC;MAC1DvB,GAAG,CAAC8B,gBAAgB,CAACX,OAAO,GAAGH,UAAU,GAAC,CAAC,EAAES,QAAQ,EAAEN,OAAO,GAAGH,UAAU,GAAC,CAAC,GAAGO,WAAW,EAAEE,QAAQ,CAAC;MACtGzB,GAAG,CAAC6B,MAAM,CAACL,SAAS,GAAGD,WAAW,EAAEE,QAAQ,CAAC;MAC7CzB,GAAG,CAAC8B,gBAAgB,CAACN,SAAS,EAAEC,QAAQ,EAAED,SAAS,EAAEC,QAAQ,GAAGF,WAAW,CAAC;MAC5EvB,GAAG,CAAC6B,MAAM,CAACL,SAAS,EAAEG,WAAW,GAAGJ,WAAW,CAAC;MAChDvB,GAAG,CAAC8B,gBAAgB,CAACN,SAAS,EAAEG,WAAW,EAAEH,SAAS,GAAGD,WAAW,EAAEI,WAAW,CAAC;MAClF3B,GAAG,CAAC+B,SAAS,CAAC,CAAC;MACf/B,GAAG,CAACgC,IAAI,CAAC,CAAC;MACV1C,OAAO,CAACO,MAAM,CAACoC,SAAS,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;IACDzC,GAAG,CAAC0C,OAAO,GAAG3C,MAAM;EACtB,CAAC,CAAC;AACJ;AAEA,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}