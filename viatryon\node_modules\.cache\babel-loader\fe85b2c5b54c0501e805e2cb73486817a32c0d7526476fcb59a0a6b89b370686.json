{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductDetails.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useParams, useNavigate } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetails = () => {\n  _s();\n  const {\n    category,\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  useEffect(() => {\n    const loadProductData = async () => {\n      try {\n        // Load product collections with processed images\n        const collections = await getProductCollections();\n        if (category && id) {\n          var _collections$category;\n          // Find the product in the collection\n          const productId = parseInt(id);\n          const foundProduct = (_collections$category = collections[category]) === null || _collections$category === void 0 ? void 0 : _collections$category.find(p => p.id === productId);\n          if (foundProduct) {\n            setProduct(foundProduct);\n          } else {\n            // Handle product not found\n            console.error('Product not found');\n          }\n        }\n      } catch (error) {\n        console.error('Error loading product data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProductData();\n  }, [category, id]);\n\n  // Navigate to virtual try-on with this product\n  const handleTryOn = () => {\n    navigate(`/virtual-try-on?category=${category}&productId=${product.id}`);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-[#F9FAFB] flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#1F2937] font-sans\",\n          children: \"Loading product with background removal...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-[#F9FAFB]\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6 pt-36 pb-24 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-serif text-[#1F2937] mb-6\",\n          children: \"Product Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#1F2937] mb-8 font-sans\",\n          children: \"Sorry, we couldn't find the product you're looking for.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/${category}`,\n          className: \"inline-block bg-white border px-8 py-3 rounded-full transition font-sans font-medium\",\n          style: {\n            borderColor: '#2D8C88',\n            color: '#2D8C88'\n          },\n          onMouseEnter: e => {\n            e.currentTarget.style.backgroundColor = '#2D8C88';\n            e.currentTarget.style.color = 'white';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.backgroundColor = 'white';\n            e.currentTarget.style.color = '#2D8C88';\n          },\n          children: [\"Back to \", category]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB]\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-20 sm:pt-36 pb-4 bg-white shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 sm:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-xs sm:text-sm font-sans text-gray-500 overflow-x-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"hover:text-[#2D8C88] whitespace-nowrap\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mx-1 sm:mx-2\",\n            children: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/${category}`,\n            className: \"hover:text-[#2D8C88] capitalize whitespace-nowrap\",\n            children: category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mx-1 sm:mx-2\",\n            children: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-[#1F2937] truncate\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-6 sm:py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 sm:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-xl shadow-md p-4 sm:p-8 mb-6 transition-all duration-300 hover:shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative aspect-square bg-[#F9FAFB] rounded-lg flex items-center justify-center mb-4 sm:mb-6 overflow-hidden group\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.images[currentImageIndex],\n                  alt: product.name,\n                  className: \"max-h-full max-w-full object-contain transition-transform duration-500 group-hover:scale-105\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setCurrentImageIndex(prev => prev === 0 ? product.images.length - 1 : prev - 1),\n                  className: \"absolute left-4 bg-white bg-opacity-80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                  \"aria-label\": \"Previous image\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 text-[#1F2937]\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 19l-7-7 7-7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setCurrentImageIndex(prev => prev === product.images.length - 1 ? 0 : prev + 1),\n                  className: \"absolute right-4 bg-white bg-opacity-80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                  \"aria-label\": \"Next image\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 text-[#1F2937]\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 5l7 7-7 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2 sm:space-x-4 justify-center overflow-x-auto pb-2\",\n                children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setCurrentImageIndex(index),\n                  className: `w-16 h-16 sm:w-20 sm:h-20 rounded-md overflow-hidden border-2 transition-all hover:shadow-md flex-shrink-0 ${currentImageIndex === index ? 'border-[#2D8C88] scale-105' : 'border-transparent'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: image,\n                    alt: `${product.name} view ${index + 1}`,\n                    className: \"w-full h-full object-contain\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-xl shadow-md p-8 transition-all duration-300 hover:shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-serif text-[#1F2937] mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5 mr-2 text-[#F28C38]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), \"Why Choose This \", category === 'watches' ? 'Watch' : 'Bracelet']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5 text-[#2D8C88]\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-sans font-medium text-[#1F2937]\",\n                      children: \"Premium Quality\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 font-sans\",\n                      children: \"Crafted with the finest materials for durability and elegance.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5 text-[#2D8C88]\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-sans font-medium text-[#1F2937]\",\n                      children: \"Timeless Design\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 font-sans\",\n                      children: \"A classic style that complements any outfit and occasion.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5 text-[#2D8C88]\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-sans font-medium text-[#1F2937]\",\n                      children: \"Comfort Guaranteed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 font-sans\",\n                      children: \"Designed for all-day wear with maximum comfort.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-xl shadow-md p-8 mb-6 transition-all duration-300 hover:shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-b border-gray-100 pb-6 mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-3xl font-serif text-[#1F2937] mb-2\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F28C38] bg-opacity-10 rounded-full px-3 py-1 text-sm font-sans font-medium\",\n                    style: {\n                      color: '#F28C38'\n                    },\n                    children: \"Best Seller\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex text-[#F28C38]\",\n                  children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 25\n                    }, this)\n                  }, star, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm text-gray-600 font-sans\",\n                  children: \"4.9 (120 reviews)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-serif text-[#1F2937] mb-3\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#1F2937] font-sans leading-relaxed\",\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-serif text-[#1F2937] mb-3\",\n                  children: \"Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-3 font-sans text-[#1F2937]\",\n                  children: product.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5 mr-3 text-[#2D8C88] flex-shrink-0 mt-0.5\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 25\n                    }, this), feature]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col space-y-4\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleTryOn,\n                  className: \"w-full bg-[#2D8C88] text-white text-lg flex items-center justify-center px-8 py-5 rounded-full transition font-sans font-medium hover:bg-[#236e6a] shadow-lg hover:shadow-xl group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3 transform group-hover:scale-110 transition-transform\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this), \"Try On Virtually\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-xl shadow-md p-8 transition-all duration-300 hover:shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-serif text-[#1F2937] mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5 mr-2 text-[#2D8C88]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), \"Specifications\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: Object.entries(product.specifications).map(([key, value], index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between py-3 border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-sans text-gray-600\",\n                    children: key\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-sans font-medium text-[#1F2937]\",\n                    children: value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetails, \"4HBXgK/o+OzHFtd4CwqdEC6dfLY=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useParams", "useNavigate", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "ProductDetails", "_s", "category", "id", "navigate", "product", "setProduct", "loading", "setLoading", "currentImageIndex", "setCurrentImageIndex", "loadProductData", "collections", "_collections$category", "productId", "parseInt", "foundProduct", "find", "p", "console", "error", "handleTryOn", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "style", "borderColor", "color", "onMouseEnter", "e", "currentTarget", "backgroundColor", "onMouseLeave", "name", "src", "images", "alt", "onClick", "prev", "length", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "image", "index", "star", "description", "features", "feature", "Object", "entries", "specifications", "key", "value", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductDetails.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useParams, useNavigate } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\n\nconst ProductDetails = () => {\n  const { category, id } = useParams();\n  const navigate = useNavigate();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n\n  useEffect(() => {\n    const loadProductData = async () => {\n      try {\n        // Load product collections with processed images\n        const collections = await getProductCollections();\n\n        if (category && id) {\n          // Find the product in the collection\n          const productId = parseInt(id);\n          const foundProduct = collections[category]?.find(p => p.id === productId);\n\n          if (foundProduct) {\n            setProduct(foundProduct);\n          } else {\n            // Handle product not found\n            console.error('Product not found');\n          }\n        }\n      } catch (error) {\n        console.error('Error loading product data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadProductData();\n  }, [category, id]);\n\n  // Navigate to virtual try-on with this product\n  const handleTryOn = () => {\n    navigate(`/virtual-try-on?category=${category}&productId=${product.id}`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-[#F9FAFB] flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4\"></div>\n          <p className=\"text-[#1F2937] font-sans\">Loading product with background removal...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!product) {\n    return (\n      <div className=\"min-h-screen bg-[#F9FAFB]\">\n        <Navbar />\n        <div className=\"container mx-auto px-6 pt-36 pb-24 text-center\">\n          <h1 className=\"text-3xl font-serif text-[#1F2937] mb-6\">Product Not Found</h1>\n          <p className=\"text-[#1F2937] mb-8 font-sans\">\n            Sorry, we couldn't find the product you're looking for.\n          </p>\n          <Link\n            to={`/${category}`}\n            className=\"inline-block bg-white border px-8 py-3 rounded-full transition font-sans font-medium\"\n            style={{ borderColor: '#2D8C88', color: '#2D8C88' }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = '#2D8C88';\n              e.currentTarget.style.color = 'white';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.backgroundColor = 'white';\n              e.currentTarget.style.color = '#2D8C88';\n            }}\n          >\n            Back to {category}\n          </Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-[#F9FAFB]\">\n      <Navbar />\n\n      {/* Hero Section with Breadcrumb */}\n      <section className=\"pt-20 sm:pt-36 pb-4 bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 sm:px-6\">\n          <div className=\"flex items-center text-xs sm:text-sm font-sans text-gray-500 overflow-x-auto\">\n            <Link to=\"/\" className=\"hover:text-[#2D8C88] whitespace-nowrap\">Home</Link>\n            <span className=\"mx-1 sm:mx-2\">/</span>\n            <Link to={`/${category}`} className=\"hover:text-[#2D8C88] capitalize whitespace-nowrap\">{category}</Link>\n            <span className=\"mx-1 sm:mx-2\">/</span>\n            <span className=\"text-[#1F2937] truncate\">{product.name}</span>\n          </div>\n        </div>\n      </section>\n\n      {/* Product Details */}\n      <section className=\"py-6 sm:py-12\">\n        <div className=\"container mx-auto px-4 sm:px-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-12\">\n            {/* Product Images */}\n            <div>\n              <div className=\"bg-white rounded-xl shadow-md p-4 sm:p-8 mb-6 transition-all duration-300 hover:shadow-lg\">\n                <div className=\"relative aspect-square bg-[#F9FAFB] rounded-lg flex items-center justify-center mb-4 sm:mb-6 overflow-hidden group\">\n                  <img\n                    src={product.images[currentImageIndex]}\n                    alt={product.name}\n                    className=\"max-h-full max-w-full object-contain transition-transform duration-500 group-hover:scale-105\"\n                  />\n\n                  {/* Image navigation arrows */}\n                  <button\n                    onClick={() => setCurrentImageIndex(prev => (prev === 0 ? product.images.length - 1 : prev - 1))}\n                    className=\"absolute left-4 bg-white bg-opacity-80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    aria-label=\"Previous image\"\n                  >\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-[#1F2937]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                    </svg>\n                  </button>\n\n                  <button\n                    onClick={() => setCurrentImageIndex(prev => (prev === product.images.length - 1 ? 0 : prev + 1))}\n                    className=\"absolute right-4 bg-white bg-opacity-80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    aria-label=\"Next image\"\n                  >\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-[#1F2937]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </button>\n                </div>\n\n                <div className=\"flex space-x-2 sm:space-x-4 justify-center overflow-x-auto pb-2\">\n                  {product.images.map((image, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setCurrentImageIndex(index)}\n                      className={`w-16 h-16 sm:w-20 sm:h-20 rounded-md overflow-hidden border-2 transition-all hover:shadow-md flex-shrink-0 ${\n                        currentImageIndex === index ? 'border-[#2D8C88] scale-105' : 'border-transparent'\n                      }`}\n                    >\n                      <img\n                        src={image}\n                        alt={`${product.name} view ${index + 1}`}\n                        className=\"w-full h-full object-contain\"\n                      />\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Additional product info card */}\n              <div className=\"bg-white rounded-xl shadow-md p-8 transition-all duration-300 hover:shadow-lg\">\n                <h2 className=\"text-xl font-serif text-[#1F2937] mb-4 flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 text-[#F28C38]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  Why Choose This {category === 'watches' ? 'Watch' : 'Bracelet'}\n                </h2>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-start\">\n                    <div className=\"bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-sans font-medium text-[#1F2937]\">Premium Quality</h3>\n                      <p className=\"text-sm text-gray-600 font-sans\">Crafted with the finest materials for durability and elegance.</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-sans font-medium text-[#1F2937]\">Timeless Design</h3>\n                      <p className=\"text-sm text-gray-600 font-sans\">A classic style that complements any outfit and occasion.</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-sans font-medium text-[#1F2937]\">Comfort Guaranteed</h3>\n                      <p className=\"text-sm text-gray-600 font-sans\">Designed for all-day wear with maximum comfort.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Product Info */}\n            <div>\n              <div className=\"bg-white rounded-xl shadow-md p-8 mb-6 transition-all duration-300 hover:shadow-lg\">\n                {/* Product title and price */}\n                <div className=\"border-b border-gray-100 pb-6 mb-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <h1 className=\"text-3xl font-serif text-[#1F2937] mb-2\">{product.name}</h1>\n                    <div className=\"bg-[#F28C38] bg-opacity-10 rounded-full px-3 py-1 text-sm font-sans font-medium\" style={{ color: '#F28C38' }}>\n                      Best Seller\n                    </div>\n                  </div>\n                </div>\n\n                {/* Rating */}\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"flex text-[#F28C38]\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <svg key={star} xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                      </svg>\n                    ))}\n                  </div>\n                  <span className=\"ml-2 text-sm text-gray-600 font-sans\">4.9 (120 reviews)</span>\n                </div>\n\n                {/* Description */}\n                <div className=\"mb-8\">\n                  <h2 className=\"text-xl font-serif text-[#1F2937] mb-3\">Description</h2>\n                  <p className=\"text-[#1F2937] font-sans leading-relaxed\">\n                    {product.description}\n                  </p>\n                </div>\n\n                {/* Features */}\n                <div className=\"space-y-4 mb-8\">\n                  <h2 className=\"text-xl font-serif text-[#1F2937] mb-3\">Features</h2>\n                  <ul className=\"space-y-3 font-sans text-[#1F2937]\">\n                    {product.features.map((feature, index) => (\n                      <li key={index} className=\"flex items-start\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3 text-[#2D8C88] flex-shrink-0 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Action buttons */}\n                <div className=\"flex flex-col space-y-4\">\n                  <button\n                    onClick={handleTryOn}\n                    className=\"w-full bg-[#2D8C88] text-white text-lg flex items-center justify-center px-8 py-5 rounded-full transition font-sans font-medium hover:bg-[#236e6a] shadow-lg hover:shadow-xl group\"\n                  >\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3 transform group-hover:scale-110 transition-transform\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                    Try On Virtually\n                  </button>\n                </div>\n              </div>\n\n              {/* Specifications */}\n              <div className=\"bg-white rounded-xl shadow-md p-8 transition-all duration-300 hover:shadow-lg\">\n                <h2 className=\"text-xl font-serif text-[#1F2937] mb-6 flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                  </svg>\n                  Specifications\n                </h2>\n                <div className=\"space-y-3\">\n                  {Object.entries(product.specifications).map(([key, value], index) => (\n                    <div key={index} className=\"flex justify-between py-3 border-b border-gray-100\">\n                      <span className=\"font-sans text-gray-600\">{key}</span>\n                      <span className=\"font-sans font-medium text-[#1F2937]\">{value}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default ProductDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,QAAQ;IAAEC;EAAG,CAAC,GAAGV,SAAS,CAAC,CAAC;EACpC,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd,MAAMoB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMC,WAAW,GAAG,MAAMf,qBAAqB,CAAC,CAAC;QAEjD,IAAIK,QAAQ,IAAIC,EAAE,EAAE;UAAA,IAAAU,qBAAA;UAClB;UACA,MAAMC,SAAS,GAAGC,QAAQ,CAACZ,EAAE,CAAC;UAC9B,MAAMa,YAAY,IAAAH,qBAAA,GAAGD,WAAW,CAACV,QAAQ,CAAC,cAAAW,qBAAA,uBAArBA,qBAAA,CAAuBI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKW,SAAS,CAAC;UAEzE,IAAIE,YAAY,EAAE;YAChBV,UAAU,CAACU,YAAY,CAAC;UAC1B,CAAC,MAAM;YACL;YACAG,OAAO,CAACC,KAAK,CAAC,mBAAmB,CAAC;UACpC;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,SAAS;QACRZ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACT,QAAQ,EAAEC,EAAE,CAAC,CAAC;;EAElB;EACA,MAAMkB,WAAW,GAAGA,CAAA,KAAM;IACxBjB,QAAQ,CAAC,4BAA4BF,QAAQ,cAAcG,OAAO,CAACF,EAAE,EAAE,CAAC;EAC1E,CAAC;EAED,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKuB,SAAS,EAAC,4DAA4D;MAAAC,QAAA,eACzExB,OAAA;QAAKuB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxB,OAAA;UAAKuB,SAAS,EAAC;QAAyF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/G5B,OAAA;UAAGuB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACtB,OAAO,EAAE;IACZ,oBACEN,OAAA;MAAKuB,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCxB,OAAA,CAACJ,MAAM;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACV5B,OAAA;QAAKuB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DxB,OAAA;UAAIuB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E5B,OAAA;UAAGuB,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ5B,OAAA,CAACP,IAAI;UACHoC,EAAE,EAAE,IAAI1B,QAAQ,EAAG;UACnBoB,SAAS,EAAC,sFAAsF;UAChGO,KAAK,EAAE;YAAEC,WAAW,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAU,CAAE;UACpDC,YAAY,EAAGC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACL,KAAK,CAACM,eAAe,GAAG,SAAS;YACjDF,CAAC,CAACC,aAAa,CAACL,KAAK,CAACE,KAAK,GAAG,OAAO;UACvC,CAAE;UACFK,YAAY,EAAGH,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACL,KAAK,CAACM,eAAe,GAAG,OAAO;YAC/CF,CAAC,CAACC,aAAa,CAACL,KAAK,CAACE,KAAK,GAAG,SAAS;UACzC,CAAE;UAAAR,QAAA,GACH,UACS,EAACrB,QAAQ;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5B,OAAA,CAACH,MAAM;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAKuB,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCxB,OAAA,CAACJ,MAAM;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV5B,OAAA;MAASuB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACzDxB,OAAA;QAAKuB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CxB,OAAA;UAAKuB,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAC3FxB,OAAA,CAACP,IAAI;YAACoC,EAAE,EAAC,GAAG;YAACN,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3E5B,OAAA;YAAMuB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC5B,OAAA,CAACP,IAAI;YAACoC,EAAE,EAAE,IAAI1B,QAAQ,EAAG;YAACoB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAErB;UAAQ;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzG5B,OAAA;YAAMuB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC5B,OAAA;YAAMuB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAElB,OAAO,CAACgC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV5B,OAAA;MAASuB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCxB,OAAA;QAAKuB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CxB,OAAA;UAAKuB,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE9DxB,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAKuB,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBACxGxB,OAAA;gBAAKuB,SAAS,EAAC,oHAAoH;gBAAAC,QAAA,gBACjIxB,OAAA;kBACEuC,GAAG,EAAEjC,OAAO,CAACkC,MAAM,CAAC9B,iBAAiB,CAAE;kBACvC+B,GAAG,EAAEnC,OAAO,CAACgC,IAAK;kBAClBf,SAAS,EAAC;gBAA8F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC,eAGF5B,OAAA;kBACE0C,OAAO,EAAEA,CAAA,KAAM/B,oBAAoB,CAACgC,IAAI,IAAKA,IAAI,KAAK,CAAC,GAAGrC,OAAO,CAACkC,MAAM,CAACI,MAAM,GAAG,CAAC,GAAGD,IAAI,GAAG,CAAE,CAAE;kBACjGpB,SAAS,EAAC,2HAA2H;kBACrI,cAAW,gBAAgB;kBAAAC,QAAA,eAE3BxB,OAAA;oBAAK6C,KAAK,EAAC,4BAA4B;oBAACtB,SAAS,EAAC,wBAAwB;oBAACuB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAxB,QAAA,eAC9HxB,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAiB;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAET5B,OAAA;kBACE0C,OAAO,EAAEA,CAAA,KAAM/B,oBAAoB,CAACgC,IAAI,IAAKA,IAAI,KAAKrC,OAAO,CAACkC,MAAM,CAACI,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGD,IAAI,GAAG,CAAE,CAAE;kBACjGpB,SAAS,EAAC,4HAA4H;kBACtI,cAAW,YAAY;kBAAAC,QAAA,eAEvBxB,OAAA;oBAAK6C,KAAK,EAAC,4BAA4B;oBAACtB,SAAS,EAAC,wBAAwB;oBAACuB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAxB,QAAA,eAC9HxB,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAc;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN5B,OAAA;gBAAKuB,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC7ElB,OAAO,CAACkC,MAAM,CAACa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/BvD,OAAA;kBAEE0C,OAAO,EAAEA,CAAA,KAAM/B,oBAAoB,CAAC4C,KAAK,CAAE;kBAC3ChC,SAAS,EAAE,8GACTb,iBAAiB,KAAK6C,KAAK,GAAG,4BAA4B,GAAG,oBAAoB,EAChF;kBAAA/B,QAAA,eAEHxB,OAAA;oBACEuC,GAAG,EAAEe,KAAM;oBACXb,GAAG,EAAE,GAAGnC,OAAO,CAACgC,IAAI,SAASiB,KAAK,GAAG,CAAC,EAAG;oBACzChC,SAAS,EAAC;kBAA8B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC,GAVG2B,KAAK;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5B,OAAA;cAAKuB,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAC5FxB,OAAA;gBAAIuB,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,gBACtExB,OAAA;kBAAK6C,KAAK,EAAC,4BAA4B;kBAACtB,SAAS,EAAC,6BAA6B;kBAACuB,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAxB,QAAA,eACnIxB,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA2D;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC,oBACU,EAACzB,QAAQ,KAAK,SAAS,GAAG,OAAO,GAAG,UAAU;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACL5B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxB,OAAA;kBAAKuB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BxB,OAAA;oBAAKuB,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpExB,OAAA;sBAAK6C,KAAK,EAAC,4BAA4B;sBAACtB,SAAS,EAAC,wBAAwB;sBAACuB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAxB,QAAA,eAC9HxB,OAAA;wBAAMiD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5B,OAAA;oBAAAwB,QAAA,gBACExB,OAAA;sBAAIuB,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzE5B,OAAA;sBAAGuB,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAA8D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5B,OAAA;kBAAKuB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BxB,OAAA;oBAAKuB,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpExB,OAAA;sBAAK6C,KAAK,EAAC,4BAA4B;sBAACtB,SAAS,EAAC,wBAAwB;sBAACuB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAxB,QAAA,eAC9HxB,OAAA;wBAAMiD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5B,OAAA;oBAAAwB,QAAA,gBACExB,OAAA;sBAAIuB,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzE5B,OAAA;sBAAGuB,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAyD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5B,OAAA;kBAAKuB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BxB,OAAA;oBAAKuB,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpExB,OAAA;sBAAK6C,KAAK,EAAC,4BAA4B;sBAACtB,SAAS,EAAC,wBAAwB;sBAACuB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAxB,QAAA,eAC9HxB,OAAA;wBAAMiD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5B,OAAA;oBAAAwB,QAAA,gBACExB,OAAA;sBAAIuB,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5E5B,OAAA;sBAAGuB,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAA+C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAKuB,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBAEjGxB,OAAA;gBAAKuB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDxB,OAAA;kBAAKuB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CxB,OAAA;oBAAIuB,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAElB,OAAO,CAACgC;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3E5B,OAAA;oBAAKuB,SAAS,EAAC,iFAAiF;oBAACO,KAAK,EAAE;sBAAEE,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,EAAC;kBAE9H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5B,OAAA;gBAAKuB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCxB,OAAA;kBAAKuB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC6B,GAAG,CAAEG,IAAI,iBACxBxD,OAAA;oBAAgB6C,KAAK,EAAC,4BAA4B;oBAACtB,SAAS,EAAC,SAAS;oBAACwB,OAAO,EAAC,WAAW;oBAACD,IAAI,EAAC,cAAc;oBAAAtB,QAAA,eAC5GxB,OAAA;sBAAMoD,CAAC,EAAC;oBAA0V;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC,GAD7V4B,IAAI;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAET,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5B,OAAA;kBAAMuB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eAGN5B,OAAA;gBAAKuB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBxB,OAAA;kBAAIuB,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvE5B,OAAA;kBAAGuB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACpDlB,OAAO,CAACmD;gBAAW;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN5B,OAAA;gBAAKuB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BxB,OAAA;kBAAIuB,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE5B,OAAA;kBAAIuB,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAC/ClB,OAAO,CAACoD,QAAQ,CAACL,GAAG,CAAC,CAACM,OAAO,EAAEJ,KAAK,kBACnCvD,OAAA;oBAAgBuB,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC1CxB,OAAA;sBAAK6C,KAAK,EAAC,4BAA4B;sBAACtB,SAAS,EAAC,kDAAkD;sBAACuB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAxB,QAAA,eACxJxB,OAAA;wBAAMiD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC,EACL+B,OAAO;kBAAA,GAJDJ,KAAK;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGN5B,OAAA;gBAAKuB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eACtCxB,OAAA;kBACE0C,OAAO,EAAEpB,WAAY;kBACrBC,SAAS,EAAC,oLAAoL;kBAAAC,QAAA,gBAE9LxB,OAAA;oBAAK6C,KAAK,EAAC,4BAA4B;oBAACtB,SAAS,EAAC,mEAAmE;oBAACuB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAxB,QAAA,eACzKxB,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA2G;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChL,CAAC,oBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5B,OAAA;cAAKuB,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAC5FxB,OAAA;gBAAIuB,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,gBACtExB,OAAA;kBAAK6C,KAAK,EAAC,4BAA4B;kBAACtB,SAAS,EAAC,6BAA6B;kBAACuB,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAxB,QAAA,eACnIxB,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAiI;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtM,CAAC,kBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBoC,MAAM,CAACC,OAAO,CAACvD,OAAO,CAACwD,cAAc,CAAC,CAACT,GAAG,CAAC,CAAC,CAACU,GAAG,EAAEC,KAAK,CAAC,EAAET,KAAK,kBAC9DvD,OAAA;kBAAiBuB,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBAC7ExB,OAAA;oBAAMuB,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAEuC;kBAAG;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtD5B,OAAA;oBAAMuB,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAEwC;kBAAK;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAF7D2B,KAAK;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV5B,OAAA,CAACH,MAAM;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA9RID,cAAc;EAAA,QACOP,SAAS,EACjBC,WAAW;AAAA;AAAAsE,EAAA,GAFxBhE,cAAc;AAgSpB,eAAeA,cAAc;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}