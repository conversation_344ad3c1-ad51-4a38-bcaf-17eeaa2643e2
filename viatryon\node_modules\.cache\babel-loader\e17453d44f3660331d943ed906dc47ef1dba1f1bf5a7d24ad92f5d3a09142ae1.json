{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code, X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity, Calendar, Target, Zap, MapPin, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  var _uniqueUsersData$summ2, _uniqueUsersData$summ3, _selectedClientForDet, _clientAnalytics$time, _clientAnalytics$time2, _clientAnalytics$time3, _clientAnalytics$time4, _clientAnalytics$time5, _clientAnalytics$time6, _clientAnalytics$prod, _clientAnalytics$devi, _clientAnalytics$time7, _clientAnalytics$time8;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [codeOptions, setCodeOptions] = useState({\n    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',\n    productSize: '42',\n    productType: 'watches',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    buttonText: 'Try On Virtually'\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n  const fetchClients = async () => {\n    try {\n      var _clientsData$stats, _clientsData$stats2, _clientsData$stats3;\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([fetch(`${apiUrl}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        var _uniqueUsersData$summ;\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = ((_uniqueUsersData$summ = uniqueUsersData.summary) === null || _uniqueUsersData$summ === void 0 ? void 0 : _uniqueUsersData$summ.totalUniqueUsers) || 0;\n      }\n      setStats({\n        newClientsThisMonth: ((_clientsData$stats = clientsData.stats) === null || _clientsData$stats === void 0 ? void 0 : _clientsData$stats.newClientsThisMonth) || 0,\n        activeRate: ((_clientsData$stats2 = clientsData.stats) === null || _clientsData$stats2 === void 0 ? void 0 : _clientsData$stats2.activeRate) || 0,\n        tryOnsGrowth: ((_clientsData$stats3 = clientsData.stats) === null || _clientsData$stats3 === void 0 ? void 0 : _clientsData$stats3.tryOnsGrowth) || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = date => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n  const handleAddClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteClient = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openEditModal = client => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '',\n      // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async clientId => {\n    try {\n      var _aggregatedData, _clientSpecificData, _clientSpecificData$a, _aggregatedData2, _aggregatedData3, _aggregatedData4, _clientSpecificData2, _clientSpecificData2$, _aggregatedData5, _aggregatedData6, _clientSpecificData3, _clientSpecificData3$, _aggregatedData7, _aggregatedData8;\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n\n      // Use admin endpoints to get specific client data and admin clients endpoint for aggregated data\n      const [clientDetailResponse, adminClientsResponse] = await Promise.all([fetch(`${apiUrl}/api/clients/${clientId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      let clientSpecificData = null;\n      let aggregatedData = null;\n      if (clientDetailResponse.ok) {\n        clientSpecificData = await clientDetailResponse.json();\n      }\n      if (adminClientsResponse.ok) {\n        const allClientsData = await adminClientsResponse.json();\n        // Find the specific client in the aggregated data\n        aggregatedData = allClientsData.find(client => client.clientId === clientId || client._id === clientId || client.clientName === (selectedClientForDetails === null || selectedClientForDetails === void 0 ? void 0 : selectedClientForDetails.companyName) || client.email === (selectedClientForDetails === null || selectedClientForDetails === void 0 ? void 0 : selectedClientForDetails.email));\n      }\n\n      // Combine the data sources\n      const combinedAnalytics = {\n        clientDetail: clientSpecificData,\n        aggregatedData: aggregatedData,\n        totalSessions: ((_aggregatedData = aggregatedData) === null || _aggregatedData === void 0 ? void 0 : _aggregatedData.sessions) || ((_clientSpecificData = clientSpecificData) === null || _clientSpecificData === void 0 ? void 0 : (_clientSpecificData$a = _clientSpecificData.analytics) === null || _clientSpecificData$a === void 0 ? void 0 : _clientSpecificData$a.totalSessions) || 0,\n        avgDuration: ((_aggregatedData2 = aggregatedData) === null || _aggregatedData2 === void 0 ? void 0 : _aggregatedData2.avgDuration) || 0,\n        totalInteractions: ((_aggregatedData3 = aggregatedData) === null || _aggregatedData3 === void 0 ? void 0 : _aggregatedData3.totalInteractions) || 0,\n        conversionRate: ((_aggregatedData4 = aggregatedData) === null || _aggregatedData4 === void 0 ? void 0 : _aggregatedData4.conversionRate) || ((_clientSpecificData2 = clientSpecificData) === null || _clientSpecificData2 === void 0 ? void 0 : (_clientSpecificData2$ = _clientSpecificData2.analytics) === null || _clientSpecificData2$ === void 0 ? void 0 : _clientSpecificData2$.conversionRate) || 0,\n        uniqueUsers: ((_aggregatedData5 = aggregatedData) === null || _aggregatedData5 === void 0 ? void 0 : _aggregatedData5.uniqueUsers) || 0,\n        lastActive: ((_aggregatedData6 = aggregatedData) === null || _aggregatedData6 === void 0 ? void 0 : _aggregatedData6.lastActive) || ((_clientSpecificData3 = clientSpecificData) === null || _clientSpecificData3 === void 0 ? void 0 : (_clientSpecificData3$ = _clientSpecificData3.analytics) === null || _clientSpecificData3$ === void 0 ? void 0 : _clientSpecificData3$.lastActive),\n        deviceBreakdown: ((_aggregatedData7 = aggregatedData) === null || _aggregatedData7 === void 0 ? void 0 : _aggregatedData7.deviceBreakdown) || [],\n        recentSessions: ((_aggregatedData8 = aggregatedData) === null || _aggregatedData8 === void 0 ? void 0 : _aggregatedData8.recentSessions) || []\n      };\n      setClientAnalytics(combinedAnalytics);\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = client => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = client => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = client => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '42', productType = 'watches') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('YOUR_PRODUCT_IMAGE_URL', '42', 'watches')\"\n  style=\"\n    background-color: #2D8C88;\n    color: white;\n    border: none;\n    padding: 12px 24px;\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='#236b68'\"\n  onmouseout=\"this.style.backgroundColor='#2D8C88'\"\n>\n  Try On Virtually\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: openAddModal,\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [\"+\", stats.newClientsThisMonth, \" new\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [stats.activeRate.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics;\n                    return sum + (((_c$analytics = c.analytics) === null || _c$analytics === void 0 ? void 0 : _c$analytics.totalSessions) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.tryOnsGrowth >= 0 ? '+' : '', stats.tryOnsGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users (by IP)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : stats.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Activity, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-blue-600\",\n                children: [(uniqueUsersData === null || uniqueUsersData === void 0 ? void 0 : (_uniqueUsersData$summ2 = uniqueUsersData.summary) === null || _uniqueUsersData$summ2 === void 0 ? void 0 : (_uniqueUsersData$summ3 = _uniqueUsersData$summ2.avgSessionsPerUser) === null || _uniqueUsersData$summ3 === void 0 ? void 0 : _uniqueUsersData$summ3.toFixed(1)) || '0', \" avg sessions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"per user\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showModal && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: editingClient ? 'Edit Client' : 'Add New Client'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowModal(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: editingClient ? handleEditClient : handleAddClient,\n                  className: \"space-y-5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Company Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"companyName\",\n                        value: clientForm.companyName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter company name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Contact Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 612,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"contactName\",\n                        value: clientForm.contactName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter contact name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"email\",\n                        name: \"email\",\n                        value: clientForm.email,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter email address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Phone\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        name: \"phone\",\n                        value: clientForm.phone,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter phone number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Website\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"url\",\n                        name: \"website\",\n                        value: clientForm.website,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"https://example.com\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 654,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Industry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 664,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"industry\",\n                        value: clientForm.industry,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"e.g., Fashion, Jewelry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Product Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"productType\",\n                        value: clientForm.productType,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"watches\",\n                          children: \"Watches\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 685,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"bracelets\",\n                          children: \"Bracelets\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 686,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"both\",\n                          children: \"Both\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 687,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Subscription Plan\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"subscriptionPlan\",\n                        value: clientForm.subscriptionPlan,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"basic\",\n                          children: \"Basic\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 698,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"premium\",\n                          children: \"Premium\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 699,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"enterprise\",\n                          children: \"Enterprise\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 700,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 23\n                  }, this), !editingClient && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"password\",\n                        value: clientForm.password,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: handleSuggestPassword,\n                        className: \"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\",\n                        children: \"Generate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 718,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-end space-x-3 pt-4 border-t border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowModal(false),\n                      className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"submit\",\n                      className: \"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\",\n                      children: editingClient ? 'Update Client' : 'Create Client'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Conversion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 796,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 21\n                }, this) : error ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-red-600\",\n                    children: [\"Error loading clients: \", error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this) : clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-gray-500\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this) : clients.map(client => {\n                  var _client$companyName, _client$analytics, _client$analytics$tot, _client$analytics2, _client$analytics3, _client$analytics3$to, _client$analytics4, _client$analytics5, _client$analytics6, _client$analytics7;\n                  return /*#__PURE__*/_jsxDEV(motion.tr, {\n                    initial: {\n                      opacity: 0\n                    },\n                    animate: {\n                      opacity: 1\n                    },\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 h-10 w-10\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                            children: ((_client$companyName = client.companyName) === null || _client$companyName === void 0 ? void 0 : _client$companyName.charAt(0)) || 'C'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 822,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"ml-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: client.companyName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 827,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: client.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500 lg:hidden\",\n                            children: [((_client$analytics = client.analytics) === null || _client$analytics === void 0 ? void 0 : (_client$analytics$tot = _client$analytics.totalSessions) === null || _client$analytics$tot === void 0 ? void 0 : _client$analytics$tot.toLocaleString()) || '0', \" try-ons \\u2022 \", ((_client$analytics2 = client.analytics) === null || _client$analytics2 === void 0 ? void 0 : _client$analytics2.uniqueUsers) || '0', \" unique users\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 829,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 826,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 820,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 819,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: ((_client$analytics3 = client.analytics) === null || _client$analytics3 === void 0 ? void 0 : (_client$analytics3$to = _client$analytics3.totalSessions) === null || _client$analytics3$to === void 0 ? void 0 : _client$analytics3$to.toLocaleString()) || '0'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 836,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics4 = client.analytics) === null || _client$analytics4 === void 0 ? void 0 : _client$analytics4.productCount) || '0', \" products\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [((_client$analytics5 = client.analytics) === null || _client$analytics5 === void 0 ? void 0 : _client$analytics5.conversionRate) || '0', \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 840,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics6 = client.analytics) === null || _client$analytics6 === void 0 ? void 0 : _client$analytics6.uniqueUsers) || '0', \" unique users\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 841,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                          children: client.subscriptionStatus\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 845,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatLastActive((_client$analytics7 = client.analytics) === null || _client$analytics7 === void 0 ? void 0 : _client$analytics7.lastActive)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 844,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 843,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' : client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n                        children: client.subscriptionPlan\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 856,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\",\n                          onClick: () => handleViewDetails(client),\n                          title: \"View Details\",\n                          children: /*#__PURE__*/_jsxDEV(Eye, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 870,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 865,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\",\n                          onClick: () => handleViewCode(client),\n                          title: \"Integration Code\",\n                          children: /*#__PURE__*/_jsxDEV(Code, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 877,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 872,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\",\n                          onClick: () => openEditModal(client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 884,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors\",\n                          onClick: () => handleDeleteClient(client._id),\n                          title: \"Delete Client\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 891,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 886,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 864,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 863,\n                      columnNumber: 25\n                    }, this)]\n                  }, client._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showDetailsPopup && selectedClientForDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold\",\n                      children: ((_selectedClientForDet = selectedClientForDetails.companyName) === null || _selectedClientForDet === void 0 ? void 0 : _selectedClientForDet.charAt(0)) || 'C'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: selectedClientForDetails.companyName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 926,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/80 text-sm\",\n                        children: selectedClientForDetails.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 929,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowDetailsPopup(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\",\n                children: loadingAnalytics ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center py-12\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Users, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 956,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 955,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-blue-600\",\n                            children: \"Total Sessions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 959,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-blue-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time === void 0 ? void 0 : (_clientAnalytics$time2 = _clientAnalytics$time.dailyTrends) === null || _clientAnalytics$time2 === void 0 ? void 0 : _clientAnalytics$time2.reduce((sum, day) => sum + (day.sessions || 0), 0)) || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 960,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 958,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 954,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Clock, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 970,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 969,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-green-600\",\n                            children: \"Avg Duration\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 973,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-green-900\",\n                            children: [Math.round((clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time3 = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time3 === void 0 ? void 0 : (_clientAnalytics$time4 = _clientAnalytics$time3.dailyTrends) === null || _clientAnalytics$time4 === void 0 ? void 0 : _clientAnalytics$time4.reduce((sum, day) => sum + (day.avgDuration || 0), 0)) / ((clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time5 = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time5 === void 0 ? void 0 : (_clientAnalytics$time6 = _clientAnalytics$time5.dailyTrends) === null || _clientAnalytics$time6 === void 0 ? void 0 : _clientAnalytics$time6.length) || 1)) || 0, \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 974,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 972,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 967,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 984,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 983,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-purple-600\",\n                            children: \"Products\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 987,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-purple-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$prod = clientAnalytics.productPerformance) === null || _clientAnalytics$prod === void 0 ? void 0 : _clientAnalytics$prod.length) || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 988,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 986,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 982,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 981,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Smartphone, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 998,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 997,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-orange-600\",\n                            children: \"Devices\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1001,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-orange-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$devi = clientAnalytics.deviceStats) === null || _clientAnalytics$devi === void 0 ? void 0 : _clientAnalytics$devi.length) || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1002,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1000,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 996,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Activity, {\n                        className: \"h-5 w-5 mr-2 text-[#2D8C88]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1013,\n                        columnNumber: 29\n                      }, this), \"Recent Activity\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1012,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time7 = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time7 === void 0 ? void 0 : (_clientAnalytics$time8 = _clientAnalytics$time7.dailyTrends) === null || _clientAnalytics$time8 === void 0 ? void 0 : _clientAnalytics$time8.slice(-5).map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-2 h-2 rounded-full bg-[#2D8C88]\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1020,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: new Date(day.date).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1021,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1019,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600\",\n                          children: [day.sessions, \" sessions \\u2022 \", Math.round(day.avgDuration || 0), \"s avg\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1025,\n                          columnNumber: 33\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1018,\n                        columnNumber: 31\n                      }, this))) || /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-500 text-center py-4\",\n                        children: \"No recent activity data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1030,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1016,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1011,\n                    columnNumber: 25\n                  }, this), (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.deviceStats) && clientAnalytics.deviceStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Monitor, {\n                        className: \"h-5 w-5 mr-2 text-[#2D8C88]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1039,\n                        columnNumber: 31\n                      }, this), \"Device Usage\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1038,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                      children: clientAnalytics.deviceStats.map((device, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-4\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                            children: device.device || 'Unknown'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1046,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [device.sessions, \" sessions\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1049,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1045,\n                          columnNumber: 35\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1044,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1042,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showCodePopup && selectedClientForCode && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Code, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1086,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1085,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Integration Code\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1089,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/80 text-sm\",\n                        children: selectedClientForCode.companyName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1092,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1088,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1084,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowCodePopup(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1101,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-blue-900 mb-2\",\n                      children: \"How to integrate ViatrOn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1111,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2 text-sm text-blue-800\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"1. Copy the code below and paste it into your website's HTML\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1116,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"3. Customize the button styling to match your brand\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1117,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"4. Test the integration to ensure it works correctly\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1118,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1114,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1110,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Integration Code\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1125,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: copyCodeToClipboard,\n                        className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\",\n                        children: copiedCode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Check, {\n                            className: \"h-4 w-4 mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1134,\n                            columnNumber: 33\n                          }, this), \"Copied!\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Copy, {\n                            className: \"h-4 w-4 mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1139,\n                            columnNumber: 33\n                          }, this), \"Copy Code\"]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1128,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1124,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gray-900 rounded-xl p-4 overflow-x-auto\",\n                      children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"text-green-400 text-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"code\", {\n                          children: generateIntegrationCode(selectedClientForCode)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1148,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1147,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1146,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1123,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-3\",\n                      children: \"Example Usage\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1155,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 border\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-700 mb-2\",\n                          children: \"For Watches:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1160,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                          className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                          children: \"openViaTryon('https://example.com/watch.jpg', '42', 'watches')\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1161,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1159,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 border\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-700 mb-2\",\n                          children: \"For Bracelets:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1166,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                          className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                          children: \"openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1167,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1165,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1158,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1154,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-yellow-900 mb-2\",\n                      children: \"Need Help?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1176,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-yellow-800\",\n                      children: \"If you need assistance with the integration, please contact our support team or refer to our documentation.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1179,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1175,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1067,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 453,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"GA9SqiyWeMa5eupQPgRf5n403oE=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "AnimatePresence", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "X", "Copy", "Check", "BarChart3", "Clock", "Smartphone", "Monitor", "Activity", "Calendar", "Target", "Zap", "MapPin", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "_uniqueUsersData$summ2", "_uniqueUsersData$summ3", "_selectedClientForDet", "_clientAnalytics$time", "_clientAnalytics$time2", "_clientAnalytics$time3", "_clientAnalytics$time4", "_clientAnalytics$time5", "_clientAnalytics$time6", "_clientAnalytics$prod", "_clientAnalytics$devi", "_clientAnalytics$time7", "_clientAnalytics$time8", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "editingClient", "setEditingClient", "clients", "setClients", "loading", "setLoading", "error", "setError", "stats", "setStats", "newClientsThisMonth", "activeRate", "tryOnsGrowth", "uniqueUsers", "uniqueUsersData", "setUniqueUsersData", "showDetailsPopup", "setShowDetailsPopup", "showCodePopup", "setShowCodePopup", "selectedClientForDetails", "setSelectedClientForDetails", "selectedClientForCode", "setSelectedClientForCode", "clientAnalytics", "setClientAnalytics", "loadingAnalytics", "setLoadingAnalytics", "copiedCode", "setCopiedCode", "codeOptions", "setCodeOptions", "productImageUrl", "productSize", "productType", "buttonStyle", "buttonSize", "buttonText", "clientForm", "setClientForm", "companyName", "contactName", "website", "email", "phone", "industry", "subscriptionPlan", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "fetchClients", "_clientsData$stats", "_clientsData$stats2", "_clientsData$stats3", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "params", "URLSearchParams", "append", "clientsResponse", "uniqueUsersResponse", "Promise", "all", "fetch", "headers", "ok", "errorData", "json", "message", "clientsData", "uniqueUsersCount", "_uniqueUsersData$summ", "summary", "totalUniqueUsers", "err", "console", "formatLastActive", "date", "now", "Date", "lastActive", "diffInHours", "diffInDays", "diffInWeeks", "handleFormChange", "e", "name", "value", "target", "prev", "handleSuggestPassword", "resetForm", "handleAddClient", "preventDefault", "response", "method", "body", "JSON", "stringify", "handleEditClient", "_id", "handleDeleteClient", "clientId", "window", "confirm", "openEditModal", "client", "openAddModal", "fetchClientAnalytics", "_aggregatedData", "_clientSpecificData", "_clientSpecificData$a", "_aggregatedData2", "_aggregatedData3", "_aggregatedData4", "_clientSpecificData2", "_clientSpecificData2$", "_aggregatedData5", "_aggregatedData6", "_clientSpecificData3", "_clientSpecificData3$", "_aggregatedData7", "_aggregatedData8", "end", "start", "setDate", "getDate", "clientDetailResponse", "adminClientsResponse", "toISOString", "clientSpecificData", "aggregatedData", "allClientsData", "find", "clientName", "combinedAnalytics", "clientDetail", "totalSessions", "sessions", "analytics", "avgDuration", "totalInteractions", "conversionRate", "deviceBreakdown", "recentSessions", "handleViewDetails", "handleViewCode", "generateIntegrationCode", "REACT_APP_FRONTEND_URL", "location", "origin", "copyCodeToClipboard", "code", "navigator", "clipboard", "writeText", "then", "setTimeout", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "filter", "c", "subscriptionStatus", "toFixed", "reduce", "sum", "_c$analytics", "toLocaleString", "avgSessionsPerUser", "exit", "scale", "onSubmit", "type", "onChange", "required", "placeholder", "colSpan", "map", "_client$companyName", "_client$analytics", "_client$analytics$tot", "_client$analytics2", "_client$analytics3", "_client$analytics3$to", "_client$analytics4", "_client$analytics5", "_client$analytics6", "_client$analytics7", "tr", "productCount", "title", "timeAnalysis", "dailyTrends", "day", "round", "productPerformance", "deviceStats", "index", "toLocaleDateString", "device", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code,\n  X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity,\n  Calendar, Target, Zap, MapPin, ChevronRight\n} from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [codeOptions, setCodeOptions] = useState({\n    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',\n    productSize: '42',\n    productType: 'watches',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    buttonText: 'Try On Virtually'\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients?${params}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = uniqueUsersData.summary?.totalUniqueUsers || 0;\n      }\n\n      setStats({\n        newClientsThisMonth: clientsData.stats?.newClientsThisMonth || 0,\n        activeRate: clientsData.stats?.activeRate || 0,\n        tryOnsGrowth: clientsData.stats?.tryOnsGrowth || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = (date) => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n\n  const handleAddClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEditClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClient = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const openEditModal = (client) => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '', // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async (clientId) => {\n    try {\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n\n      // Use admin endpoints to get specific client data and admin clients endpoint for aggregated data\n      const [clientDetailResponse, adminClientsResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients/${clientId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      let clientSpecificData = null;\n      let aggregatedData = null;\n\n      if (clientDetailResponse.ok) {\n        clientSpecificData = await clientDetailResponse.json();\n      }\n\n      if (adminClientsResponse.ok) {\n        const allClientsData = await adminClientsResponse.json();\n        // Find the specific client in the aggregated data\n        aggregatedData = allClientsData.find(client =>\n          client.clientId === clientId ||\n          client._id === clientId ||\n          client.clientName === selectedClientForDetails?.companyName ||\n          client.email === selectedClientForDetails?.email\n        );\n      }\n\n      // Combine the data sources\n      const combinedAnalytics = {\n        clientDetail: clientSpecificData,\n        aggregatedData: aggregatedData,\n        totalSessions: aggregatedData?.sessions || clientSpecificData?.analytics?.totalSessions || 0,\n        avgDuration: aggregatedData?.avgDuration || 0,\n        totalInteractions: aggregatedData?.totalInteractions || 0,\n        conversionRate: aggregatedData?.conversionRate || clientSpecificData?.analytics?.conversionRate || 0,\n        uniqueUsers: aggregatedData?.uniqueUsers || 0,\n        lastActive: aggregatedData?.lastActive || clientSpecificData?.analytics?.lastActive,\n        deviceBreakdown: aggregatedData?.deviceBreakdown || [],\n        recentSessions: aggregatedData?.recentSessions || []\n      };\n\n      setClientAnalytics(combinedAnalytics);\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = (client) => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = (client) => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = (client) => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '42', productType = 'watches') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('YOUR_PRODUCT_IMAGE_URL', '42', 'watches')\"\n  style=\"\n    background-color: #2D8C88;\n    color: white;\n    border: none;\n    padding: 12px 24px;\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='#236b68'\"\n  onmouseout=\"this.style.backgroundColor='#2D8C88'\"\n>\n  Try On Virtually\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={openAddModal}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+{stats.newClientsThisMonth} new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">{stats.activeRate.toFixed(1)}%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users (by IP)</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : stats.uniqueUsers.toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\n                  <Activity className=\"h-6 w-6 text-purple-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-blue-600\">\n                  {uniqueUsersData?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} avg sessions\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">per user</span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add/Edit Client Modal */}\n          <AnimatePresence>\n            {showModal && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <h2 className=\"text-xl font-bold text-white\">\n                        {editingClient ? 'Edit Client' : 'Add New Client'}\n                      </h2>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowModal(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Form */}\n                  <div className=\"p-6\">\n                    <form onSubmit={editingClient ? handleEditClient : handleAddClient} className=\"space-y-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Company Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"companyName\"\n                            value={clientForm.companyName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter company name\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Contact Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"contactName\"\n                            value={clientForm.contactName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter contact name\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Email</label>\n                          <input\n                            type=\"email\"\n                            name=\"email\"\n                            value={clientForm.email}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter email address\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Phone</label>\n                          <input\n                            type=\"tel\"\n                            name=\"phone\"\n                            value={clientForm.phone}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter phone number\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Website</label>\n                          <input\n                            type=\"url\"\n                            name=\"website\"\n                            value={clientForm.website}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"https://example.com\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Industry</label>\n                          <input\n                            type=\"text\"\n                            name=\"industry\"\n                            value={clientForm.industry}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"e.g., Fashion, Jewelry\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Product Type</label>\n                          <select\n                            name=\"productType\"\n                            value={clientForm.productType}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"watches\">Watches</option>\n                            <option value=\"bracelets\">Bracelets</option>\n                            <option value=\"both\">Both</option>\n                          </select>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Subscription Plan</label>\n                          <select\n                            name=\"subscriptionPlan\"\n                            value={clientForm.subscriptionPlan}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"basic\">Basic</option>\n                            <option value=\"premium\">Premium</option>\n                            <option value=\"enterprise\">Enterprise</option>\n                          </select>\n                        </div>\n                      </div>\n\n                      {!editingClient && (\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Password</label>\n                          <div className=\"flex gap-3\">\n                            <input\n                              type=\"text\"\n                              name=\"password\"\n                              value={clientForm.password}\n                              onChange={handleFormChange}\n                              required\n                              className=\"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                              placeholder=\"Enter password\"\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={handleSuggestPassword}\n                              className=\"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\"\n                            >\n                              Generate\n                            </button>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Action Buttons */}\n                      <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-100\">\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowModal(false)}\n                          className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                        >\n                          Cancel\n                        </button>\n                        <button\n                          type=\"submit\"\n                          className=\"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\"\n                        >\n                          {editingClient ? 'Update Client' : 'Create Client'}\n                        </button>\n                      </div>\n                    </form>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Conversion</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {loading ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center\">\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"></div>\n                      </td>\n                    </tr>\n                  ) : error ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-red-600\">\n                        Error loading clients: {error}\n                      </td>\n                    </tr>\n                  ) : clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-gray-500\">\n                        No clients found\n                      </td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <motion.tr\n                        key={client._id}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"hover:bg-gray-50\"\n                      >\n                        <td className=\"px-4 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                                {client.companyName?.charAt(0) || 'C'}\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{client.companyName}</div>\n                              <div className=\"text-sm text-gray-500\">{client.email}</div>\n                              <div className=\"text-sm text-gray-500 lg:hidden\">\n                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.uniqueUsers || '0'} unique users\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.productCount || '0'} products</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.conversionRate || '0'}%</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.uniqueUsers || '0'} unique users</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                              'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {client.subscriptionStatus}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{formatLastActive(client.analytics?.lastActive)}</span>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {client.subscriptionPlan}\n                          </span>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex justify-end space-x-2\">\n                            <button\n                              className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\"\n                              onClick={() => handleViewDetails(client)}\n                              title=\"View Details\"\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\"\n                              onClick={() => handleViewCode(client)}\n                              title=\"Integration Code\"\n                            >\n                              <Code className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\"\n                              onClick={() => openEditModal(client)}\n                              title=\"Edit Client\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors\"\n                              onClick={() => handleDeleteClient(client._id)}\n                              title=\"Delete Client\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* View Details Popup */}\n          <AnimatePresence>\n            {showDetailsPopup && selectedClientForDetails && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold\">\n                          {selectedClientForDetails.companyName?.charAt(0) || 'C'}\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl font-bold text-white\">\n                            {selectedClientForDetails.companyName}\n                          </h2>\n                          <p className=\"text-white/80 text-sm\">\n                            {selectedClientForDetails.email}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowDetailsPopup(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\">\n                    {loadingAnalytics ? (\n                      <div className=\"flex items-center justify-center py-12\">\n                        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\n                      </div>\n                    ) : (\n                      <div className=\"space-y-6\">\n                        {/* Client Info */}\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                          <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center\">\n                                <Users className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-blue-600\">Total Sessions</p>\n                                <p className=\"text-xl font-bold text-blue-900\">\n                                  {clientAnalytics?.timeAnalysis?.dailyTrends?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center\">\n                                <Clock className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-green-600\">Avg Duration</p>\n                                <p className=\"text-xl font-bold text-green-900\">\n                                  {Math.round(clientAnalytics?.timeAnalysis?.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / (clientAnalytics?.timeAnalysis?.dailyTrends?.length || 1)) || 0}s\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center\">\n                                <BarChart3 className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-purple-600\">Products</p>\n                                <p className=\"text-xl font-bold text-purple-900\">\n                                  {clientAnalytics?.productPerformance?.length || 0}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center\">\n                                <Smartphone className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-orange-600\">Devices</p>\n                                <p className=\"text-xl font-bold text-orange-900\">\n                                  {clientAnalytics?.deviceStats?.length || 0}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Recent Activity */}\n                        <div className=\"bg-gray-50 rounded-xl p-6\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                            <Activity className=\"h-5 w-5 mr-2 text-[#2D8C88]\" />\n                            Recent Activity\n                          </h3>\n                          <div className=\"space-y-3\">\n                            {clientAnalytics?.timeAnalysis?.dailyTrends?.slice(-5).map((day, index) => (\n                              <div key={index} className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <div className=\"flex items-center space-x-3\">\n                                  <div className=\"w-2 h-2 rounded-full bg-[#2D8C88]\"></div>\n                                  <span className=\"text-sm font-medium text-gray-900\">\n                                    {new Date(day.date).toLocaleDateString()}\n                                  </span>\n                                </div>\n                                <div className=\"text-sm text-gray-600\">\n                                  {day.sessions} sessions • {Math.round(day.avgDuration || 0)}s avg\n                                </div>\n                              </div>\n                            )) || (\n                              <p className=\"text-gray-500 text-center py-4\">No recent activity data available</p>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Device Breakdown */}\n                        {clientAnalytics?.deviceStats && clientAnalytics.deviceStats.length > 0 && (\n                          <div className=\"bg-gray-50 rounded-xl p-6\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                              <Monitor className=\"h-5 w-5 mr-2 text-[#2D8C88]\" />\n                              Device Usage\n                            </h3>\n                            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                              {clientAnalytics.deviceStats.map((device, index) => (\n                                <div key={index} className=\"bg-white rounded-lg p-4\">\n                                  <div className=\"flex items-center justify-between\">\n                                    <span className=\"text-sm font-medium text-gray-900 capitalize\">\n                                      {device.device || 'Unknown'}\n                                    </span>\n                                    <span className=\"text-sm text-gray-600\">\n                                      {device.sessions} sessions\n                                    </span>\n                                  </div>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* View Code Popup */}\n          <AnimatePresence>\n            {showCodePopup && selectedClientForCode && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center\">\n                          <Code className=\"h-5 w-5 text-white\" />\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl font-bold text-white\">\n                            Integration Code\n                          </h2>\n                          <p className=\"text-white/80 text-sm\">\n                            {selectedClientForCode.companyName}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowCodePopup(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <div className=\"space-y-6\">\n                      {/* Instructions */}\n                      <div className=\"bg-blue-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">\n                          How to integrate ViatrOn\n                        </h3>\n                        <div className=\"space-y-2 text-sm text-blue-800\">\n                          <p>1. Copy the code below and paste it into your website's HTML</p>\n                          <p>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs</p>\n                          <p>3. Customize the button styling to match your brand</p>\n                          <p>4. Test the integration to ensure it works correctly</p>\n                        </div>\n                      </div>\n\n                      {/* Code Block */}\n                      <div className=\"relative\">\n                        <div className=\"flex items-center justify-between mb-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">\n                            Integration Code\n                          </h3>\n                          <button\n                            onClick={copyCodeToClipboard}\n                            className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\"\n                          >\n                            {copiedCode ? (\n                              <>\n                                <Check className=\"h-4 w-4 mr-2\" />\n                                Copied!\n                              </>\n                            ) : (\n                              <>\n                                <Copy className=\"h-4 w-4 mr-2\" />\n                                Copy Code\n                              </>\n                            )}\n                          </button>\n                        </div>\n\n                        <div className=\"bg-gray-900 rounded-xl p-4 overflow-x-auto\">\n                          <pre className=\"text-green-400 text-sm\">\n                            <code>{generateIntegrationCode(selectedClientForCode)}</code>\n                          </pre>\n                        </div>\n                      </div>\n\n                      {/* Example Usage */}\n                      <div className=\"bg-gray-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                          Example Usage\n                        </h3>\n                        <div className=\"space-y-3\">\n                          <div className=\"bg-white rounded-lg p-3 border\">\n                            <p className=\"text-sm font-medium text-gray-700 mb-2\">For Watches:</p>\n                            <code className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                              openViaTryon('https://example.com/watch.jpg', '42', 'watches')\n                            </code>\n                          </div>\n                          <div className=\"bg-white rounded-lg p-3 border\">\n                            <p className=\"text-sm font-medium text-gray-700 mb-2\">For Bracelets:</p>\n                            <code className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                              openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')\n                            </code>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Support Info */}\n                      <div className=\"bg-yellow-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-yellow-900 mb-2\">\n                          Need Help?\n                        </h3>\n                        <p className=\"text-sm text-yellow-800\">\n                          If you need assistance with the integration, please contact our support team or refer to our documentation.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAC/DC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAC/DC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QACtC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC;IACjC4E,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoF,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC9E,MAAM,CAACwF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8F,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgG,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC;IAC7CkG,eAAe,EAAE,wBAAwB;IACzCC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzG,QAAQ,CAAC;IAC3C0G,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACT3E,QAAQ,EAAE,EAAE;IACZ4E,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZX,WAAW,EAAE,SAAS;IACtBY,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxD,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0D,UAAU,GAAGxD,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACAzD,SAAS,CAAC,MAAM;IACdkH,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACvD,WAAW,EAAEE,cAAc,CAAC,CAAC;EAEjC,MAAMqD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMO,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIvE,WAAW,EAAEsE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAExE,WAAW,CAAC;MACrD,IAAIE,cAAc,KAAK,KAAK,EAAEoE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEtE,cAAc,CAAC;;MAErE;MACA,MAAM,CAACuE,eAAe,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DC,KAAK,CAAC,GAAGV,MAAM,gBAAgBG,MAAM,EAAE,EAAE;QACvCQ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,mCAAmC,EAAE;QAClDW,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,IAAI,CAACc,eAAe,CAACM,EAAE,EAAE;QACvB,MAAMC,SAAS,GAAG,MAAMP,eAAe,CAACQ,IAAI,CAAC,CAAC;QAC9C,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMC,WAAW,GAAG,MAAMV,eAAe,CAACQ,IAAI,CAAC,CAAC;MAChDxE,UAAU,CAAC0E,WAAW,CAAC3E,OAAO,IAAI,EAAE,CAAC;;MAErC;MACA,IAAI4E,gBAAgB,GAAG,CAAC;MACxB,IAAIV,mBAAmB,CAACK,EAAE,EAAE;QAAA,IAAAM,qBAAA;QAC1B,MAAMjE,eAAe,GAAG,MAAMsD,mBAAmB,CAACO,IAAI,CAAC,CAAC;QACxD5D,kBAAkB,CAACD,eAAe,CAAC;QACnCgE,gBAAgB,GAAG,EAAAC,qBAAA,GAAAjE,eAAe,CAACkE,OAAO,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBE,gBAAgB,KAAI,CAAC;MACnE;MAEAxE,QAAQ,CAAC;QACPC,mBAAmB,EAAE,EAAAwC,kBAAA,GAAA2B,WAAW,CAACrE,KAAK,cAAA0C,kBAAA,uBAAjBA,kBAAA,CAAmBxC,mBAAmB,KAAI,CAAC;QAChEC,UAAU,EAAE,EAAAwC,mBAAA,GAAA0B,WAAW,CAACrE,KAAK,cAAA2C,mBAAA,uBAAjBA,mBAAA,CAAmBxC,UAAU,KAAI,CAAC;QAC9CC,YAAY,EAAE,EAAAwC,mBAAA,GAAAyB,WAAW,CAACrE,KAAK,cAAA4C,mBAAA,uBAAjBA,mBAAA,CAAmBxC,YAAY,KAAI,CAAC;QAClDC,WAAW,EAAEiE;MACf,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,yBAAyB,EAAE4E,GAAG,CAAC;MAC7C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;MACrBzE,UAAU,CAAC,EAAE,CAAC;MACdM,QAAQ,CAAC;QACPC,mBAAmB,EAAE,CAAC;QACtBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IACjC,MAAMI,WAAW,GAAGtH,IAAI,CAACC,KAAK,CAAC,CAACkH,GAAG,GAAGE,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErE,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMC,UAAU,GAAGvH,IAAI,CAACC,KAAK,CAACqH,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,WAAW;IACnD,MAAMC,WAAW,GAAGxH,IAAI,CAACC,KAAK,CAACsH,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,YAAY;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzD,aAAa,CAAC0D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC3D,aAAa,CAAC0D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjI,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAMqI,SAAS,GAAGA,CAAA,KAAM;IACtB5D,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACT3E,QAAQ,EAAE,EAAE;MACZ4E,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,SAAS;MACtBY,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACF7C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmG,eAAe,GAAG,MAAOP,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,cAAc,EAAE;QACpD0C,MAAM,EAAE,MAAM;QACd/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACgE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBlD,YAAY,CAAC,KAAK,CAAC;MACnBoG,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAE4E,GAAG,CAAC;MAC5C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsG,gBAAgB,GAAG,MAAOd,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgB7D,aAAa,CAAC4G,GAAG,EAAE,EAAE;QACzEL,MAAM,EAAE,KAAK;QACb/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACgE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBlD,YAAY,CAAC,KAAK,CAAC;MACnBoG,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAE4E,GAAG,CAAC;MAC5C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwG,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MACnE;IACF;IAEA,IAAI;MACF3G,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgBiD,QAAQ,EAAE,EAAE;QAChEP,MAAM,EAAE,QAAQ;QAChB/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACiD,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAE4E,GAAG,CAAC;MAC5C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4G,aAAa,GAAIC,MAAM,IAAK;IAChCjH,gBAAgB,CAACiH,MAAM,CAAC;IACxB3E,aAAa,CAAC;MACZC,WAAW,EAAE0E,MAAM,CAAC1E,WAAW,IAAI,EAAE;MACrCC,WAAW,EAAEyE,MAAM,CAACzE,WAAW,IAAI,EAAE;MACrCC,OAAO,EAAEwE,MAAM,CAACxE,OAAO,IAAI,EAAE;MAC7BC,KAAK,EAAEuE,MAAM,CAACvE,KAAK,IAAI,EAAE;MACzB3E,QAAQ,EAAE,EAAE;MAAE;MACd4E,KAAK,EAAEsE,MAAM,CAACtE,KAAK,IAAI,EAAE;MACzBC,QAAQ,EAAEqE,MAAM,CAACrE,QAAQ,IAAI,EAAE;MAC/BX,WAAW,EAAEgF,MAAM,CAAChF,WAAW,IAAI,SAAS;MAC5CY,gBAAgB,EAAEoE,MAAM,CAACpE,gBAAgB,IAAI;IAC/C,CAAC,CAAC;IACF/C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoH,YAAY,GAAGA,CAAA,KAAM;IACzBhB,SAAS,CAAC,CAAC;IACXpG,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMqH,oBAAoB,GAAG,MAAON,QAAQ,IAAK;IAC/C,IAAI;MAAA,IAAAO,eAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACFvG,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAM0B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;MAErE;MACA,MAAM0E,GAAG,GAAG,IAAI5C,IAAI,CAAC,CAAC;MACtB,MAAM6C,KAAK,GAAG,IAAI7C,IAAI,CAAC,CAAC;MACxB6C,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;;MAEnC;MACA,MAAM,CAACC,oBAAoB,EAAEC,oBAAoB,CAAC,GAAG,MAAMnE,OAAO,CAACC,GAAG,CAAC,CACrEC,KAAK,CAAC,GAAGV,MAAM,gBAAgBiD,QAAQ,EAAE,EAAE;QACzCtC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,sCAAsCuE,KAAK,CAACK,WAAW,CAAC,CAAC,QAAQN,GAAG,CAACM,WAAW,CAAC,CAAC,EAAE,EAAE;QACnGjE,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,IAAIqF,kBAAkB,GAAG,IAAI;MAC7B,IAAIC,cAAc,GAAG,IAAI;MAEzB,IAAIJ,oBAAoB,CAAC9D,EAAE,EAAE;QAC3BiE,kBAAkB,GAAG,MAAMH,oBAAoB,CAAC5D,IAAI,CAAC,CAAC;MACxD;MAEA,IAAI6D,oBAAoB,CAAC/D,EAAE,EAAE;QAC3B,MAAMmE,cAAc,GAAG,MAAMJ,oBAAoB,CAAC7D,IAAI,CAAC,CAAC;QACxD;QACAgE,cAAc,GAAGC,cAAc,CAACC,IAAI,CAAC3B,MAAM,IACzCA,MAAM,CAACJ,QAAQ,KAAKA,QAAQ,IAC5BI,MAAM,CAACN,GAAG,KAAKE,QAAQ,IACvBI,MAAM,CAAC4B,UAAU,MAAK1H,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAEoB,WAAW,KAC3D0E,MAAM,CAACvE,KAAK,MAAKvB,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAEuB,KAAK,CAClD,CAAC;MACH;;MAEA;MACA,MAAMoG,iBAAiB,GAAG;QACxBC,YAAY,EAAEN,kBAAkB;QAChCC,cAAc,EAAEA,cAAc;QAC9BM,aAAa,EAAE,EAAA5B,eAAA,GAAAsB,cAAc,cAAAtB,eAAA,uBAAdA,eAAA,CAAgB6B,QAAQ,OAAA5B,mBAAA,GAAIoB,kBAAkB,cAAApB,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB6B,SAAS,cAAA5B,qBAAA,uBAA7BA,qBAAA,CAA+B0B,aAAa,KAAI,CAAC;QAC5FG,WAAW,EAAE,EAAA5B,gBAAA,GAAAmB,cAAc,cAAAnB,gBAAA,uBAAdA,gBAAA,CAAgB4B,WAAW,KAAI,CAAC;QAC7CC,iBAAiB,EAAE,EAAA5B,gBAAA,GAAAkB,cAAc,cAAAlB,gBAAA,uBAAdA,gBAAA,CAAgB4B,iBAAiB,KAAI,CAAC;QACzDC,cAAc,EAAE,EAAA5B,gBAAA,GAAAiB,cAAc,cAAAjB,gBAAA,uBAAdA,gBAAA,CAAgB4B,cAAc,OAAA3B,oBAAA,GAAIe,kBAAkB,cAAAf,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBwB,SAAS,cAAAvB,qBAAA,uBAA7BA,qBAAA,CAA+B0B,cAAc,KAAI,CAAC;QACpGzI,WAAW,EAAE,EAAAgH,gBAAA,GAAAc,cAAc,cAAAd,gBAAA,uBAAdA,gBAAA,CAAgBhH,WAAW,KAAI,CAAC;QAC7C2E,UAAU,EAAE,EAAAsC,gBAAA,GAAAa,cAAc,cAAAb,gBAAA,uBAAdA,gBAAA,CAAgBtC,UAAU,OAAAuC,oBAAA,GAAIW,kBAAkB,cAAAX,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBoB,SAAS,cAAAnB,qBAAA,uBAA7BA,qBAAA,CAA+BxC,UAAU;QACnF+D,eAAe,EAAE,EAAAtB,gBAAA,GAAAU,cAAc,cAAAV,gBAAA,uBAAdA,gBAAA,CAAgBsB,eAAe,KAAI,EAAE;QACtDC,cAAc,EAAE,EAAAtB,gBAAA,GAAAS,cAAc,cAAAT,gBAAA,uBAAdA,gBAAA,CAAgBsB,cAAc,KAAI;MACpD,CAAC;MAED/H,kBAAkB,CAACsH,iBAAiB,CAAC;IACvC,CAAC,CAAC,OAAOzI,KAAK,EAAE;MACd6E,OAAO,CAAC7E,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDmB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,SAAS;MACRE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM8H,iBAAiB,GAAIvC,MAAM,IAAK;IACpC7F,2BAA2B,CAAC6F,MAAM,CAAC;IACnCjG,mBAAmB,CAAC,IAAI,CAAC;IACzBmG,oBAAoB,CAACF,MAAM,CAACN,GAAG,CAAC;EAClC,CAAC;;EAED;EACA,MAAM8C,cAAc,GAAIxC,MAAM,IAAK;IACjC3F,wBAAwB,CAAC2F,MAAM,CAAC;IAChC/F,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMwI,uBAAuB,GAAIzC,MAAM,IAAK;IAC1C,MAAMzD,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACiG,sBAAsB,IAAI7C,MAAM,CAAC8C,QAAQ,CAACC,MAAM;IAC5E,OAAO;AACX;AACA;AACA,sBAAsBrG,OAAO;AAC7B;AACA,eAAeyD,MAAM,CAACN,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;EACR,CAAC;;EAED;EACA,MAAMmD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,IAAI,GAAGL,uBAAuB,CAACrI,qBAAqB,CAAC;IAC3D2I,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAM;MAC7CvI,aAAa,CAAC,IAAI,CAAC;MACnBwI,UAAU,CAAC,MAAMxI,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,oBACElE,OAAA;IAAK2M,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC5M,OAAA,CAAC3B,YAAY;MAACwO,MAAM,EAAElL,aAAc;MAACmL,OAAO,EAAEA,CAAA,KAAMlL,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAiL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjIlN,OAAA,CAAC1B,WAAW;MAAC8G,aAAa,EAAEA,aAAc;MAACvD,SAAS,EAAEA;IAAU;MAAAkL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnElN,OAAA;MAAM2M,SAAS,EAAE,GAAGtH,UAAU,oCAAqC;MAAAuH,QAAA,eACjE5M,OAAA;QAAK2M,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzB5M,OAAA;UAAK2M,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtF5M,OAAA;YAAA4M,QAAA,gBACE5M,OAAA;cAAI2M,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvElN,OAAA;cAAG2M,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACNlN,OAAA;YACE2M,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAE3D,YAAa;YAAAoD,QAAA,gBAEtB5M,OAAA,CAACtB,IAAI;cAACiO,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNlN,OAAA;UAAK2M,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjF5M,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5M,OAAA;cAAK2M,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5M,OAAA;gBAAA4M,QAAA,gBACE5M,OAAA;kBAAG2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClElN,OAAA;kBAAG2M,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnK,OAAO,GAAG,KAAK,GAAGF,OAAO,CAAC5B;gBAAM;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACNlN,OAAA;gBAAK2M,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrF5M,OAAA,CAAChB,KAAK;kBAAC2N,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlN,OAAA;cAAK2M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5M,OAAA;gBAAM2M,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,GAAC,EAAC/J,KAAK,CAACE,mBAAmB,EAAC,MAAI;cAAA;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5FlN,OAAA;gBAAM2M,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEblN,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5M,OAAA;cAAK2M,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5M,OAAA;gBAAA4M,QAAA,gBACE5M,OAAA;kBAAG2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnElN,OAAA;kBAAG2M,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnK,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACoL,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB,KAAK,QAAQ,CAAC,CAAClN;gBAAM;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,eACNlN,OAAA;gBAAK2M,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5M,OAAA,CAACjB,UAAU;kBAAC4N,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlN,OAAA;cAAK2M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5M,OAAA;gBAAM2M,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAE/J,KAAK,CAACG,UAAU,CAAC8K,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1FlN,OAAA;gBAAM2M,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEblN,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5M,OAAA;cAAK2M,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5M,OAAA;gBAAA4M,QAAA,gBACE5M,OAAA;kBAAG2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClElN,OAAA;kBAAG2M,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnK,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACwL,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC;oBAAA,IAAAK,YAAA;oBAAA,OAAKD,GAAG,IAAI,EAAAC,YAAA,GAAAL,CAAC,CAACpC,SAAS,cAAAyC,YAAA,uBAAXA,YAAA,CAAa3C,aAAa,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAAC4C,cAAc,CAAC;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrK,CAAC,eACNlN,OAAA;gBAAK2M,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5M,OAAA,CAACrB,GAAG;kBAACgO,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlN,OAAA;cAAK2M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5M,OAAA;gBAAM2M,SAAS,EAAE,uBAAuB9J,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAA2J,QAAA,GACnG/J,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,YAAY,CAAC6K,OAAO,CAAC,CAAC,CAAC,EAAC,GACrE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlN,OAAA;gBAAM2M,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEblN,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5M,OAAA;cAAK2M,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5M,OAAA;gBAAA4M,QAAA,gBACE5M,OAAA;kBAAG2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzElN,OAAA;kBAAG2M,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnK,OAAO,GAAG,KAAK,GAAGI,KAAK,CAACK,WAAW,CAACgL,cAAc,CAAC;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACNlN,OAAA;gBAAK2M,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF5M,OAAA,CAACP,QAAQ;kBAACkN,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlN,OAAA;cAAK2M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5M,OAAA;gBAAM2M,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChD,CAAAzJ,eAAe,aAAfA,eAAe,wBAAArC,sBAAA,GAAfqC,eAAe,CAAEkE,OAAO,cAAAvG,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BqN,kBAAkB,cAAApN,sBAAA,uBAA5CA,sBAAA,CAA8C+M,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,eACnE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlN,OAAA;gBAAM2M,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlN,OAAA,CAACxB,eAAe;UAAAoO,QAAA,EACbzK,SAAS,iBACRnC,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1F5M,OAAA,CAACzB,MAAM,CAAC6O,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,0EAA0E;cAAAC,QAAA,gBAGpF5M,OAAA;gBAAK2M,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrE5M,OAAA;kBAAK2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5M,OAAA;oBAAI2M,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EACzCvK,aAAa,GAAG,aAAa,GAAG;kBAAgB;oBAAA0K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACLlN,OAAA;oBACE2M,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM/K,YAAY,CAAC,KAAK,CAAE;oBAAAwK,QAAA,eAEnC5M,OAAA,CAACd,CAAC;sBAACyN,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlN,OAAA;gBAAK2M,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClB5M,OAAA;kBAAMsO,QAAQ,EAAEjM,aAAa,GAAG2G,gBAAgB,GAAGP,eAAgB;kBAACkE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvF5M,OAAA;oBAAK2M,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD5M,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFlN,OAAA;wBACEuO,IAAI,EAAC,MAAM;wBACXpG,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAEzD,UAAU,CAACE,WAAY;wBAC9B2J,QAAQ,EAAEvG,gBAAiB;wBAC3BwG,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNlN,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFlN,OAAA;wBACEuO,IAAI,EAAC,MAAM;wBACXpG,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAEzD,UAAU,CAACG,WAAY;wBAC9B0J,QAAQ,EAAEvG,gBAAiB;wBAC3BwG,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlN,OAAA;oBAAK2M,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD5M,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/ElN,OAAA;wBACEuO,IAAI,EAAC,OAAO;wBACZpG,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAEzD,UAAU,CAACK,KAAM;wBACxBwJ,QAAQ,EAAEvG,gBAAiB;wBAC3BwG,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAqB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNlN,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/ElN,OAAA;wBACEuO,IAAI,EAAC,KAAK;wBACVpG,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAEzD,UAAU,CAACM,KAAM;wBACxBuJ,QAAQ,EAAEvG,gBAAiB;wBAC3B0E,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlN,OAAA;oBAAK2M,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD5M,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjFlN,OAAA;wBACEuO,IAAI,EAAC,KAAK;wBACVpG,IAAI,EAAC,SAAS;wBACdC,KAAK,EAAEzD,UAAU,CAACI,OAAQ;wBAC1ByJ,QAAQ,EAAEvG,gBAAiB;wBAC3B0E,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAqB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNlN,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClFlN,OAAA;wBACEuO,IAAI,EAAC,MAAM;wBACXpG,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAEzD,UAAU,CAACO,QAAS;wBAC3BsJ,QAAQ,EAAEvG,gBAAiB;wBAC3B0E,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAwB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlN,OAAA;oBAAK2M,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD5M,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFlN,OAAA;wBACEmI,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAEzD,UAAU,CAACJ,WAAY;wBAC9BiK,QAAQ,EAAEvG,gBAAiB;wBAC3B0E,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3J5M,OAAA;0BAAQoI,KAAK,EAAC,SAAS;0BAAAwE,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxClN,OAAA;0BAAQoI,KAAK,EAAC,WAAW;0BAAAwE,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5ClN,OAAA;0BAAQoI,KAAK,EAAC,MAAM;0BAAAwE,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNlN,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAO2M,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC3FlN,OAAA;wBACEmI,IAAI,EAAC,kBAAkB;wBACvBC,KAAK,EAAEzD,UAAU,CAACQ,gBAAiB;wBACnCqJ,QAAQ,EAAEvG,gBAAiB;wBAC3B0E,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3J5M,OAAA;0BAAQoI,KAAK,EAAC,OAAO;0BAAAwE,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpClN,OAAA;0BAAQoI,KAAK,EAAC,SAAS;0BAAAwE,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxClN,OAAA;0BAAQoI,KAAK,EAAC,YAAY;0BAAAwE,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAAC7K,aAAa,iBACbrC,OAAA;oBAAA4M,QAAA,gBACE5M,OAAA;sBAAO2M,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClFlN,OAAA;sBAAK2M,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzB5M,OAAA;wBACEuO,IAAI,EAAC,MAAM;wBACXpG,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAEzD,UAAU,CAACtE,QAAS;wBAC3BmO,QAAQ,EAAEvG,gBAAiB;wBAC3BwG,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACFlN,OAAA;wBACEuO,IAAI,EAAC,QAAQ;wBACbpB,OAAO,EAAE5E,qBAAsB;wBAC/BoE,SAAS,EAAC,gGAAgG;wBAAAC,QAAA,EAC3G;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDlN,OAAA;oBAAK2M,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,gBACvE5M,OAAA;sBACEuO,IAAI,EAAC,QAAQ;sBACbpB,OAAO,EAAEA,CAAA,KAAM/K,YAAY,CAAC,KAAK,CAAE;sBACnCuK,SAAS,EAAC,0GAA0G;sBAAAC,QAAA,EACrH;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTlN,OAAA;sBACEuO,IAAI,EAAC,QAAQ;sBACb5B,SAAS,EAAC,yGAAyG;sBAAAC,QAAA,EAElHvK,aAAa,GAAG,eAAe,GAAG;oBAAe;sBAAA0K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBlN,OAAA;UAAK2M,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD5M,OAAA;YAAK2M,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C5M,OAAA;cAAK2M,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrB5M,OAAA;gBACEuO,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/BtG,KAAK,EAAErG,WAAY;gBACnByM,QAAQ,EAAGtG,CAAC,IAAKlG,cAAc,CAACkG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAChDuE,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlN,OAAA;cAAK2M,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5M,OAAA;gBACEoI,KAAK,EAAEnG,cAAe;gBACtBuM,QAAQ,EAAGtG,CAAC,IAAKhG,iBAAiB,CAACgG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACnDuE,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5I5M,OAAA;kBAAQoI,KAAK,EAAC,KAAK;kBAAAwE,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvClN,OAAA;kBAAQoI,KAAK,EAAC,QAAQ;kBAAAwE,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClN,OAAA;kBAAQoI,KAAK,EAAC,SAAS;kBAAAwE,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlN,OAAA;UAAK2M,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5D5M,OAAA;YAAK2M,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B5M,OAAA;cAAO2M,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpD5M,OAAA;gBAAO2M,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3B5M,OAAA;kBAAA4M,QAAA,gBACE5M,OAAA;oBAAI2M,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GlN,OAAA;oBAAI2M,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChIlN,OAAA;oBAAI2M,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnIlN,OAAA;oBAAI2M,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/HlN,OAAA;oBAAI2M,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpIlN,OAAA;oBAAI2M,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRlN,OAAA;gBAAO2M,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDnK,OAAO,gBACNzC,OAAA;kBAAA4M,QAAA,eACE5M,OAAA;oBAAI2O,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eAC/C5M,OAAA;sBAAK2M,SAAS,EAAC;oBAAuE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACHvK,KAAK,gBACP3C,OAAA;kBAAA4M,QAAA,eACE5M,OAAA;oBAAI2O,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,yBACtC,EAACjK,KAAK;kBAAA;oBAAAoK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH3K,OAAO,CAAC5B,MAAM,KAAK,CAAC,gBACtBX,OAAA;kBAAA4M,QAAA,eACE5M,OAAA;oBAAI2O,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAEL3K,OAAO,CAACqM,GAAG,CAAErF,MAAM;kBAAA,IAAAsF,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;kBAAA,oBACjBtP,OAAA,CAACzB,MAAM,CAACgR,EAAE;oBAERlC,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAE;oBACxBE,OAAO,EAAE;sBAAEF,OAAO,EAAE;oBAAE,CAAE;oBACxBX,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5B5M,OAAA;sBAAI2M,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,eACzC5M,OAAA;wBAAK2M,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChC5M,OAAA;0BAAK2M,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,eACtC5M,OAAA;4BAAK2M,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,EAC7F,EAAAiC,mBAAA,GAAAtF,MAAM,CAAC1E,WAAW,cAAAgK,mBAAA,uBAAlBA,mBAAA,CAAoBtO,MAAM,CAAC,CAAC,CAAC,KAAI;0BAAG;4BAAAwM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNlN,OAAA;0BAAK2M,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnB5M,OAAA;4BAAK2M,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAErD,MAAM,CAAC1E;0BAAW;4BAAAkI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7ElN,OAAA;4BAAK2M,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAErD,MAAM,CAACvE;0BAAK;4BAAA+H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3DlN,OAAA;4BAAK2M,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC7C,EAAAkC,iBAAA,GAAAvF,MAAM,CAACiC,SAAS,cAAAsD,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBxD,aAAa,cAAAyD,qBAAA,uBAA/BA,qBAAA,CAAiCb,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,kBAAW,EAAC,EAAAc,kBAAA,GAAAzF,MAAM,CAACiC,SAAS,cAAAwD,kBAAA,uBAAhBA,kBAAA,CAAkB9L,WAAW,KAAI,GAAG,EAAC,eAC7G;0BAAA;4BAAA6J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLlN,OAAA;sBAAI2M,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9D5M,OAAA;wBAAK2M,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAAqC,kBAAA,GAAA1F,MAAM,CAACiC,SAAS,cAAAyD,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkB3D,aAAa,cAAA4D,qBAAA,uBAA/BA,qBAAA,CAAiChB,cAAc,CAAC,CAAC,KAAI;sBAAG;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnHlN,OAAA;wBAAK2M,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAuC,kBAAA,GAAA5F,MAAM,CAACiC,SAAS,cAAA2D,kBAAA,uBAAhBA,kBAAA,CAAkBK,YAAY,KAAI,GAAG,EAAC,WAAS;sBAAA;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACLlN,OAAA;sBAAI2M,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9D5M,OAAA;wBAAK2M,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAAE,EAAAwC,kBAAA,GAAA7F,MAAM,CAACiC,SAAS,cAAA4D,kBAAA,uBAAhBA,kBAAA,CAAkBzD,cAAc,KAAI,GAAG,EAAC,GAAC;sBAAA;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnGlN,OAAA;wBAAK2M,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAyC,kBAAA,GAAA9F,MAAM,CAACiC,SAAS,cAAA6D,kBAAA,uBAAhBA,kBAAA,CAAkBnM,WAAW,KAAI,GAAG,EAAC,eAAa;sBAAA;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACLlN,OAAA;sBAAI2M,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9D5M,OAAA;wBAAK2M,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC5M,OAAA;0BAAM2M,SAAS,EAAE,2EACfpD,MAAM,CAACsE,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GACtEtE,MAAM,CAACsE,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GACnE,+BAA+B,EAC9B;0BAAAjB,QAAA,EACArD,MAAM,CAACsE;wBAAkB;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACPlN,OAAA;0BAAM2M,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAEnF,gBAAgB,EAAA6H,kBAAA,GAAC/F,MAAM,CAACiC,SAAS,cAAA8D,kBAAA,uBAAhBA,kBAAA,CAAkBzH,UAAU;wBAAC;0BAAAkF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLlN,OAAA;sBAAI2M,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9D5M,OAAA;wBAAM2M,SAAS,EAAE,2EACfpD,MAAM,CAACpE,gBAAgB,KAAK,YAAY,GAAG,+BAA+B,GAC1EoE,MAAM,CAACpE,gBAAgB,KAAK,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,EAChG;wBAAAyH,QAAA,EACArD,MAAM,CAACpE;sBAAgB;wBAAA4H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLlN,OAAA;sBAAI2M,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACxE5M,OAAA;wBAAK2M,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzC5M,OAAA;0BACE2M,SAAS,EAAC,+FAA+F;0BACzGQ,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAACvC,MAAM,CAAE;0BACzCkG,KAAK,EAAC,cAAc;0BAAA7C,QAAA,eAEpB5M,OAAA,CAACrB,GAAG;4BAACgO,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACTlN,OAAA;0BACE2M,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACxC,MAAM,CAAE;0BACtCkG,KAAK,EAAC,kBAAkB;0BAAA7C,QAAA,eAExB5M,OAAA,CAACf,IAAI;4BAAC0N,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTlN,OAAA;0BACE2M,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAACC,MAAM,CAAE;0BACrCkG,KAAK,EAAC,aAAa;0BAAA7C,QAAA,eAEnB5M,OAAA,CAACpB,IAAI;4BAAC+N,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTlN,OAAA;0BACE2M,SAAS,EAAC,kFAAkF;0BAC5FQ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAACK,MAAM,CAACN,GAAG,CAAE;0BAC9CwG,KAAK,EAAC,eAAe;0BAAA7C,QAAA,eAErB5M,OAAA,CAACnB,MAAM;4BAAC8N,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAhFA3D,MAAM,CAACN,GAAG;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiFN,CAAC;gBAAA,CACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlN,OAAA,CAACxB,eAAe;UAAAoO,QAAA,EACbvJ,gBAAgB,IAAII,wBAAwB,iBAC3CzD,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1F5M,OAAA,CAACzB,MAAM,CAAC6O,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAGzF5M,OAAA;gBAAK2M,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrE5M,OAAA;kBAAK2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5M,OAAA;oBAAK2M,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5M,OAAA;sBAAK2M,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,EACtG,EAAA5L,qBAAA,GAAAyC,wBAAwB,CAACoB,WAAW,cAAA7D,qBAAA,uBAApCA,qBAAA,CAAsCT,MAAM,CAAC,CAAC,CAAC,KAAI;oBAAG;sBAAAwM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNlN,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAI2M,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EACzCnJ,wBAAwB,CAACoB;sBAAW;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,eACLlN,OAAA;wBAAG2M,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjCnJ,wBAAwB,CAACuB;sBAAK;wBAAA+H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlN,OAAA;oBACE2M,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM7J,mBAAmB,CAAC,KAAK,CAAE;oBAAAsJ,QAAA,eAE1C5M,OAAA,CAACd,CAAC;sBAACyN,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlN,OAAA;gBAAK2M,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EACzD7I,gBAAgB,gBACf/D,OAAA;kBAAK2M,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,eACrD5M,OAAA;oBAAK2M,SAAS,EAAC;kBAAiE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,gBAENlN,OAAA;kBAAK2M,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBAExB5M,OAAA;oBAAK2M,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnE5M,OAAA;sBAAK2M,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,eACxE5M,OAAA;wBAAK2M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C5M,OAAA;0BAAK2M,SAAS,EAAC,mEAAmE;0BAAAC,QAAA,eAChF5M,OAAA,CAAChB,KAAK;4BAAC2N,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACNlN,OAAA;0BAAA4M,QAAA,gBACE5M,OAAA;4BAAG2M,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAc;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACnElN,OAAA;4BAAG2M,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC3C,CAAA/I,eAAe,aAAfA,eAAe,wBAAA5C,qBAAA,GAAf4C,eAAe,CAAE6L,YAAY,cAAAzO,qBAAA,wBAAAC,sBAAA,GAA7BD,qBAAA,CAA+B0O,WAAW,cAAAzO,sBAAA,uBAA1CA,sBAAA,CAA4C6M,MAAM,CAAC,CAACC,GAAG,EAAE4B,GAAG,KAAK5B,GAAG,IAAI4B,GAAG,CAACrE,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI;0BAAC;4BAAAwB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENlN,OAAA;sBAAK2M,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1E5M,OAAA;wBAAK2M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C5M,OAAA;0BAAK2M,SAAS,EAAC,oEAAoE;0BAAAC,QAAA,eACjF5M,OAAA,CAACV,KAAK;4BAACqN,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACNlN,OAAA;0BAAA4M,QAAA,gBACE5M,OAAA;4BAAG2M,SAAS,EAAC,oCAAoC;4BAAAC,QAAA,EAAC;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAClElN,OAAA;4BAAG2M,SAAS,EAAC,kCAAkC;4BAAAC,QAAA,GAC5CpM,IAAI,CAACqP,KAAK,CAAC,CAAAhM,eAAe,aAAfA,eAAe,wBAAA1C,sBAAA,GAAf0C,eAAe,CAAE6L,YAAY,cAAAvO,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BwO,WAAW,cAAAvO,sBAAA,uBAA1CA,sBAAA,CAA4C2M,MAAM,CAAC,CAACC,GAAG,EAAE4B,GAAG,KAAK5B,GAAG,IAAI4B,GAAG,CAACnE,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,CAAA5H,eAAe,aAAfA,eAAe,wBAAAxC,sBAAA,GAAfwC,eAAe,CAAE6L,YAAY,cAAArO,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BsO,WAAW,cAAArO,sBAAA,uBAA1CA,sBAAA,CAA4CX,MAAM,KAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,GAClL;0BAAA;4BAAAoM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENlN,OAAA;sBAAK2M,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5E5M,OAAA;wBAAK2M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C5M,OAAA;0BAAK2M,SAAS,EAAC,qEAAqE;0BAAAC,QAAA,eAClF5M,OAAA,CAACX,SAAS;4BAACsN,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,eACNlN,OAAA;0BAAA4M,QAAA,gBACE5M,OAAA;4BAAG2M,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAC/DlN,OAAA;4BAAG2M,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAC7C,CAAA/I,eAAe,aAAfA,eAAe,wBAAAtC,qBAAA,GAAfsC,eAAe,CAAEiM,kBAAkB,cAAAvO,qBAAA,uBAAnCA,qBAAA,CAAqCZ,MAAM,KAAI;0BAAC;4BAAAoM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENlN,OAAA;sBAAK2M,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5E5M,OAAA;wBAAK2M,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C5M,OAAA;0BAAK2M,SAAS,EAAC,qEAAqE;0BAAAC,QAAA,eAClF5M,OAAA,CAACT,UAAU;4BAACoN,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACNlN,OAAA;0BAAA4M,QAAA,gBACE5M,OAAA;4BAAG2M,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAC;0BAAO;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAC9DlN,OAAA;4BAAG2M,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAC7C,CAAA/I,eAAe,aAAfA,eAAe,wBAAArC,qBAAA,GAAfqC,eAAe,CAAEkM,WAAW,cAAAvO,qBAAA,uBAA5BA,qBAAA,CAA8Bb,MAAM,KAAI;0BAAC;4BAAAoM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNlN,OAAA;oBAAK2M,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC5M,OAAA;sBAAI2M,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACxE5M,OAAA,CAACP,QAAQ;wBAACkN,SAAS,EAAC;sBAA6B;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAEtD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLlN,OAAA;sBAAK2M,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvB,CAAA/I,eAAe,aAAfA,eAAe,wBAAApC,sBAAA,GAAfoC,eAAe,CAAE6L,YAAY,cAAAjO,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BkO,WAAW,cAAAjO,sBAAA,uBAA1CA,sBAAA,CAA4C0E,KAAK,CAAC,CAAC,CAAC,CAAC,CAACwI,GAAG,CAAC,CAACgB,GAAG,EAAEI,KAAK,kBACpEhQ,OAAA;wBAAiB2M,SAAS,EAAC,iEAAiE;wBAAAC,QAAA,gBAC1F5M,OAAA;0BAAK2M,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1C5M,OAAA;4BAAK2M,SAAS,EAAC;0BAAmC;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzDlN,OAAA;4BAAM2M,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAChD,IAAIhF,IAAI,CAACgI,GAAG,CAAClI,IAAI,CAAC,CAACuI,kBAAkB,CAAC;0BAAC;4BAAAlD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNlN,OAAA;0BAAK2M,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GACnCgD,GAAG,CAACrE,QAAQ,EAAC,mBAAY,EAAC/K,IAAI,CAACqP,KAAK,CAACD,GAAG,CAACnE,WAAW,IAAI,CAAC,CAAC,EAAC,OAC9D;wBAAA;0BAAAsB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,GATE8C,KAAK;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAUV,CACN,CAAC,kBACAlN,OAAA;wBAAG2M,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAC;sBAAiC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBACnF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL,CAAArJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkM,WAAW,KAAIlM,eAAe,CAACkM,WAAW,CAACpP,MAAM,GAAG,CAAC,iBACrEX,OAAA;oBAAK2M,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC5M,OAAA;sBAAI2M,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACxE5M,OAAA,CAACR,OAAO;wBAACmN,SAAS,EAAC;sBAA6B;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAErD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLlN,OAAA;sBAAK2M,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EACnD/I,eAAe,CAACkM,WAAW,CAACnB,GAAG,CAAC,CAACsB,MAAM,EAAEF,KAAK,kBAC7ChQ,OAAA;wBAAiB2M,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,eAClD5M,OAAA;0BAAK2M,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChD5M,OAAA;4BAAM2M,SAAS,EAAC,8CAA8C;4BAAAC,QAAA,EAC3DsD,MAAM,CAACA,MAAM,IAAI;0BAAS;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACPlN,OAAA;4BAAM2M,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACpCsD,MAAM,CAAC3E,QAAQ,EAAC,WACnB;0BAAA;4BAAAwB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC,GARE8C,KAAK;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OASV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBlN,OAAA,CAACxB,eAAe;UAAAoO,QAAA,EACbrJ,aAAa,IAAII,qBAAqB,iBACrC3D,OAAA,CAACzB,MAAM,CAAC6O,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1F5M,OAAA,CAACzB,MAAM,CAAC6O,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAGzF5M,OAAA;gBAAK2M,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnE5M,OAAA;kBAAK2M,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5M,OAAA;oBAAK2M,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5M,OAAA;sBAAK2M,SAAS,EAAC,mEAAmE;sBAAAC,QAAA,eAChF5M,OAAA,CAACf,IAAI;wBAAC0N,SAAS,EAAC;sBAAoB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACNlN,OAAA;sBAAA4M,QAAA,gBACE5M,OAAA;wBAAI2M,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAC;sBAE7C;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlN,OAAA;wBAAG2M,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjCjJ,qBAAqB,CAACkB;sBAAW;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlN,OAAA;oBACE2M,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM3J,gBAAgB,CAAC,KAAK,CAAE;oBAAAoJ,QAAA,eAEvC5M,OAAA,CAACd,CAAC;sBAACyN,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlN,OAAA;gBAAK2M,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClB5M,OAAA;kBAAK2M,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBAExB5M,OAAA;oBAAK2M,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC5M,OAAA;sBAAI2M,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAEzD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLlN,OAAA;sBAAK2M,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C5M,OAAA;wBAAA4M,QAAA,EAAG;sBAA4D;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnElN,OAAA;wBAAA4M,QAAA,EAAG;sBAAuE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC9ElN,OAAA;wBAAA4M,QAAA,EAAG;sBAAmD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1DlN,OAAA;wBAAA4M,QAAA,EAAG;sBAAoD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNlN,OAAA;oBAAK2M,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB5M,OAAA;sBAAK2M,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD5M,OAAA;wBAAI2M,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAEpD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlN,OAAA;wBACEmN,OAAO,EAAEf,mBAAoB;wBAC7BO,SAAS,EAAC,4GAA4G;wBAAAC,QAAA,EAErH3I,UAAU,gBACTjE,OAAA,CAAAE,SAAA;0BAAA0M,QAAA,gBACE5M,OAAA,CAACZ,KAAK;4BAACuN,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,WAEpC;wBAAA,eAAE,CAAC,gBAEHlN,OAAA,CAAAE,SAAA;0BAAA0M,QAAA,gBACE5M,OAAA,CAACb,IAAI;4BAACwN,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,aAEnC;wBAAA,eAAE;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENlN,OAAA;sBAAK2M,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,eACzD5M,OAAA;wBAAK2M,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACrC5M,OAAA;0BAAA4M,QAAA,EAAOZ,uBAAuB,CAACrI,qBAAqB;wBAAC;0BAAAoJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNlN,OAAA;oBAAK2M,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC5M,OAAA;sBAAI2M,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAEzD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLlN,OAAA;sBAAK2M,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB5M,OAAA;wBAAK2M,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7C5M,OAAA;0BAAG2M,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACtElN,OAAA;0BAAM2M,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAAC;wBAEtE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNlN,OAAA;wBAAK2M,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7C5M,OAAA;0BAAG2M,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAc;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACxElN,OAAA;0BAAM2M,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAAC;wBAEtE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNlN,OAAA;oBAAK2M,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5M,OAAA;sBAAI2M,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAE3D;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLlN,OAAA;sBAAG2M,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAC;oBAEvC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrM,EAAA,CArpCID,OAAO;AAAAuP,EAAA,GAAPvP,OAAO;AAupCb,eAAeA,OAAO;AAAC,IAAAuP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}