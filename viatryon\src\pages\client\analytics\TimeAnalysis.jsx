import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from 'recharts';
import { Clock, Calendar, TrendingUp, Users } from 'lucide-react';

const TimeAnalysis = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [timeData, setTimeData] = useState({
    dailyTrends: [],
    hourlyPatterns: [],
    weeklyPatterns: [],
    durationDistribution: {}
  });

  // Fetch time analysis data
  useEffect(() => {
    const fetchTimeData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        const response = await fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setTimeData(data);
        } else {
          console.error('Failed to fetch time analysis data');
        }
      } catch (error) {
        console.error('Error fetching time analysis data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTimeData();
  }, [timeRange]);

  // Format data for charts
  const hourlyData = timeData.hourlyPatterns?.map(item => ({
    hour: `${item._id.toString().padStart(2, '0')}:00`,
    sessions: item.sessions,
    avgDuration: Math.round(item.avgDuration || 0)
  })) || [];

  const weeklyData = timeData.weeklyPatterns?.map(item => {
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return {
      day: dayNames[item._id - 1] || 'Unknown',
      sessions: item.sessions,
      avgDuration: Math.round(item.avgDuration || 0)
    };
  }) || [];

  const dailyTrends = timeData.dailyTrends?.map(item => ({
    date: item._id,
    sessions: item.sessions,
    avgDuration: Math.round(item.avgDuration || 0)
  })) || [];

  // Calculate peak metrics
  const peakHour = hourlyData.reduce((max, current) =>
    current.sessions > (max.sessions || 0) ? current : max, {});
  const peakDay = weeklyData.reduce((max, current) =>
    current.sessions > (max.sessions || 0) ? current : max, {});

  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  // Calculate average session duration from daily trends
  const avgDuration = dailyTrends.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / (dailyTrends.length || 1);

  // Calculate session duration distribution data
  const sessionDurationData = [
    { name: '0-30s', value: timeData.durationDistribution?.['0-30'] || 0 },
    { name: '30s-1m', value: timeData.durationDistribution?.['30-60'] || 0 },
    { name: '1-2m', value: timeData.durationDistribution?.['60-120'] || 0 },
    { name: '2-5m', value: timeData.durationDistribution?.['120-300'] || 0 },
    { name: '5m+', value: timeData.durationDistribution?.['300+'] || 0 }
  ];

  const metrics = [
    {
      title: 'Peak Hour',
      value: peakHour.hour || 'N/A',
      change: 'No change',
      trend: 'neutral',
      icon: <Clock className="h-6 w-6 text-[#2D8C88]" />
    },
    {
      title: 'Peak Day',
      value: peakDay.day || 'N/A',
      change: 'No change',
      trend: 'neutral',
      icon: <Calendar className="h-6 w-6 text-blue-500" />
    },
    {
      title: 'Avg. Session Duration',
      value: formatDuration(avgDuration),
      change: 'No change',
      trend: 'neutral',
      icon: <TrendingUp className="h-6 w-6 text-green-500" />
    },
    {
      title: 'Total Sessions',
      value: dailyTrends.reduce((sum, day) => sum + (day.sessions || 0), 0).toLocaleString(),
      change: 'No change',
      trend: 'neutral',
      icon: <Users className="h-6 w-6 text-purple-500" />
    },
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                {metric.icon}
              </div>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Hourly Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Hourly Activity</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                  name="Sessions"
                />
                <Line
                  type="monotone"
                  dataKey="avgDuration"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6' }}
                  name="Avg Duration (s)"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Weekly Patterns */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Patterns</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar
                  dataKey="sessions"
                  fill="#2D8C88"
                  name="Sessions"
                />
                <Bar
                  dataKey="avgDuration"
                  fill="#3B82F6"
                  name="Avg Duration (s)"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Session Duration Distribution */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-xl shadow-sm p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Session Duration Distribution</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={sessionDurationData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {sessionDurationData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </motion.div>
    </div>
  );
};

export default TimeAnalysis; 