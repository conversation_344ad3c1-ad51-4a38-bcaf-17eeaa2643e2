{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Contact.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [formStatus, setFormStatus] = useState({\n    submitted: false,\n    error: null\n  });\n  const [errors, setErrors] = useState({});\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) newErrors.name = 'Name is required';\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';\n    if (!formData.message.trim()) newErrors.message = 'Message is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/email/contact`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit form');\n      }\n      setFormStatus({\n        submitted: true,\n        error: null\n      });\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n    } catch (error) {\n      setFormStatus({\n        submitted: false,\n        error: error.message || 'Failed to submit form. Please try again.'\n      });\n    }\n  };\n  const contactInfo = [{\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this),\n    title: 'Email',\n    content: '<EMAIL>',\n    link: 'mailto:<EMAIL>'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this),\n    title: 'Phone',\n    content: '+****************',\n    link: 'tel:+15551234567'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this),\n    title: 'Location',\n    content: '123 Innovation Street, Tech City, TC 12345',\n    link: 'https://maps.google.com'\n  }];\n  const socialLinks = [{\n    name: 'Facebook',\n    url: 'https://www.facebook.com/viatryon',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'Instagram',\n    url: 'https://www.instagram.com/viatryon',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'LinkedIn',\n    url: 'https://www.linkedin.com/company/viatryon',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative min-h-[100vh] pt-24 pb-16 md:pt-32 md:pb-24 lg:pt-40 lg:pb-32 overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2 text-center lg:text-left\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6\n              },\n              className: \"inline-block px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-[#2D8C88]\",\n                children: \"Get in Touch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6\n              },\n              className: \"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-serif text-[#1F2937] mb-4 md:mb-6 leading-tight\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"italic\",\n                children: \"Let's\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n                children: \"Connect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"text-base md:text-lg lg:text-xl text-[#1F2937] mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans font-light leading-relaxed\",\n              children: \"Have questions about our virtual try-on technology? We're here to help.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 justify-center lg:justify-start mb-8 md:mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/virtual-try-on\",\n                className: \"relative text-white px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center group\",\n                style: {\n                  backgroundColor: '#2D8C88'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this), \"Try It Now\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/how-it-works\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"relative bg-white border-2 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center w-full sm:w-auto group\",\n                  style: {\n                    borderColor: '#2D8C88',\n                    color: '#2D8C88'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 6h16M4 12h16M4 18h7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 23\n                    }, this), \"Learn More\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"lg:w-1/2 relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/imgs/contact-hero.png\",\n                alt: \"Contact Us\",\n                className: \"h-[40vh] sm:h-[50vh] md:h-[60vh] lg:h-[70vh] object-contain drop-shadow-[0_10px_30px_rgba(0,0,0,0.1)] transform hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -bottom-8 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-3/4 h-6 md:h-10 rounded-full blur-xl md:blur-2xl\",\n                style: {\n                  backgroundColor: 'rgba(31, 41, 55, 0.1)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            viewport: {\n              once: true\n            },\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\",\n                children: \"Contact Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n                children: \"Get in touch with us for any questions about our virtual try-on technology.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: contactInfo.map((info, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n                href: info.link,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                whileInView: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: index * 0.1\n                },\n                viewport: {\n                  once: true\n                },\n                className: \"flex items-start space-x-4 group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] group-hover:bg-[#2D8C88] group-hover:text-white transition-colors duration-200\",\n                  children: info.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-[#1F2937] mb-1\",\n                    children: info.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n                    children: info.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-[#1F2937] mb-4\",\n                children: \"Follow Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4\",\n                children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n                  href: social.url,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  whileInView: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    duration: 0.5,\n                    delay: index * 0.1\n                  },\n                  viewport: {\n                    once: true\n                  },\n                  className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors duration-200\",\n                  children: social.icon\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-xl p-6 md:p-8 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-6\",\n              children: \"Send Us a Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), formStatus.submitted ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.95\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-8 w-8 text-[#2D8C88]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 13l4 4L19 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-[#1F2937] mb-2\",\n                children: \"Message Sent!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n                children: \"Thank you for contacting us. We'll get back to you soon.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"name\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"name\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`,\n                  placeholder: \"Your name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"email\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`,\n                  placeholder: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"subject\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Subject\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"subject\",\n                  name: \"subject\",\n                  value: formData.subject,\n                  onChange: handleChange,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.subject ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`,\n                  placeholder: \"What's this about?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this), errors.subject && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"message\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"message\",\n                  name: \"message\",\n                  value: formData.message,\n                  onChange: handleChange,\n                  rows: 4,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.message ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200 resize-none`,\n                  placeholder: \"Your message...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), errors.message && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), formStatus.error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 rounded-lg bg-red-50 text-red-500 text-sm\",\n                children: formStatus.error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"w-full bg-[#2D8C88] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#F28C38] transition-colors duration-200\",\n                children: \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Transform Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Shopping Experience?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Join leading brands in revolutionizing online shopping with our cutting-edge AR technology. Let's create something amazing together.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/virtual-try-on\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), \"Schedule a Demo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 21\n                  }, this), \"Contact Us\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(Contact, \"UzgxteZbL9m1l61xKt7u9vN6yk8=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "Contact", "_s", "formData", "setFormData", "name", "email", "subject", "message", "formStatus", "setFormStatus", "submitted", "error", "errors", "setErrors", "handleChange", "e", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "Object", "keys", "length", "handleSubmit", "preventDefault", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "JSON", "stringify", "ok", "errorData", "json", "Error", "contactInfo", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "content", "link", "socialLinks", "url", "style", "background", "div", "initial", "opacity", "y", "animate", "transition", "duration", "h1", "p", "delay", "to", "backgroundColor", "borderColor", "color", "scale", "src", "alt", "whileInView", "viewport", "once", "map", "info", "index", "a", "href", "rel", "social", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Contact.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n\n  const [formStatus, setFormStatus] = useState({\n    submitted: false,\n    error: null\n  });\n\n  const [errors, setErrors] = useState({});\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) newErrors.name = 'Name is required';\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';\n    if (!formData.message.trim()) newErrors.message = 'Message is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (!validateForm()) return;\n\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/email/contact`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit form');\n      }\n\n      setFormStatus({ submitted: true, error: null });\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch (error) {\n      setFormStatus({ submitted: false, error: error.message || 'Failed to submit form. Please try again.' });\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n      title: 'Email',\n      content: '<EMAIL>',\n      link: 'mailto:<EMAIL>'\n    },\n    {\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n        </svg>\n      ),\n      title: 'Phone',\n      content: '+****************',\n      link: 'tel:+15551234567'\n    },\n    {\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      ),\n      title: 'Location',\n      content: '123 Innovation Street, Tech City, TC 12345',\n      link: 'https://maps.google.com'\n    }\n  ];\n\n  const socialLinks = [\n    {\n      name: 'Facebook',\n      url: 'https://www.facebook.com/viatryon',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Instagram',\n      url: 'https://www.instagram.com/viatryon',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'LinkedIn',\n      url: 'https://www.linkedin.com/company/viatryon',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section\n        className=\"relative min-h-[100vh] pt-24 pb-16 md:pt-32 md:pb-24 lg:pt-40 lg:pb-32 overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`,\n        }}\n      >\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n            }}\n          />\n          <div\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n            }}\n          />\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\">\n            <div className=\"lg:w-1/2 text-center lg:text-left\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                className=\"inline-block px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm mb-6\"\n              >\n                <span className=\"text-sm font-medium text-[#2D8C88]\">Get in Touch</span>\n              </motion.div>\n\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-serif text-[#1F2937] mb-4 md:mb-6 leading-tight\"\n              >\n                <span className=\"italic\">Let's</span>\n                <br />\n                <span className=\"font-medium bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\n                  Connect\n                </span>\n              </motion.h1>\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"text-base md:text-lg lg:text-xl text-[#1F2937] mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans font-light leading-relaxed\"\n              >\n                Have questions about our virtual try-on technology? We're here to help.\n              </motion.p>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n                className=\"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 justify-center lg:justify-start mb-8 md:mb-12\"\n              >\n                <Link\n                  to=\"/virtual-try-on\"\n                  className=\"relative text-white px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center group\"\n                  style={{ backgroundColor: '#2D8C88' }}\n                >\n                  <span className=\"flex items-center justify-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Try It Now\n                  </span>\n                </Link>\n                <Link to=\"/how-it-works\">\n                  <button\n                    className=\"relative bg-white border-2 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center w-full sm:w-auto group\"\n                    style={{ borderColor: '#2D8C88', color: '#2D8C88' }}\n                  >\n                    <span className=\"flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h7\" />\n                      </svg>\n                      Learn More\n                    </span>\n                  </button>\n                </Link>\n              </motion.div>\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8 }}\n              className=\"lg:w-1/2 relative\"\n            >\n              <div className=\"relative flex items-center justify-center\">\n                <img\n                  src=\"/imgs/contact-hero.png\"\n                  alt=\"Contact Us\"\n                  className=\"h-[40vh] sm:h-[50vh] md:h-[60vh] lg:h-[70vh] object-contain drop-shadow-[0_10px_30px_rgba(0,0,0,0.1)] transform hover:scale-105 transition-transform duration-300\"\n                />\n                <div\n                  className=\"absolute -bottom-8 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-3/4 h-6 md:h-10 rounded-full blur-xl md:blur-2xl\"\n                  style={{ backgroundColor: 'rgba(31, 41, 55, 0.1)' }}\n                />\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form Section */}\n      <section className=\"py-16 md:py-20 bg-white\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16\">\n            {/* Contact Information */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n              viewport={{ once: true }}\n              className=\"space-y-8\"\n            >\n              <div>\n                <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\">Contact Information</h2>\n                <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">\n                  Get in touch with us for any questions about our virtual try-on technology.\n                </p>\n              </div>\n\n              <div className=\"space-y-6\">\n                {contactInfo.map((info, index) => (\n                  <motion.a\n                    key={index}\n                    href={info.link}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"flex items-start space-x-4 group\"\n                  >\n                    <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] group-hover:bg-[#2D8C88] group-hover:text-white transition-colors duration-200\">\n                      {info.icon}\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-medium text-[#1F2937] mb-1\">{info.title}</h3>\n                      <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">{info.content}</p>\n                    </div>\n                  </motion.a>\n                ))}\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-[#1F2937] mb-4\">Follow Us</h3>\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => (\n                    <motion.a\n                      key={index}\n                      href={social.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      initial={{ opacity: 0, y: 20 }}\n                      whileInView={{ opacity: 1, y: 0 }}\n                      transition={{ duration: 0.5, delay: index * 0.1 }}\n                      viewport={{ once: true }}\n                      className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors duration-200\"\n                    >\n                      {social.icon}\n                    </motion.a>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Contact Form */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n              viewport={{ once: true }}\n              className=\"bg-white rounded-xl p-6 md:p-8 shadow-sm\"\n            >\n              <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-6\">Send Us a Message</h2>\n              {formStatus.submitted ? (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.95 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-8\"\n                >\n                  <div className=\"w-16 h-16 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mx-auto mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-xl font-medium text-[#1F2937] mb-2\">Message Sent!</h3>\n                  <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">\n                    Thank you for contacting us. We'll get back to you soon.\n                  </p>\n                </motion.div>\n              ) : (\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.name ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`}\n                      placeholder=\"Your name\"\n                    />\n                    {errors.name && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.name}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Email\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.email ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`}\n                      placeholder=\"<EMAIL>\"\n                    />\n                    {errors.email && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.email}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Subject\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.subject ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`}\n                      placeholder=\"What's this about?\"\n                    />\n                    {errors.subject && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.subject}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Message\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleChange}\n                      rows={4}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.message ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200 resize-none`}\n                      placeholder=\"Your message...\"\n                    />\n                    {errors.message && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.message}</p>\n                    )}\n                  </div>\n\n                  {formStatus.error && (\n                    <div className=\"p-4 rounded-lg bg-red-50 text-red-500 text-sm\">\n                      {formStatus.error}\n                    </div>\n                  )}\n\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-[#2D8C88] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#F28C38] transition-colors duration-200\"\n                  >\n                    Send Message\n                  </button>\n                </form>\n              )}\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\n        }}\n      >\n        {/* Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\n              Ready to Transform Your\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\n                Shopping Experience?\n              </span>\n            </h2>\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\n              Join leading brands in revolutionizing online shopping with our cutting-edge AR technology. Let's create something amazing together.\n            </p>\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\n              <Link to=\"/virtual-try-on\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\n                  style={{ color: '#2D8C88' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                      />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Schedule a Demo\n                  </span>\n                </button>\n              </Link>\n              <Link to=\"/contact\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\n                  style={{ borderColor: '#F28C38' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                      />\n                    </svg>\n                    Contact Us\n                  </span>\n                </button>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC;IAC3CiB,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEX,IAAI;MAAEY;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACd,IAAI,GAAGY;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIJ,MAAM,CAACR,IAAI,CAAC,EAAE;MAChBS,SAAS,CAACK,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACd,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAAClB,QAAQ,CAACE,IAAI,CAACiB,IAAI,CAAC,CAAC,EAAED,SAAS,CAAChB,IAAI,GAAG,kBAAkB;IAC9D,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACgB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACf,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACiB,IAAI,CAACpB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/Ce,SAAS,CAACf,KAAK,GAAG,kBAAkB;IACtC;IACA,IAAI,CAACH,QAAQ,CAACI,OAAO,CAACe,IAAI,CAAC,CAAC,EAAED,SAAS,CAACd,OAAO,GAAG,qBAAqB;IACvE,IAAI,CAACJ,QAAQ,CAACK,OAAO,CAACc,IAAI,CAAC,CAAC,EAAED,SAAS,CAACb,OAAO,GAAG,qBAAqB;IACvEM,SAAS,CAACO,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAClB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,oBAAoB,EAAE;QACjFC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnC,QAAQ;MAC/B,CAAC,CAAC;MAEF,IAAI,CAAC0B,QAAQ,CAACU,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAAChC,OAAO,IAAI,uBAAuB,CAAC;MAC/D;MAEAE,aAAa,CAAC;QAAEC,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAC/CR,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,aAAa,CAAC;QAAEC,SAAS,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACJ,OAAO,IAAI;MAA2C,CAAC,CAAC;IACzG;EACF,CAAC;EAED,MAAMmC,WAAW,GAAG,CAClB;IACEC,IAAI,eACF5C,OAAA;MAAK6C,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GlD,OAAA;QAAMmD,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3K,CACN;IACDC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,sBAAsB;IAC/BC,IAAI,EAAE;EACR,CAAC,EACD;IACEjB,IAAI,eACF5C,OAAA;MAAK6C,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GlD,OAAA;QAAMmD,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAuN;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5R,CACN;IACDC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,mBAAmB;IAC5BC,IAAI,EAAE;EACR,CAAC,EACD;IACEjB,IAAI,eACF5C,OAAA;MAAK6C,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,gBAC/GlD,OAAA;QAAMmD,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAoF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5J1D,OAAA;QAAMmD,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CACN;IACDC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,4CAA4C;IACrDC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IACEzD,IAAI,EAAE,UAAU;IAChB0D,GAAG,EAAE,mCAAmC;IACxCnB,IAAI,eACF5C,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAE,QAAA,eAC9DlD,OAAA;QAAMsD,CAAC,EAAC;MAAgS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxS;EAET,CAAC,EACD;IACErD,IAAI,EAAE,WAAW;IACjB0D,GAAG,EAAE,oCAAoC;IACzCnB,IAAI,eACF5C,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAE,QAAA,eAC9DlD,OAAA;QAAMsD,CAAC,EAAC;MAA+2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACv3B;EAET,CAAC,EACD;IACErD,IAAI,EAAE,UAAU;IAChB0D,GAAG,EAAE,2CAA2C;IAChDnB,IAAI,eACF5C,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAE,QAAA,eAC9DlD,OAAA;QAAMsD,CAAC,EAAC;MAAqU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7U;EAET,CAAC,CACF;EAED,oBACE1D,OAAA;IAAK8C,SAAS,EAAC,6CAA6C;IAAAI,QAAA,gBAC1DlD,OAAA,CAACH,MAAM;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV1D,OAAA;MACE8C,SAAS,EAAC,wFAAwF;MAClGkB,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBAEFlD,OAAA;QAAK8C,SAAS,EAAC,kCAAkC;QAAAI,QAAA,gBAC/ClD,OAAA;UACE8C,SAAS,EAAC,8HAA8H;UACxIkB,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF1D,OAAA;UACE8C,SAAS,EAAC,mIAAmI;UAC7IkB,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1D,OAAA;QAAK8C,SAAS,EAAC,8CAA8C;QAAAI,QAAA,eAC3DlD,OAAA;UAAK8C,SAAS,EAAC,wEAAwE;UAAAI,QAAA,gBACrFlD,OAAA;YAAK8C,SAAS,EAAC,mCAAmC;YAAAI,QAAA,gBAChDlD,OAAA,CAACL,MAAM,CAACuE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9B1B,SAAS,EAAC,uEAAuE;cAAAI,QAAA,eAEjFlD,OAAA;gBAAM8C,SAAS,EAAC,oCAAoC;gBAAAI,QAAA,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eAEb1D,OAAA,CAACL,MAAM,CAAC8E,EAAE;cACRN,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9B1B,SAAS,EAAC,mGAAmG;cAAAI,QAAA,gBAE7GlD,OAAA;gBAAM8C,SAAS,EAAC,QAAQ;gBAAAI,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC1D,OAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1D,OAAA;gBAAM8C,SAAS,EAAC,wFAAwF;gBAAAI,QAAA,EAAC;cAEzG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACZ1D,OAAA,CAACL,MAAM,CAAC+E,CAAC;cACPP,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1C7B,SAAS,EAAC,4HAA4H;cAAAI,QAAA,EACvI;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAEX1D,OAAA,CAACL,MAAM,CAACuE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1C7B,SAAS,EAAC,6GAA6G;cAAAI,QAAA,gBAEvHlD,OAAA,CAACJ,IAAI;gBACHgF,EAAE,EAAC,iBAAiB;gBACpB9B,SAAS,EAAC,gPAAgP;gBAC1PkB,KAAK,EAAE;kBAAEa,eAAe,EAAE;gBAAU,CAAE;gBAAA3B,QAAA,eAEtClD,OAAA;kBAAM8C,SAAS,EAAC,kCAAkC;kBAAAI,QAAA,gBAChDlD,OAAA;oBAAK6C,KAAK,EAAC,4BAA4B;oBAACC,SAAS,EAAC,iFAAiF;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAC,QAAA,gBACvLlD,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1K1D,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,cAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACP1D,OAAA,CAACJ,IAAI;gBAACgF,EAAE,EAAC,eAAe;gBAAA1B,QAAA,eACtBlD,OAAA;kBACE8C,SAAS,EAAC,wQAAwQ;kBAClRkB,KAAK,EAAE;oBAAEc,WAAW,EAAE,SAAS;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAA7B,QAAA,eAEpDlD,OAAA;oBAAM8C,SAAS,EAAC,kCAAkC;oBAAAI,QAAA,gBAChDlD,OAAA;sBAAK6C,KAAK,EAAC,4BAA4B;sBAACC,SAAS,EAAC,iFAAiF;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAC,QAAA,eACvLlD,OAAA;wBAAMmD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAwB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,cAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN1D,OAAA,CAACL,MAAM,CAACuE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAI,CAAE;YACpCV,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAE,CAAE;YAClCT,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B1B,SAAS,EAAC,mBAAmB;YAAAI,QAAA,eAE7BlD,OAAA;cAAK8C,SAAS,EAAC,2CAA2C;cAAAI,QAAA,gBACxDlD,OAAA;gBACEiF,GAAG,EAAC,wBAAwB;gBAC5BC,GAAG,EAAC,YAAY;gBAChBpC,SAAS,EAAC;cAAmK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9K,CAAC,eACF1D,OAAA;gBACE8C,SAAS,EAAC,yHAAyH;gBACnIkB,KAAK,EAAE;kBAAEa,eAAe,EAAE;gBAAwB;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV1D,OAAA;MAAS8C,SAAS,EAAC,yBAAyB;MAAAI,QAAA,eAC1ClD,OAAA;QAAK8C,SAAS,EAAC,gCAAgC;QAAAI,QAAA,eAC7ClD,OAAA;UAAK8C,SAAS,EAAC,kDAAkD;UAAAI,QAAA,gBAE/DlD,OAAA,CAACL,MAAM,CAACuE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/Bc,WAAW,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BY,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvC,SAAS,EAAC,WAAW;YAAAI,QAAA,gBAErBlD,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAI8C,SAAS,EAAC,qDAAqD;gBAAAI,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5F1D,OAAA;gBAAG8C,SAAS,EAAC,+CAA+C;gBAAAI,QAAA,EAAC;cAE7D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN1D,OAAA;cAAK8C,SAAS,EAAC,WAAW;cAAAI,QAAA,EACvBP,WAAW,CAAC2C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BxF,OAAA,CAACL,MAAM,CAAC8F,CAAC;gBAEPC,IAAI,EAAEH,IAAI,CAAC1B,IAAK;gBAChB3C,MAAM,EAAC,QAAQ;gBACfyE,GAAG,EAAC,qBAAqB;gBACzBxB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/Bc,WAAW,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAClCE,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAEa,KAAK,GAAG;gBAAI,CAAE;gBAClDJ,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBvC,SAAS,EAAC,kCAAkC;gBAAAI,QAAA,gBAE5ClD,OAAA;kBAAK8C,SAAS,EAAC,uKAAuK;kBAAAI,QAAA,EACnLqC,IAAI,CAAC3C;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACN1D,OAAA;kBAAAkD,QAAA,gBACElD,OAAA;oBAAI8C,SAAS,EAAC,yCAAyC;oBAAAI,QAAA,EAAEqC,IAAI,CAAC5B;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzE1D,OAAA;oBAAG8C,SAAS,EAAC,+CAA+C;oBAAAI,QAAA,EAAEqC,IAAI,CAAC3B;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA,GAhBD8B,KAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1D,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAI8C,SAAS,EAAC,yCAAyC;gBAAAI,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtE1D,OAAA;gBAAK8C,SAAS,EAAC,gBAAgB;gBAAAI,QAAA,EAC5BY,WAAW,CAACwB,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,kBAC7BxF,OAAA,CAACL,MAAM,CAAC8F,CAAC;kBAEPC,IAAI,EAAEE,MAAM,CAAC7B,GAAI;kBACjB7C,MAAM,EAAC,QAAQ;kBACfyE,GAAG,EAAC,qBAAqB;kBACzBxB,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/Bc,WAAW,EAAE;oBAAEf,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAClCE,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEG,KAAK,EAAEa,KAAK,GAAG;kBAAI,CAAE;kBAClDJ,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAK,CAAE;kBACzBvC,SAAS,EAAC,2JAA2J;kBAAAI,QAAA,EAEpK0C,MAAM,CAAChD;gBAAI,GAVP4C,KAAK;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWF,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGb1D,OAAA,CAACL,MAAM,CAACuE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/Bc,WAAW,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BY,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvC,SAAS,EAAC,0CAA0C;YAAAI,QAAA,gBAEpDlD,OAAA;cAAI8C,SAAS,EAAC,qDAAqD;cAAAI,QAAA,EAAC;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzFjD,UAAU,CAACE,SAAS,gBACnBX,OAAA,CAACL,MAAM,CAACuE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAK,CAAE;cACrCV,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAE,CAAE;cAClClC,SAAS,EAAC,kBAAkB;cAAAI,QAAA,gBAE5BlD,OAAA;gBAAK8C,SAAS,EAAC,sFAAsF;gBAAAI,QAAA,eACnGlD,OAAA;kBAAK6C,KAAK,EAAC,4BAA4B;kBAACC,SAAS,EAAC,wBAAwB;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAC,QAAA,eAC9HlD,OAAA;oBAAMmD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1D,OAAA;gBAAI8C,SAAS,EAAC,yCAAyC;gBAAAI,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E1D,OAAA;gBAAG8C,SAAS,EAAC,+CAA+C;gBAAAI,QAAA,EAAC;cAE7D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,gBAEb1D,OAAA;cAAM6F,QAAQ,EAAElE,YAAa;cAACmB,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACjDlD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAO8F,OAAO,EAAC,MAAM;kBAAChD,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEhF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1D,OAAA;kBACE+F,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,MAAM;kBACT3F,IAAI,EAAC,MAAM;kBACXY,KAAK,EAAEd,QAAQ,CAACE,IAAK;kBACrB4F,QAAQ,EAAElF,YAAa;kBACvB+B,SAAS,EAAE,wCACTjC,MAAM,CAACR,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,+GAC4D;kBAChH6F,WAAW,EAAC;gBAAW;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,EACD7C,MAAM,CAACR,IAAI,iBACVL,OAAA;kBAAG8C,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAErC,MAAM,CAACR;gBAAI;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1D,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAO8F,OAAO,EAAC,OAAO;kBAAChD,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEjF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1D,OAAA;kBACE+F,IAAI,EAAC,OAAO;kBACZC,EAAE,EAAC,OAAO;kBACV3F,IAAI,EAAC,OAAO;kBACZY,KAAK,EAAEd,QAAQ,CAACG,KAAM;kBACtB2F,QAAQ,EAAElF,YAAa;kBACvB+B,SAAS,EAAE,wCACTjC,MAAM,CAACP,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,+GAC2D;kBAChH4F,WAAW,EAAC;gBAAgB;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,EACD7C,MAAM,CAACP,KAAK,iBACXN,OAAA;kBAAG8C,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAErC,MAAM,CAACP;gBAAK;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1D,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAO8F,OAAO,EAAC,SAAS;kBAAChD,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEnF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1D,OAAA;kBACE+F,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,SAAS;kBACZ3F,IAAI,EAAC,SAAS;kBACdY,KAAK,EAAEd,QAAQ,CAACI,OAAQ;kBACxB0F,QAAQ,EAAElF,YAAa;kBACvB+B,SAAS,EAAE,wCACTjC,MAAM,CAACN,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,+GACyD;kBAChH2F,WAAW,EAAC;gBAAoB;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,EACD7C,MAAM,CAACN,OAAO,iBACbP,OAAA;kBAAG8C,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAErC,MAAM,CAACN;gBAAO;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1D,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAO8F,OAAO,EAAC,SAAS;kBAAChD,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEnF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1D,OAAA;kBACEgG,EAAE,EAAC,SAAS;kBACZ3F,IAAI,EAAC,SAAS;kBACdY,KAAK,EAAEd,QAAQ,CAACK,OAAQ;kBACxByF,QAAQ,EAAElF,YAAa;kBACvBoF,IAAI,EAAE,CAAE;kBACRrD,SAAS,EAAE,wCACTjC,MAAM,CAACL,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,2HACqE;kBAC5H0F,WAAW,EAAC;gBAAiB;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,EACD7C,MAAM,CAACL,OAAO,iBACbR,OAAA;kBAAG8C,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAErC,MAAM,CAACL;gBAAO;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAELjD,UAAU,CAACG,KAAK,iBACfZ,OAAA;gBAAK8C,SAAS,EAAC,+CAA+C;gBAAAI,QAAA,EAC3DzC,UAAU,CAACG;cAAK;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CACN,eAED1D,OAAA;gBACE+F,IAAI,EAAC,QAAQ;gBACbjD,SAAS,EAAC,mHAAmH;gBAAAI,QAAA,EAC9H;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV1D,OAAA;MACE8C,SAAS,EAAC,oDAAoD;MAC9DkB,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBAGFlD,OAAA;QAAK8C,SAAS,EAAC,kCAAkC;QAAAI,QAAA,eAC/ClD,OAAA;UAAK8C,SAAS,EAAC,gDAAgD;UAAAI,QAAA,gBAC7DlD,OAAA;YAAK8C,SAAS,EAAC;UAAiF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvG1D,OAAA;YAAK8C,SAAS,EAAC;UAAqF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAK8C,SAAS,EAAC,0DAA0D;QAAAI,QAAA,eACvElD,OAAA,CAACL,MAAM,CAACuE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/Bc,WAAW,EAAE;YAAEf,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BY,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAnC,QAAA,gBAEzBlD,OAAA;YAAI8C,SAAS,EAAC,2EAA2E;YAAAI,QAAA,GAAC,yBAExF,eAAAlD,OAAA;cAAM8C,SAAS,EAAC,mFAAmF;cAAAI,QAAA,EAAC;YAEpG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL1D,OAAA;YAAG8C,SAAS,EAAC,yDAAyD;YAAAI,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1D,OAAA;YAAK8C,SAAS,EAAC,8EAA8E;YAAAI,QAAA,gBAC3FlD,OAAA,CAACJ,IAAI;cAACgF,EAAE,EAAC,iBAAiB;cAAA1B,QAAA,eACxBlD,OAAA;gBACE8C,SAAS,EAAC,uMAAuM;gBACjNkB,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAU,CAAE;gBAAA7B,QAAA,gBAE5BlD,OAAA;kBAAM8C,SAAS,EAAC;gBAA6J;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrL1D,OAAA;kBAAM8C,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC1ClD,OAAA;oBACE6C,KAAK,EAAC,4BAA4B;oBAClCC,SAAS,EAAC,cAAc;oBACxBC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAC,QAAA,gBAErBlD,OAAA;sBACEmD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACF1D,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,mBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACP1D,OAAA,CAACJ,IAAI;cAACgF,EAAE,EAAC,UAAU;cAAA1B,QAAA,eACjBlD,OAAA;gBACE8C,SAAS,EAAC,uMAAuM;gBACjNkB,KAAK,EAAE;kBAAEc,WAAW,EAAE;gBAAU,CAAE;gBAAA5B,QAAA,gBAElClD,OAAA;kBAAM8C,SAAS,EAAC;gBAA6H;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJ1D,OAAA;kBAAM8C,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC1ClD,OAAA;oBACE6C,KAAK,EAAC,4BAA4B;oBAClCC,SAAS,EAAC,cAAc;oBACxBC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAC,QAAA,eAErBlD,OAAA;sBACEmD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAsG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,cAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV1D,OAAA,CAACF,MAAM;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACxD,EAAA,CAnhBID,OAAO;AAAAmG,EAAA,GAAPnG,OAAO;AAqhBb,eAAeA,OAAO;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}