/**
 * Utility for removing white backgrounds from images
 * Only removes pure white and very close whites to preserve light gray watches
 */

/**
 * Remove white background from an image while preserving watch/bracelet details
 * @param {string} imageSrc - Image source URL or data URL
 * @param {number} tolerance - Color tolerance for white detection (0-255, default: 3)
 * @returns {Promise<string>} - Data URL of processed image
 */
export const removeWhiteBackground = async (imageSrc, tolerance = 3) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = img.width;
        canvas.height = img.height;

        // Draw the image
        ctx.drawImage(img, 0, 0);

        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // First pass: Identify edge pixels to preserve product boundaries
        const edgePixels = new Set();
        for (let y = 1; y < canvas.height - 1; y++) {
          for (let x = 1; x < canvas.width - 1; x++) {
            const idx = (y * canvas.width + x) * 4;
            const r = data[idx];
            const g = data[idx + 1];
            const b = data[idx + 2];

            // Check surrounding pixels for edge detection
            let isEdge = false;
            for (let dy = -1; dy <= 1; dy++) {
              for (let dx = -1; dx <= 1; dx++) {
                if (dx === 0 && dy === 0) continue;
                const neighborIdx = ((y + dy) * canvas.width + (x + dx)) * 4;
                const nr = data[neighborIdx];
                const ng = data[neighborIdx + 1];
                const nb = data[neighborIdx + 2];

                // If there's a significant color difference, it's an edge
                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);
                if (colorDiff > 30) { // Reduced threshold for better edge detection
                  isEdge = true;
                  break;
                }
              }
              if (isEdge) break;
            }

            if (isEdge) {
              edgePixels.add(idx / 4);
            }
          }
        }

        // Second pass: Remove only white background while preserving all product colors
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const pixelIndex = i / 4;

          // Calculate color properties
          const brightness = (r + g + b) / 3;

          // Check if pixel is near edges (preserve product boundaries)
          const isNearEdge = edgePixels.has(pixelIndex);

          // Only remove pure white or near-white pixels that aren't edges
          const isPureWhite = (
            r > 250 &&
            g > 250 &&
            b > 250 &&
            Math.abs(r - g) < 8 &&
            Math.abs(g - b) < 8 &&
            !isNearEdge
          );

          // Remove background if it's pure white and not an edge
          if (isPureWhite) {
            data[i + 3] = 0; // Make fully transparent
          } else if (brightness > 245 && !isNearEdge) {
            // For very bright but not pure white, reduce opacity slightly
            data[i + 3] = Math.max(0, data[i + 3] - 30);
          }
        }

        // Put the modified image data back
        ctx.putImageData(imageData, 0, 0);

        // Convert to data URL
        const processedImageUrl = canvas.toDataURL('image/png');
        resolve(processedImageUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageSrc;
  });
};

/**
 * Check if a pixel is white or very close to white
 * @param {number} r - Red value (0-255)
 * @param {number} g - Green value (0-255)
 * @param {number} b - Blue value (0-255)
 * @param {number} tolerance - Tolerance for white detection
 * @returns {boolean} - True if pixel is considered white
 */
const isWhitePixel = (r, g, b, tolerance) => {
  // Pure white check
  if (r === 255 && g === 255 && b === 255) {
    return true;
  }

  // Near-white check with tolerance
  // Only consider pixels that are very close to pure white
  const whiteThreshold = 255 - tolerance;
  return r >= whiteThreshold && g >= whiteThreshold && b >= whiteThreshold;
};

/**
 * Check if a pixel is part of an edge
 * @param {Uint8ClampedArray} data - Image data
 * @param {number} index - Pixel index
 * @param {number} width - Image width
 * @returns {boolean} - True if pixel is part of an edge
 */
const isEdgePixel = (data, index, width) => {
  const pixelSize = 4;
  const height = data.length / (width * pixelSize);
  const x = (index / pixelSize) % width;
  const y = Math.floor((index / pixelSize) / width);

  // Skip edge pixels of the image
  if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {
    return true;
  }

  // Check surrounding pixels for non-white colors
  const surroundingPixels = [
    data[index - width * pixelSize - pixelSize], // top-left
    data[index - width * pixelSize], // top
    data[index - width * pixelSize + pixelSize], // top-right
    data[index - pixelSize], // left
    data[index + pixelSize], // right
    data[index + width * pixelSize - pixelSize], // bottom-left
    data[index + width * pixelSize], // bottom
    data[index + width * pixelSize + pixelSize] // bottom-right
  ];

  // If any surrounding pixel is not white, this is an edge pixel
  return surroundingPixels.some((pixel, i) => {
    if (pixel === undefined) return false;
    const r = data[index - width * pixelSize - pixelSize + (i * pixelSize)];
    const g = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 1];
    const b = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 2];
    return !isWhitePixel(r, g, b, 5);
  });
};

/**
 * Batch process multiple images to remove white backgrounds
 * @param {string[]} imageSrcs - Array of image source URLs
 * @param {number} tolerance - Color tolerance for white detection
 * @returns {Promise<string[]>} - Array of processed image data URLs
 */
export const batchRemoveWhiteBackground = async (imageSrcs, tolerance = 10) => {
  const promises = imageSrcs.map(src => removeWhiteBackground(src, tolerance));
  return Promise.all(promises);
};

/**
 * Process an image file and return processed data URL
 * @param {File} file - Image file
 * @param {number} tolerance - Color tolerance for white detection
 * @returns {Promise<string>} - Processed image data URL
 */
export const processImageFile = async (file, tolerance = 10) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const processedImage = await removeWhiteBackground(e.target.result, tolerance);
        resolve(processedImage);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};

/**
 * Specialized background removal for watches and bracelets
 * @param {string} imageSrc - Image source URL or data URL
 * @param {string} productType - 'watch' or 'bracelet'
 * @returns {Promise<string>} - Data URL of processed image
 */
export const removeProductBackground = async (imageSrc, productType = 'watch') => {
  try {
    // For watches: use conservative tolerance to preserve dial details
    // For bracelets: use slightly higher tolerance for cleaner edges
    const tolerance = productType === 'watch' ? 2 : 4;

    return await removeWhiteBackground(imageSrc, tolerance);
  } catch (error) {
    console.warn(`Failed to remove background for ${productType}:`, error);
    return imageSrc; // Return original if processing fails
  }
};

/**
 * Check if an image has a white background
 * @param {string} imageSrc - Image source URL
 * @returns {Promise<boolean>} - True if image has significant white background
 */
export const hasWhiteBackground = async (imageSrc) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        let whitePixelCount = 0;
        const totalPixels = data.length / 4;

        for (let i = 0; i < data.length; i += 4) {
          const red = data[i];
          const green = data[i + 1];
          const blue = data[i + 2];

          if (isWhitePixel(red, green, blue, 10)) {
            whitePixelCount++;
          }
        }

        // Consider image to have white background if more than 30% pixels are white
        const whitePercentage = (whitePixelCount / totalPixels) * 100;
        resolve(whitePercentage > 30);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageSrc;
  });
};
