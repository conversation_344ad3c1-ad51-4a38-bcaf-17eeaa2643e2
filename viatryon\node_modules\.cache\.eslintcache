[{"D:\\Via\\test\\viatryon\\src\\index.js": "1", "D:\\Via\\test\\viatryon\\src\\App.js": "2", "D:\\Via\\test\\viatryon\\src\\reportWebVitals.js": "3", "D:\\Via\\test\\viatryon\\src\\components\\Navbar.jsx": "4", "D:\\Via\\test\\viatryon\\src\\components\\DemoForm.jsx": "5", "D:\\Via\\test\\viatryon\\src\\components\\Footer.jsx": "6", "D:\\Via\\test\\viatryon\\src\\pages\\Bracelets.jsx": "7", "D:\\Via\\test\\viatryon\\src\\pages\\HowItWorks.jsx": "8", "D:\\Via\\test\\viatryon\\src\\pages\\Home.jsx": "9", "D:\\Via\\test\\viatryon\\src\\pages\\Watches.jsx": "10", "D:\\Via\\test\\viatryon\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\test\\viatryon\\src\\pages\\VirtualTryOn.jsx": "12", "D:\\Via\\test\\viatryon\\src\\pages\\WhyViaTryon.jsx": "13", "D:\\Via\\test\\viatryon\\src\\pages\\Login.jsx": "14", "D:\\Via\\test\\viatryon\\src\\pages\\Tryon.jsx": "15", "D:\\Via\\test\\viatryon\\src\\pages\\Requirements.jsx": "16", "D:\\Via\\test\\viatryon\\src\\pages\\Contact.jsx": "17", "D:\\Via\\test\\viatryon\\src\\pages\\ProductDetails.jsx": "18", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\AdminDashboard.jsx": "19", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Clients.jsx": "20", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\TryOnAnalytics.jsx": "21", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Settings.jsx": "22", "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientSettings.jsx": "23", "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientDashboard.jsx": "24", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx": "25", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "26", "D:\\Via\\test\\viatryon\\src\\data\\productCollections.js": "27", "D:\\Via\\test\\viatryon\\src\\context\\CartContext.js": "28", "D:\\Via\\test\\viatryon\\src\\utils\\imageLoader.js": "29", "D:\\Via\\test\\viatryon\\src\\components\\EmbedCodeGenerator.jsx": "30", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\Overview.jsx": "31", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx": "32", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ClientPerformance.jsx": "33", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx": "34", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx": "35", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\BehaviorAnalytics.jsx": "36", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "37", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\DeviceStats.jsx": "38", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\Overview.jsx": "39", "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminSidebar.jsx": "40", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\UserEngagement.jsx": "41", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "42", "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminNavbar.jsx": "43", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\GeographicData.jsx": "44", "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientNavbar.jsx": "45", "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientSidebar.jsx": "46", "D:\\Via\\test\\viatryon\\src\\components\\debug\\BackendTest.jsx": "47", "D:\\Via\\test\\viatryon\\src\\utils\\backgroundRemover.js": "48", "D:\\Via\\test\\viatryon\\src\\utils\\uShapeCutter.js": "49"}, {"size": 653, "mtime": 1749456773932, "results": "50", "hashOfConfig": "51"}, {"size": 5382, "mtime": 1749744585299, "results": "52", "hashOfConfig": "51"}, {"size": 362, "mtime": 1746042995461, "results": "53", "hashOfConfig": "51"}, {"size": 14287, "mtime": 1749152911428, "results": "54", "hashOfConfig": "51"}, {"size": 12163, "mtime": 1749747078228, "results": "55", "hashOfConfig": "51"}, {"size": 6479, "mtime": 1748866960444, "results": "56", "hashOfConfig": "51"}, {"size": 18596, "mtime": 1749744585299, "results": "57", "hashOfConfig": "51"}, {"size": 17189, "mtime": 1748864169684, "results": "58", "hashOfConfig": "51"}, {"size": 41162, "mtime": 1748975550119, "results": "59", "hashOfConfig": "51"}, {"size": 18511, "mtime": 1749744585299, "results": "60", "hashOfConfig": "51"}, {"size": 6108, "mtime": 1746344994148, "results": "61", "hashOfConfig": "51"}, {"size": 71395, "mtime": 1749728225541, "results": "62", "hashOfConfig": "51"}, {"size": 16901, "mtime": 1748864143496, "results": "63", "hashOfConfig": "51"}, {"size": 7923, "mtime": 1749663944103, "results": "64", "hashOfConfig": "51"}, {"size": 97759, "mtime": 1749746468769, "results": "65", "hashOfConfig": "51"}, {"size": 7100, "mtime": 1748867056681, "results": "66", "hashOfConfig": "51"}, {"size": 25676, "mtime": 1749747051651, "results": "67", "hashOfConfig": "51"}, {"size": 15030, "mtime": 1749744585299, "results": "68", "hashOfConfig": "51"}, {"size": 23368, "mtime": 1749659851907, "results": "69", "hashOfConfig": "51"}, {"size": 53829, "mtime": 1749747711922, "results": "70", "hashOfConfig": "51"}, {"size": 667, "mtime": 1749456774086, "results": "71", "hashOfConfig": "51"}, {"size": 12623, "mtime": 1749650927488, "results": "72", "hashOfConfig": "51"}, {"size": 11461, "mtime": 1749741582394, "results": "73", "hashOfConfig": "51"}, {"size": 17692, "mtime": 1749740990270, "results": "74", "hashOfConfig": "51"}, {"size": 5020, "mtime": 1749639557515, "results": "75", "hashOfConfig": "51"}, {"size": 2932, "mtime": 1749671240558, "results": "76", "hashOfConfig": "51"}, {"size": 10601, "mtime": 1748277235110, "results": "77", "hashOfConfig": "51"}, {"size": 4297, "mtime": 1748283089634, "results": "78", "hashOfConfig": "51"}, {"size": 8174, "mtime": 1748283061371, "results": "79", "hashOfConfig": "51"}, {"size": 14626, "mtime": 1749459857105, "results": "80", "hashOfConfig": "51"}, {"size": 20917, "mtime": 1749640743996, "results": "81", "hashOfConfig": "51"}, {"size": 14795, "mtime": 1749645956053, "results": "82", "hashOfConfig": "51"}, {"size": 11229, "mtime": 1749639963466, "results": "83", "hashOfConfig": "51"}, {"size": 15809, "mtime": 1749648036920, "results": "84", "hashOfConfig": "51"}, {"size": 13744, "mtime": 1749640585904, "results": "85", "hashOfConfig": "51"}, {"size": 13635, "mtime": 1749647517295, "results": "86", "hashOfConfig": "51"}, {"size": 9056, "mtime": 1749739554342, "results": "87", "hashOfConfig": "51"}, {"size": 11669, "mtime": 1749670101992, "results": "88", "hashOfConfig": "51"}, {"size": 9424, "mtime": 1749739552961, "results": "89", "hashOfConfig": "51"}, {"size": 8197, "mtime": 1749490264606, "results": "90", "hashOfConfig": "51"}, {"size": 12795, "mtime": 1749670101992, "results": "91", "hashOfConfig": "51"}, {"size": 11305, "mtime": 1749739340824, "results": "92", "hashOfConfig": "51"}, {"size": 12153, "mtime": 1749490264599, "results": "93", "hashOfConfig": "51"}, {"size": 8780, "mtime": 1749656007836, "results": "94", "hashOfConfig": "51"}, {"size": 10137, "mtime": 1749659801764, "results": "95", "hashOfConfig": "51"}, {"size": 8611, "mtime": 1749740990270, "results": "96", "hashOfConfig": "51"}, {"size": 3725, "mtime": 1749659859206, "results": "97", "hashOfConfig": "51"}, {"size": 9454, "mtime": 1749742369114, "results": "98", "hashOfConfig": "51"}, {"size": 2855, "mtime": 1749746624591, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "snlcfk", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Via\\test\\viatryon\\src\\index.js", [], [], "D:\\Via\\test\\viatryon\\src\\App.js", ["247"], [], "D:\\Via\\test\\viatryon\\src\\reportWebVitals.js", [], [], "D:\\Via\\test\\viatryon\\src\\components\\Navbar.jsx", ["248"], [], "D:\\Via\\test\\viatryon\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\components\\Footer.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Bracelets.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Home.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Watches.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\VirtualTryOn.jsx", ["249", "250"], [], "D:\\Via\\test\\viatryon\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Login.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Tryon.jsx", ["251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263"], [], "D:\\Via\\test\\viatryon\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\AdminDashboard.jsx", ["264", "265", "266"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Clients.jsx", ["267", "268", "269", "270", "271", "272", "273", "274"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Settings.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientSettings.jsx", ["275"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientDashboard.jsx", ["276", "277", "278", "279"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\data\\productCollections.js", [], [], "D:\\Via\\test\\viatryon\\src\\context\\CartContext.js", [], [], "D:\\Via\\test\\viatryon\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\test\\viatryon\\src\\components\\EmbedCodeGenerator.jsx", ["280", "281", "282"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\Overview.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx", ["283", "284", "285"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ClientPerformance.jsx", ["286", "287", "288", "289", "290", "291", "292"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx", ["293"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\BehaviorAnalytics.jsx", ["294", "295", "296"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["297", "298", "299", "300", "301"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\DeviceStats.jsx", ["302", "303", "304", "305", "306", "307", "308", "309"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\Overview.jsx", ["310", "311"], [], "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminSidebar.jsx", ["312"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["313", "314"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["315"], [], "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminNavbar.jsx", ["316"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\GeographicData.jsx", ["317"], [], "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientNavbar.jsx", ["318"], [], "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientSidebar.jsx", ["319"], [], "D:\\Via\\test\\viatryon\\src\\components\\debug\\BackendTest.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\utils\\backgroundRemover.js", ["320"], [], "D:\\Via\\test\\viatryon\\src\\utils\\uShapeCutter.js", [], [], {"ruleId": "321", "severity": 1, "message": "322", "line": 5, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 14}, {"ruleId": "321", "severity": 1, "message": "325", "line": 37, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 37, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "326", "line": 260, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 260, "endColumn": 25}, {"ruleId": "327", "severity": 1, "message": "328", "line": 880, "column": 6, "nodeType": "329", "endLine": 880, "endColumn": 55, "suggestions": "330"}, {"ruleId": "321", "severity": 1, "message": "331", "line": 195, "column": 26, "nodeType": "323", "messageId": "324", "endLine": 195, "endColumn": 43}, {"ruleId": "321", "severity": 1, "message": "332", "line": 467, "column": 17, "nodeType": "323", "messageId": "324", "endLine": 467, "endColumn": 25}, {"ruleId": "327", "severity": 1, "message": "333", "line": 544, "column": 6, "nodeType": "329", "endLine": 544, "endColumn": 17, "suggestions": "334"}, {"ruleId": "321", "severity": 1, "message": "335", "line": 595, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 595, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "336", "line": 596, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 596, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "337", "line": 597, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 597, "endColumn": 26}, {"ruleId": "321", "severity": 1, "message": "326", "line": 668, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 668, "endColumn": 25}, {"ruleId": "321", "severity": 1, "message": "338", "line": 1283, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 1283, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "339", "line": 1329, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 1329, "endColumn": 19}, {"ruleId": "321", "severity": 1, "message": "340", "line": 1338, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 1338, "endColumn": 20}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1552, "column": 6, "nodeType": "329", "endLine": 1552, "endColumn": 55, "suggestions": "341"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1616, "column": 6, "nodeType": "329", "endLine": 1616, "endColumn": 55, "suggestions": "342"}, {"ruleId": "321", "severity": 1, "message": "343", "line": 1942, "column": 25, "nodeType": "323", "messageId": "324", "endLine": 1942, "endColumn": 41}, {"ruleId": "321", "severity": 1, "message": "344", "line": 5, "column": 27, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 35}, {"ruleId": "321", "severity": 1, "message": "345", "line": 5, "column": 37, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 40}, {"ruleId": "321", "severity": 1, "message": "346", "line": 19, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 19, "endColumn": 25}, {"ruleId": "321", "severity": 1, "message": "347", "line": 6, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 6, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "348", "line": 6, "column": 36, "nodeType": "323", "messageId": "324", "endLine": 6, "endColumn": 41}, {"ruleId": "321", "severity": 1, "message": "349", "line": 8, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 11}, {"ruleId": "321", "severity": 1, "message": "350", "line": 8, "column": 13, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 19}, {"ruleId": "321", "severity": 1, "message": "351", "line": 8, "column": 21, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 24}, {"ruleId": "321", "severity": 1, "message": "352", "line": 8, "column": 26, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 32}, {"ruleId": "321", "severity": 1, "message": "353", "line": 8, "column": 34, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 46}, {"ruleId": "327", "severity": 1, "message": "354", "line": 66, "column": 6, "nodeType": "329", "endLine": 66, "endColumn": 35, "suggestions": "355"}, {"ruleId": "321", "severity": 1, "message": "356", "line": 4, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 16}, {"ruleId": "321", "severity": 1, "message": "357", "line": 7, "column": 15, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 25}, {"ruleId": "321", "severity": 1, "message": "348", "line": 7, "column": 47, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 52}, {"ruleId": "321", "severity": 1, "message": "358", "line": 7, "column": 54, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 64}, {"ruleId": "321", "severity": 1, "message": "359", "line": 21, "column": 16, "nodeType": "323", "messageId": "324", "endLine": 21, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "360", "line": 3, "column": 23, "nodeType": "323", "messageId": "324", "endLine": 3, "endColumn": 27}, {"ruleId": "361", "severity": 1, "message": "362", "line": 295, "column": 31, "nodeType": "363", "endLine": 295, "endColumn": 65}, {"ruleId": "361", "severity": 1, "message": "362", "line": 296, "column": 21, "nodeType": "363", "endLine": 296, "endColumn": 60}, {"ruleId": "321", "severity": 1, "message": "364", "line": 14, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "357", "line": 19, "column": 22, "nodeType": "323", "messageId": "324", "endLine": 19, "endColumn": 32}, {"ruleId": "321", "severity": 1, "message": "365", "line": 19, "column": 46, "nodeType": "323", "messageId": "324", "endLine": 19, "endColumn": 53}, {"ruleId": "321", "severity": 1, "message": "366", "line": 11, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 11, "endColumn": 11}, {"ruleId": "321", "severity": 1, "message": "367", "line": 12, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 12, "endColumn": 6}, {"ruleId": "321", "severity": 1, "message": "368", "line": 13, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 13, "endColumn": 7}, {"ruleId": "321", "severity": 1, "message": "369", "line": 14, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 12}, {"ruleId": "321", "severity": 1, "message": "370", "line": 15, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 15, "endColumn": 7}, {"ruleId": "321", "severity": 1, "message": "364", "line": 16, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 16, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "357", "line": 19, "column": 22, "nodeType": "323", "messageId": "324", "endLine": 19, "endColumn": 32}, {"ruleId": "321", "severity": 1, "message": "357", "line": 16, "column": 22, "nodeType": "323", "messageId": "324", "endLine": 16, "endColumn": 32}, {"ruleId": "321", "severity": 1, "message": "371", "line": 4, "column": 17, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "372", "line": 4, "column": 25, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 29}, {"ruleId": "321", "severity": 1, "message": "373", "line": 4, "column": 31, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 37}, {"ruleId": "321", "severity": 1, "message": "369", "line": 6, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 6, "endColumn": 12}, {"ruleId": "321", "severity": 1, "message": "370", "line": 7, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 7}, {"ruleId": "321", "severity": 1, "message": "364", "line": 12, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 12, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "357", "line": 15, "column": 26, "nodeType": "323", "messageId": "324", "endLine": 15, "endColumn": 36}, {"ruleId": "321", "severity": 1, "message": "374", "line": 94, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 94, "endColumn": 26}, {"ruleId": "321", "severity": 1, "message": "344", "line": 7, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 11}, {"ruleId": "321", "severity": 1, "message": "345", "line": 8, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 6}, {"ruleId": "321", "severity": 1, "message": "375", "line": 9, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 9, "endColumn": 8}, {"ruleId": "321", "severity": 1, "message": "376", "line": 10, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 10, "endColumn": 8}, {"ruleId": "321", "severity": 1, "message": "377", "line": 11, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 11, "endColumn": 16}, {"ruleId": "321", "severity": 1, "message": "369", "line": 15, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 15, "endColumn": 12}, {"ruleId": "321", "severity": 1, "message": "370", "line": 16, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 16, "endColumn": 7}, {"ruleId": "321", "severity": 1, "message": "378", "line": 121, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 121, "endColumn": 21}, {"ruleId": "321", "severity": 1, "message": "379", "line": 14, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 13}, {"ruleId": "321", "severity": 1, "message": "357", "line": 14, "column": 22, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 32}, {"ruleId": "321", "severity": 1, "message": "380", "line": 11, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 11, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "369", "line": 4, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 12}, {"ruleId": "321", "severity": 1, "message": "370", "line": 5, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 7}, {"ruleId": "321", "severity": 1, "message": "364", "line": 12, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 12, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "380", "line": 14, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "364", "line": 9, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 9, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "380", "line": 10, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 10, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "381", "line": 1, "column": 17, "nodeType": "323", "messageId": "324", "endLine": 1, "endColumn": 25}, {"ruleId": "321", "severity": 1, "message": "382", "line": 145, "column": 7, "nodeType": "323", "messageId": "324", "endLine": 145, "endColumn": 18}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'isClient' is assigned a value but never used.", "'getWatchPosition' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleCapture'. Either include it or remove the dependency array.", "ArrayExpression", ["383"], "'setQualityMetrics' is assigned a value but never used.", "'imageUrl' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'endAnalyticsSession', 'initializeEnhancedTracking', and 'startAnalyticsSession'. Either include them or remove the dependency array.", ["384"], "'MIN_WRIST_SIZE' is assigned a value but never used.", "'MAX_WRIST_SIZE' is assigned a value but never used.", "'ASSUMED_DIAL_SIZE' is assigned a value but never used.", "'takeScreenshot' is assigned a value but never used.", "'handleZoom' is assigned a value but never used.", "'handleShare' is assigned a value but never used.", ["385"], ["386"], "'defaultWristSize' is assigned a value but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'businessMetrics' is assigned a value but never used.", "'Search' is defined but never used.", "'Globe' is defined but never used.", "'Calendar' is defined but never used.", "'Target' is defined but never used.", "'Zap' is defined but never used.", "'MapPin' is defined but never used.", "'ChevronRight' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchClients'. Either include it or remove the dependency array.", ["387"], "'motion' is defined but never used.", "'TrendingUp' is defined but never used.", "'Smartphone' is defined but never used.", "'setUser' is assigned a value but never used.", "'Code' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Legend' is defined but never used.", "'Monitor' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Camera' is defined but never used.", "'Hand' is defined but never used.", "'Layers' is defined but never used.", "'totalInteractions' is assigned a value but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'deviceTrends' is assigned a value but never used.", "'Eye' is defined but never used.", "'loading' is assigned a value but never used.", "'useState' is defined but never used.", "'isEdgePixel' is assigned a value but never used.", {"desc": "388", "fix": "389"}, {"desc": "390", "fix": "391"}, {"desc": "388", "fix": "392"}, {"desc": "388", "fix": "393"}, {"desc": "394", "fix": "395"}, "Update the dependencies array to be: [isCountdownActive, isCaptured, isHandInPosition, handleCapture]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [endAnalyticsSession, initializeEnhancedTracking, startAnalyticsSession, urlParams]", {"range": "398", "text": "399"}, {"range": "400", "text": "397"}, {"range": "401", "text": "397"}, "Update the dependencies array to be: [fetchClients, searchQuery, selectedStatus]", {"range": "402", "text": "403"}, [29203, 29252], "[isCountdownActive, isCaptured, isHandInPosition, handleCapture]", [16596, 16607], "[endAnalyticsSession, initializeEnhancedTracking, startAnalyticsSession, urlParams]", [50236, 50285], [52176, 52225], [2397, 2426], "[fetchClients, searchQuery, selectedStatus]"]