{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code, X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity, Calendar, Target, Zap, MapPin, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  var _uniqueUsersData$summ2, _uniqueUsersData$summ3, _selectedClientForDet, _clientAnalytics$time, _clientAnalytics$time2, _clientAnalytics$time3, _clientAnalytics$time4, _clientAnalytics$time5, _clientAnalytics$time6, _clientAnalytics$prod, _clientAnalytics$devi, _clientAnalytics$time7, _clientAnalytics$time8;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n  const fetchClients = async () => {\n    try {\n      var _clientsData$stats, _clientsData$stats2, _clientsData$stats3;\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([fetch(`${apiUrl}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        var _uniqueUsersData$summ;\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = ((_uniqueUsersData$summ = uniqueUsersData.summary) === null || _uniqueUsersData$summ === void 0 ? void 0 : _uniqueUsersData$summ.totalUniqueUsers) || 0;\n      }\n      setStats({\n        newClientsThisMonth: ((_clientsData$stats = clientsData.stats) === null || _clientsData$stats === void 0 ? void 0 : _clientsData$stats.newClientsThisMonth) || 0,\n        activeRate: ((_clientsData$stats2 = clientsData.stats) === null || _clientsData$stats2 === void 0 ? void 0 : _clientsData$stats2.activeRate) || 0,\n        tryOnsGrowth: ((_clientsData$stats3 = clientsData.stats) === null || _clientsData$stats3 === void 0 ? void 0 : _clientsData$stats3.tryOnsGrowth) || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = date => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n  const handleAddClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteClient = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openEditModal = client => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '',\n      // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async clientId => {\n    try {\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n      const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      const timeData = timeAnalysisResponse.ok ? await timeAnalysisResponse.json() : null;\n      const productData = productPerformanceResponse.ok ? await productPerformanceResponse.json() : null;\n      const deviceData = deviceStatsResponse.ok ? await deviceStatsResponse.json() : null;\n      setClientAnalytics({\n        timeAnalysis: timeData,\n        productPerformance: productData,\n        deviceStats: deviceData\n      });\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = client => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = client => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = client => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '42', productType = 'watches') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('YOUR_PRODUCT_IMAGE_URL', '42', 'watches')\"\n  style=\"\n    background-color: #2D8C88;\n    color: white;\n    border: none;\n    padding: 12px 24px;\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='#236b68'\"\n  onmouseout=\"this.style.backgroundColor='#2D8C88'\"\n>\n  Try On Virtually\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: openAddModal,\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [\"+\", stats.newClientsThisMonth, \" new\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [stats.activeRate.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics;\n                    return sum + (((_c$analytics = c.analytics) === null || _c$analytics === void 0 ? void 0 : _c$analytics.totalSessions) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.tryOnsGrowth >= 0 ? '+' : '', stats.tryOnsGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users (by IP)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : stats.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Activity, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-blue-600\",\n                children: [(uniqueUsersData === null || uniqueUsersData === void 0 ? void 0 : (_uniqueUsersData$summ2 = uniqueUsersData.summary) === null || _uniqueUsersData$summ2 === void 0 ? void 0 : (_uniqueUsersData$summ3 = _uniqueUsersData$summ2.avgSessionsPerUser) === null || _uniqueUsersData$summ3 === void 0 ? void 0 : _uniqueUsersData$summ3.toFixed(1)) || '0', \" avg sessions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"per user\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showModal && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: editingClient ? 'Edit Client' : 'Add New Client'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowModal(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: editingClient ? handleEditClient : handleAddClient,\n                  className: \"space-y-5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Company Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"companyName\",\n                        value: clientForm.companyName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter company name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Contact Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"contactName\",\n                        value: clientForm.contactName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter contact name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 586,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"email\",\n                        name: \"email\",\n                        value: clientForm.email,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter email address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Phone\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 612,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        name: \"phone\",\n                        value: clientForm.phone,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter phone number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Website\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"url\",\n                        name: \"website\",\n                        value: clientForm.website,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"https://example.com\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Industry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 637,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"industry\",\n                        value: clientForm.industry,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"e.g., Fashion, Jewelry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 638,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Product Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"productType\",\n                        value: clientForm.productType,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"watches\",\n                          children: \"Watches\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 658,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"bracelets\",\n                          children: \"Bracelets\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 659,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"both\",\n                          children: \"Both\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 660,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Subscription Plan\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 664,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"subscriptionPlan\",\n                        value: clientForm.subscriptionPlan,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"basic\",\n                          children: \"Basic\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 671,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"premium\",\n                          children: \"Premium\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 672,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"enterprise\",\n                          children: \"Enterprise\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 673,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 23\n                  }, this), !editingClient && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"password\",\n                        value: clientForm.password,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 682,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: handleSuggestPassword,\n                        className: \"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\",\n                        children: \"Generate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-end space-x-3 pt-4 border-t border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowModal(false),\n                      className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"submit\",\n                      className: \"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\",\n                      children: editingClient ? 'Update Client' : 'Create Client'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 711,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Conversion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 21\n                }, this) : error ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-red-600\",\n                    children: [\"Error loading clients: \", error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this) : clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-gray-500\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 21\n                }, this) : clients.map(client => {\n                  var _client$companyName, _client$analytics, _client$analytics$tot, _client$analytics2, _client$analytics3, _client$analytics3$to, _client$analytics4, _client$analytics5, _client$analytics6, _client$analytics6$re, _client$analytics7;\n                  return /*#__PURE__*/_jsxDEV(motion.tr, {\n                    initial: {\n                      opacity: 0\n                    },\n                    animate: {\n                      opacity: 1\n                    },\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 h-10 w-10\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                            children: ((_client$companyName = client.companyName) === null || _client$companyName === void 0 ? void 0 : _client$companyName.charAt(0)) || 'C'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 795,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 794,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"ml-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: client.companyName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: client.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500 lg:hidden\",\n                            children: [((_client$analytics = client.analytics) === null || _client$analytics === void 0 ? void 0 : (_client$analytics$tot = _client$analytics.totalSessions) === null || _client$analytics$tot === void 0 ? void 0 : _client$analytics$tot.toLocaleString()) || '0', \" try-ons \\u2022 \", ((_client$analytics2 = client.analytics) === null || _client$analytics2 === void 0 ? void 0 : _client$analytics2.conversionRate) || '0', \"% conversion\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 802,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 799,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 792,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: ((_client$analytics3 = client.analytics) === null || _client$analytics3 === void 0 ? void 0 : (_client$analytics3$to = _client$analytics3.totalSessions) === null || _client$analytics3$to === void 0 ? void 0 : _client$analytics3$to.toLocaleString()) || '0'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 809,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics4 = client.analytics) === null || _client$analytics4 === void 0 ? void 0 : _client$analytics4.productCount) || '0', \" products\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 810,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 808,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [((_client$analytics5 = client.analytics) === null || _client$analytics5 === void 0 ? void 0 : _client$analytics5.conversionRate) || '0', \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\"$\", ((_client$analytics6 = client.analytics) === null || _client$analytics6 === void 0 ? void 0 : (_client$analytics6$re = _client$analytics6.revenue) === null || _client$analytics6$re === void 0 ? void 0 : _client$analytics6$re.toLocaleString()) || '0', \" revenue\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 814,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                          children: client.subscriptionStatus\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 818,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatLastActive((_client$analytics7 = client.analytics) === null || _client$analytics7 === void 0 ? void 0 : _client$analytics7.lastActive)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 825,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 817,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' : client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n                        children: client.subscriptionPlan\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 829,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\",\n                          onClick: () => handleViewDetails(client),\n                          title: \"View Details\",\n                          children: /*#__PURE__*/_jsxDEV(Eye, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 843,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 838,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\",\n                          onClick: () => handleViewCode(client),\n                          title: \"Integration Code\",\n                          children: /*#__PURE__*/_jsxDEV(Code, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 850,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 845,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\",\n                          onClick: () => openEditModal(client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 857,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors\",\n                          onClick: () => handleDeleteClient(client._id),\n                          title: \"Delete Client\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 864,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 859,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 25\n                    }, this)]\n                  }, client._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showDetailsPopup && selectedClientForDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold\",\n                      children: ((_selectedClientForDet = selectedClientForDetails.companyName) === null || _selectedClientForDet === void 0 ? void 0 : _selectedClientForDet.charAt(0)) || 'C'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 895,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: selectedClientForDetails.companyName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 899,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/80 text-sm\",\n                        children: selectedClientForDetails.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowDetailsPopup(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\",\n                children: loadingAnalytics ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center py-12\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Users, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 929,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 928,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-blue-600\",\n                            children: \"Total Sessions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 932,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-blue-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time === void 0 ? void 0 : (_clientAnalytics$time2 = _clientAnalytics$time.dailyTrends) === null || _clientAnalytics$time2 === void 0 ? void 0 : _clientAnalytics$time2.reduce((sum, day) => sum + (day.sessions || 0), 0)) || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 933,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 931,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 926,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Clock, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 943,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 942,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-green-600\",\n                            children: \"Avg Duration\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 946,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-green-900\",\n                            children: [Math.round((clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time3 = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time3 === void 0 ? void 0 : (_clientAnalytics$time4 = _clientAnalytics$time3.dailyTrends) === null || _clientAnalytics$time4 === void 0 ? void 0 : _clientAnalytics$time4.reduce((sum, day) => sum + (day.avgDuration || 0), 0)) / ((clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time5 = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time5 === void 0 ? void 0 : (_clientAnalytics$time6 = _clientAnalytics$time5.dailyTrends) === null || _clientAnalytics$time6 === void 0 ? void 0 : _clientAnalytics$time6.length) || 1)) || 0, \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 947,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 945,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 941,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 940,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 957,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 956,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-purple-600\",\n                            children: \"Products\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 960,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-purple-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$prod = clientAnalytics.productPerformance) === null || _clientAnalytics$prod === void 0 ? void 0 : _clientAnalytics$prod.length) || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 961,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 959,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Smartphone, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 971,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 970,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-orange-600\",\n                            children: \"Devices\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 974,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-orange-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$devi = clientAnalytics.deviceStats) === null || _clientAnalytics$devi === void 0 ? void 0 : _clientAnalytics$devi.length) || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 975,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 973,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 969,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 968,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Activity, {\n                        className: \"h-5 w-5 mr-2 text-[#2D8C88]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 29\n                      }, this), \"Recent Activity\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$time7 = clientAnalytics.timeAnalysis) === null || _clientAnalytics$time7 === void 0 ? void 0 : (_clientAnalytics$time8 = _clientAnalytics$time7.dailyTrends) === null || _clientAnalytics$time8 === void 0 ? void 0 : _clientAnalytics$time8.slice(-5).map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-2 h-2 rounded-full bg-[#2D8C88]\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 993,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: new Date(day.date).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 994,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 992,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600\",\n                          children: [day.sessions, \" sessions \\u2022 \", Math.round(day.avgDuration || 0), \"s avg\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 998,\n                          columnNumber: 33\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 31\n                      }, this))) || /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-500 text-center py-4\",\n                        children: \"No recent activity data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1003,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 25\n                  }, this), (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.deviceStats) && clientAnalytics.deviceStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Monitor, {\n                        className: \"h-5 w-5 mr-2 text-[#2D8C88]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1012,\n                        columnNumber: 31\n                      }, this), \"Device Usage\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                      children: clientAnalytics.deviceStats.map((device, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-4\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                            children: device.device || 'Unknown'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1019,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [device.sessions, \" sessions\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1022,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1018,\n                          columnNumber: 35\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1017,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1015,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showCodePopup && selectedClientForCode && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Code, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1059,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1058,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Integration Code\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1062,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/80 text-sm\",\n                        children: selectedClientForCode.companyName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1065,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1057,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowCodePopup(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1074,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-blue-900 mb-2\",\n                      children: \"How to integrate ViatrOn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1084,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2 text-sm text-blue-800\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"1. Copy the code below and paste it into your website's HTML\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1088,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1089,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"3. Customize the button styling to match your brand\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1090,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"4. Test the integration to ensure it works correctly\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1091,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1087,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Integration Code\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1098,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: copyCodeToClipboard,\n                        className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\",\n                        children: copiedCode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Check, {\n                            className: \"h-4 w-4 mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1107,\n                            columnNumber: 33\n                          }, this), \"Copied!\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Copy, {\n                            className: \"h-4 w-4 mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1112,\n                            columnNumber: 33\n                          }, this), \"Copy Code\"]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1101,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gray-900 rounded-xl p-4 overflow-x-auto\",\n                      children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"text-green-400 text-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"code\", {\n                          children: generateIntegrationCode(selectedClientForCode)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1121,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1120,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-3\",\n                      children: \"Example Usage\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1128,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 border\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-700 mb-2\",\n                          children: \"For Watches:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1133,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                          className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                          children: \"openViaTryon('https://example.com/watch.jpg', '42', 'watches')\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1134,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1132,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 border\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-700 mb-2\",\n                          children: \"For Bracelets:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1139,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                          className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                          children: \"openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1140,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1138,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1131,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-yellow-900 mb-2\",\n                      children: \"Need Help?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1149,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-yellow-800\",\n                      children: \"If you need assistance with the integration, please contact our support team or refer to our documentation.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1152,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 426,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"NgHDF/gBWGZiCGjjd7AfdbBn2j8=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "AnimatePresence", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "X", "Copy", "Check", "BarChart3", "Clock", "Smartphone", "Monitor", "Activity", "Calendar", "Target", "Zap", "MapPin", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "_uniqueUsersData$summ2", "_uniqueUsersData$summ3", "_selectedClientForDet", "_clientAnalytics$time", "_clientAnalytics$time2", "_clientAnalytics$time3", "_clientAnalytics$time4", "_clientAnalytics$time5", "_clientAnalytics$time6", "_clientAnalytics$prod", "_clientAnalytics$devi", "_clientAnalytics$time7", "_clientAnalytics$time8", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "editingClient", "setEditingClient", "clients", "setClients", "loading", "setLoading", "error", "setError", "stats", "setStats", "newClientsThisMonth", "activeRate", "tryOnsGrowth", "uniqueUsers", "uniqueUsersData", "setUniqueUsersData", "showDetailsPopup", "setShowDetailsPopup", "showCodePopup", "setShowCodePopup", "selectedClientForDetails", "setSelectedClientForDetails", "selectedClientForCode", "setSelectedClientForCode", "clientAnalytics", "setClientAnalytics", "loadingAnalytics", "setLoadingAnalytics", "copiedCode", "setCopiedCode", "clientForm", "setClientForm", "companyName", "contactName", "website", "email", "phone", "industry", "productType", "subscriptionPlan", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "fetchClients", "_clientsData$stats", "_clientsData$stats2", "_clientsData$stats3", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "params", "URLSearchParams", "append", "clientsResponse", "uniqueUsersResponse", "Promise", "all", "fetch", "headers", "ok", "errorData", "json", "message", "clientsData", "uniqueUsersCount", "_uniqueUsersData$summ", "summary", "totalUniqueUsers", "err", "console", "formatLastActive", "date", "now", "Date", "lastActive", "diffInHours", "diffInDays", "diffInWeeks", "handleFormChange", "e", "name", "value", "target", "prev", "handleSuggestPassword", "resetForm", "handleAddClient", "preventDefault", "response", "method", "body", "JSON", "stringify", "handleEditClient", "_id", "handleDeleteClient", "clientId", "window", "confirm", "openEditModal", "client", "openAddModal", "fetchClientAnalytics", "end", "start", "setDate", "getDate", "timeAnalysisResponse", "productPerformanceResponse", "deviceStatsResponse", "toISOString", "timeData", "productData", "deviceData", "timeAnalysis", "productPerformance", "deviceStats", "handleViewDetails", "handleViewCode", "generateIntegrationCode", "REACT_APP_FRONTEND_URL", "location", "origin", "copyCodeToClipboard", "code", "navigator", "clipboard", "writeText", "then", "setTimeout", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "filter", "c", "subscriptionStatus", "toFixed", "reduce", "sum", "_c$analytics", "analytics", "totalSessions", "toLocaleString", "avgSessionsPerUser", "exit", "scale", "onSubmit", "type", "onChange", "required", "placeholder", "colSpan", "map", "_client$companyName", "_client$analytics", "_client$analytics$tot", "_client$analytics2", "_client$analytics3", "_client$analytics3$to", "_client$analytics4", "_client$analytics5", "_client$analytics6", "_client$analytics6$re", "_client$analytics7", "tr", "conversionRate", "productCount", "revenue", "title", "dailyTrends", "day", "sessions", "round", "avgDuration", "index", "toLocaleDateString", "device", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code,\n  X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity,\n  Calendar, Target, Zap, MapPin, ChevronRight\n} from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients?${params}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = uniqueUsersData.summary?.totalUniqueUsers || 0;\n      }\n\n      setStats({\n        newClientsThisMonth: clientsData.stats?.newClientsThisMonth || 0,\n        activeRate: clientsData.stats?.activeRate || 0,\n        tryOnsGrowth: clientsData.stats?.tryOnsGrowth || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = (date) => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n\n  const handleAddClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEditClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClient = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const openEditModal = (client) => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '', // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async (clientId) => {\n    try {\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n\n      const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      const timeData = timeAnalysisResponse.ok ? await timeAnalysisResponse.json() : null;\n      const productData = productPerformanceResponse.ok ? await productPerformanceResponse.json() : null;\n      const deviceData = deviceStatsResponse.ok ? await deviceStatsResponse.json() : null;\n\n      setClientAnalytics({\n        timeAnalysis: timeData,\n        productPerformance: productData,\n        deviceStats: deviceData\n      });\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = (client) => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = (client) => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = (client) => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '42', productType = 'watches') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('YOUR_PRODUCT_IMAGE_URL', '42', 'watches')\"\n  style=\"\n    background-color: #2D8C88;\n    color: white;\n    border: none;\n    padding: 12px 24px;\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='#236b68'\"\n  onmouseout=\"this.style.backgroundColor='#2D8C88'\"\n>\n  Try On Virtually\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={openAddModal}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+{stats.newClientsThisMonth} new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">{stats.activeRate.toFixed(1)}%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users (by IP)</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : stats.uniqueUsers.toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\n                  <Activity className=\"h-6 w-6 text-purple-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-blue-600\">\n                  {uniqueUsersData?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} avg sessions\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">per user</span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add/Edit Client Modal */}\n          <AnimatePresence>\n            {showModal && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <h2 className=\"text-xl font-bold text-white\">\n                        {editingClient ? 'Edit Client' : 'Add New Client'}\n                      </h2>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowModal(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Form */}\n                  <div className=\"p-6\">\n                    <form onSubmit={editingClient ? handleEditClient : handleAddClient} className=\"space-y-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Company Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"companyName\"\n                            value={clientForm.companyName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter company name\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Contact Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"contactName\"\n                            value={clientForm.contactName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter contact name\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Email</label>\n                          <input\n                            type=\"email\"\n                            name=\"email\"\n                            value={clientForm.email}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter email address\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Phone</label>\n                          <input\n                            type=\"tel\"\n                            name=\"phone\"\n                            value={clientForm.phone}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter phone number\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Website</label>\n                          <input\n                            type=\"url\"\n                            name=\"website\"\n                            value={clientForm.website}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"https://example.com\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Industry</label>\n                          <input\n                            type=\"text\"\n                            name=\"industry\"\n                            value={clientForm.industry}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"e.g., Fashion, Jewelry\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Product Type</label>\n                          <select\n                            name=\"productType\"\n                            value={clientForm.productType}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"watches\">Watches</option>\n                            <option value=\"bracelets\">Bracelets</option>\n                            <option value=\"both\">Both</option>\n                          </select>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Subscription Plan</label>\n                          <select\n                            name=\"subscriptionPlan\"\n                            value={clientForm.subscriptionPlan}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"basic\">Basic</option>\n                            <option value=\"premium\">Premium</option>\n                            <option value=\"enterprise\">Enterprise</option>\n                          </select>\n                        </div>\n                      </div>\n\n                      {!editingClient && (\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Password</label>\n                          <div className=\"flex gap-3\">\n                            <input\n                              type=\"text\"\n                              name=\"password\"\n                              value={clientForm.password}\n                              onChange={handleFormChange}\n                              required\n                              className=\"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                              placeholder=\"Enter password\"\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={handleSuggestPassword}\n                              className=\"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\"\n                            >\n                              Generate\n                            </button>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Action Buttons */}\n                      <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-100\">\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowModal(false)}\n                          className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                        >\n                          Cancel\n                        </button>\n                        <button\n                          type=\"submit\"\n                          className=\"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\"\n                        >\n                          {editingClient ? 'Update Client' : 'Create Client'}\n                        </button>\n                      </div>\n                    </form>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Conversion</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {loading ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center\">\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"></div>\n                      </td>\n                    </tr>\n                  ) : error ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-red-600\">\n                        Error loading clients: {error}\n                      </td>\n                    </tr>\n                  ) : clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-gray-500\">\n                        No clients found\n                      </td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <motion.tr\n                        key={client._id}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"hover:bg-gray-50\"\n                      >\n                        <td className=\"px-4 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                                {client.companyName?.charAt(0) || 'C'}\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{client.companyName}</div>\n                              <div className=\"text-sm text-gray-500\">{client.email}</div>\n                              <div className=\"text-sm text-gray-500 lg:hidden\">\n                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.conversionRate || '0'}% conversion\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.productCount || '0'} products</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.conversionRate || '0'}%</div>\n                          <div className=\"text-sm text-gray-500\">${client.analytics?.revenue?.toLocaleString() || '0'} revenue</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                              'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {client.subscriptionStatus}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{formatLastActive(client.analytics?.lastActive)}</span>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {client.subscriptionPlan}\n                          </span>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex justify-end space-x-2\">\n                            <button\n                              className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\"\n                              onClick={() => handleViewDetails(client)}\n                              title=\"View Details\"\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\"\n                              onClick={() => handleViewCode(client)}\n                              title=\"Integration Code\"\n                            >\n                              <Code className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\"\n                              onClick={() => openEditModal(client)}\n                              title=\"Edit Client\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors\"\n                              onClick={() => handleDeleteClient(client._id)}\n                              title=\"Delete Client\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* View Details Popup */}\n          <AnimatePresence>\n            {showDetailsPopup && selectedClientForDetails && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold\">\n                          {selectedClientForDetails.companyName?.charAt(0) || 'C'}\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl font-bold text-white\">\n                            {selectedClientForDetails.companyName}\n                          </h2>\n                          <p className=\"text-white/80 text-sm\">\n                            {selectedClientForDetails.email}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowDetailsPopup(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\">\n                    {loadingAnalytics ? (\n                      <div className=\"flex items-center justify-center py-12\">\n                        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\n                      </div>\n                    ) : (\n                      <div className=\"space-y-6\">\n                        {/* Client Info */}\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                          <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center\">\n                                <Users className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-blue-600\">Total Sessions</p>\n                                <p className=\"text-xl font-bold text-blue-900\">\n                                  {clientAnalytics?.timeAnalysis?.dailyTrends?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center\">\n                                <Clock className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-green-600\">Avg Duration</p>\n                                <p className=\"text-xl font-bold text-green-900\">\n                                  {Math.round(clientAnalytics?.timeAnalysis?.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / (clientAnalytics?.timeAnalysis?.dailyTrends?.length || 1)) || 0}s\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center\">\n                                <BarChart3 className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-purple-600\">Products</p>\n                                <p className=\"text-xl font-bold text-purple-900\">\n                                  {clientAnalytics?.productPerformance?.length || 0}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center\">\n                                <Smartphone className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-orange-600\">Devices</p>\n                                <p className=\"text-xl font-bold text-orange-900\">\n                                  {clientAnalytics?.deviceStats?.length || 0}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Recent Activity */}\n                        <div className=\"bg-gray-50 rounded-xl p-6\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                            <Activity className=\"h-5 w-5 mr-2 text-[#2D8C88]\" />\n                            Recent Activity\n                          </h3>\n                          <div className=\"space-y-3\">\n                            {clientAnalytics?.timeAnalysis?.dailyTrends?.slice(-5).map((day, index) => (\n                              <div key={index} className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <div className=\"flex items-center space-x-3\">\n                                  <div className=\"w-2 h-2 rounded-full bg-[#2D8C88]\"></div>\n                                  <span className=\"text-sm font-medium text-gray-900\">\n                                    {new Date(day.date).toLocaleDateString()}\n                                  </span>\n                                </div>\n                                <div className=\"text-sm text-gray-600\">\n                                  {day.sessions} sessions • {Math.round(day.avgDuration || 0)}s avg\n                                </div>\n                              </div>\n                            )) || (\n                              <p className=\"text-gray-500 text-center py-4\">No recent activity data available</p>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Device Breakdown */}\n                        {clientAnalytics?.deviceStats && clientAnalytics.deviceStats.length > 0 && (\n                          <div className=\"bg-gray-50 rounded-xl p-6\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                              <Monitor className=\"h-5 w-5 mr-2 text-[#2D8C88]\" />\n                              Device Usage\n                            </h3>\n                            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                              {clientAnalytics.deviceStats.map((device, index) => (\n                                <div key={index} className=\"bg-white rounded-lg p-4\">\n                                  <div className=\"flex items-center justify-between\">\n                                    <span className=\"text-sm font-medium text-gray-900 capitalize\">\n                                      {device.device || 'Unknown'}\n                                    </span>\n                                    <span className=\"text-sm text-gray-600\">\n                                      {device.sessions} sessions\n                                    </span>\n                                  </div>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* View Code Popup */}\n          <AnimatePresence>\n            {showCodePopup && selectedClientForCode && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center\">\n                          <Code className=\"h-5 w-5 text-white\" />\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl font-bold text-white\">\n                            Integration Code\n                          </h2>\n                          <p className=\"text-white/80 text-sm\">\n                            {selectedClientForCode.companyName}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowCodePopup(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <div className=\"space-y-6\">\n                      {/* Instructions */}\n                      <div className=\"bg-blue-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">\n                          How to integrate ViatrOn\n                        </h3>\n                        <div className=\"space-y-2 text-sm text-blue-800\">\n                          <p>1. Copy the code below and paste it into your website's HTML</p>\n                          <p>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs</p>\n                          <p>3. Customize the button styling to match your brand</p>\n                          <p>4. Test the integration to ensure it works correctly</p>\n                        </div>\n                      </div>\n\n                      {/* Code Block */}\n                      <div className=\"relative\">\n                        <div className=\"flex items-center justify-between mb-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">\n                            Integration Code\n                          </h3>\n                          <button\n                            onClick={copyCodeToClipboard}\n                            className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\"\n                          >\n                            {copiedCode ? (\n                              <>\n                                <Check className=\"h-4 w-4 mr-2\" />\n                                Copied!\n                              </>\n                            ) : (\n                              <>\n                                <Copy className=\"h-4 w-4 mr-2\" />\n                                Copy Code\n                              </>\n                            )}\n                          </button>\n                        </div>\n\n                        <div className=\"bg-gray-900 rounded-xl p-4 overflow-x-auto\">\n                          <pre className=\"text-green-400 text-sm\">\n                            <code>{generateIntegrationCode(selectedClientForCode)}</code>\n                          </pre>\n                        </div>\n                      </div>\n\n                      {/* Example Usage */}\n                      <div className=\"bg-gray-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                          Example Usage\n                        </h3>\n                        <div className=\"space-y-3\">\n                          <div className=\"bg-white rounded-lg p-3 border\">\n                            <p className=\"text-sm font-medium text-gray-700 mb-2\">For Watches:</p>\n                            <code className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                              openViaTryon('https://example.com/watch.jpg', '42', 'watches')\n                            </code>\n                          </div>\n                          <div className=\"bg-white rounded-lg p-3 border\">\n                            <p className=\"text-sm font-medium text-gray-700 mb-2\">For Bracelets:</p>\n                            <code className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                              openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')\n                            </code>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Support Info */}\n                      <div className=\"bg-yellow-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-yellow-900 mb-2\">\n                          Need Help?\n                        </h3>\n                        <p className=\"text-sm text-yellow-800\">\n                          If you need assistance with the integration, please contact our support team or refer to our documentation.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAC/DC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAC/DC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QACtC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC;IACjC4E,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoF,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC9E,MAAM,CAACwF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8F,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC;IAC3CkG,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTnE,QAAQ,EAAE,EAAE;IACZoE,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,SAAS;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BjD,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMmD,UAAU,GAAGjD,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACAzD,SAAS,CAAC,MAAM;IACd2G,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChD,WAAW,EAAEE,cAAc,CAAC,CAAC;EAEjC,MAAM8C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMO,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIhE,WAAW,EAAE+D,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEjE,WAAW,CAAC;MACrD,IAAIE,cAAc,KAAK,KAAK,EAAE6D,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAE/D,cAAc,CAAC;;MAErE;MACA,MAAM,CAACgE,eAAe,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DC,KAAK,CAAC,GAAGV,MAAM,gBAAgBG,MAAM,EAAE,EAAE;QACvCQ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,mCAAmC,EAAE;QAClDW,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,IAAI,CAACc,eAAe,CAACM,EAAE,EAAE;QACvB,MAAMC,SAAS,GAAG,MAAMP,eAAe,CAACQ,IAAI,CAAC,CAAC;QAC9C,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMC,WAAW,GAAG,MAAMV,eAAe,CAACQ,IAAI,CAAC,CAAC;MAChDjE,UAAU,CAACmE,WAAW,CAACpE,OAAO,IAAI,EAAE,CAAC;;MAErC;MACA,IAAIqE,gBAAgB,GAAG,CAAC;MACxB,IAAIV,mBAAmB,CAACK,EAAE,EAAE;QAAA,IAAAM,qBAAA;QAC1B,MAAM1D,eAAe,GAAG,MAAM+C,mBAAmB,CAACO,IAAI,CAAC,CAAC;QACxDrD,kBAAkB,CAACD,eAAe,CAAC;QACnCyD,gBAAgB,GAAG,EAAAC,qBAAA,GAAA1D,eAAe,CAAC2D,OAAO,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBE,gBAAgB,KAAI,CAAC;MACnE;MAEAjE,QAAQ,CAAC;QACPC,mBAAmB,EAAE,EAAAiC,kBAAA,GAAA2B,WAAW,CAAC9D,KAAK,cAAAmC,kBAAA,uBAAjBA,kBAAA,CAAmBjC,mBAAmB,KAAI,CAAC;QAChEC,UAAU,EAAE,EAAAiC,mBAAA,GAAA0B,WAAW,CAAC9D,KAAK,cAAAoC,mBAAA,uBAAjBA,mBAAA,CAAmBjC,UAAU,KAAI,CAAC;QAC9CC,YAAY,EAAE,EAAAiC,mBAAA,GAAAyB,WAAW,CAAC9D,KAAK,cAAAqC,mBAAA,uBAAjBA,mBAAA,CAAmBjC,YAAY,KAAI,CAAC;QAClDC,WAAW,EAAE0D;MACf,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACtE,KAAK,CAAC,yBAAyB,EAAEqE,GAAG,CAAC;MAC7CpE,QAAQ,CAACoE,GAAG,CAACN,OAAO,CAAC;MACrBlE,UAAU,CAAC,EAAE,CAAC;MACdM,QAAQ,CAAC;QACPC,mBAAmB,EAAE,CAAC;QACtBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwE,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IACjC,MAAMI,WAAW,GAAG/G,IAAI,CAACC,KAAK,CAAC,CAAC2G,GAAG,GAAGE,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErE,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMC,UAAU,GAAGhH,IAAI,CAACC,KAAK,CAAC8G,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,WAAW;IACnD,MAAMC,WAAW,GAAGjH,IAAI,CAACC,KAAK,CAAC+G,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,YAAY;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1D,aAAa,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC5D,aAAa,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1H,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAM8H,SAAS,GAAGA,CAAA,KAAM;IACtB7D,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTnE,QAAQ,EAAE,EAAE;MACZoE,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,SAAS;MACtBC,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACFtC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM4F,eAAe,GAAG,MAAOP,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFzF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,cAAc,EAAE;QACpD0C,MAAM,EAAE,MAAM;QACd/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACrE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACiE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpB3C,YAAY,CAAC,KAAK,CAAC;MACnB6F,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAACtE,KAAK,CAAC,wBAAwB,EAAEqE,GAAG,CAAC;MAC5CpE,QAAQ,CAACoE,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRhE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+F,gBAAgB,GAAG,MAAOd,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFzF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgBtD,aAAa,CAACqG,GAAG,EAAE,EAAE;QACzEL,MAAM,EAAE,KAAK;QACb/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACrE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACiE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpB3C,YAAY,CAAC,KAAK,CAAC;MACnB6F,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAACtE,KAAK,CAAC,wBAAwB,EAAEqE,GAAG,CAAC;MAC5CpE,QAAQ,CAACoE,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRhE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiG,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MACnE;IACF;IAEA,IAAI;MACFpG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgBiD,QAAQ,EAAE,EAAE;QAChEP,MAAM,EAAE,QAAQ;QAChB/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACiD,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZC,OAAO,CAACtE,KAAK,CAAC,wBAAwB,EAAEqE,GAAG,CAAC;MAC5CpE,QAAQ,CAACoE,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRhE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqG,aAAa,GAAIC,MAAM,IAAK;IAChC1G,gBAAgB,CAAC0G,MAAM,CAAC;IACxB5E,aAAa,CAAC;MACZC,WAAW,EAAE2E,MAAM,CAAC3E,WAAW,IAAI,EAAE;MACrCC,WAAW,EAAE0E,MAAM,CAAC1E,WAAW,IAAI,EAAE;MACrCC,OAAO,EAAEyE,MAAM,CAACzE,OAAO,IAAI,EAAE;MAC7BC,KAAK,EAAEwE,MAAM,CAACxE,KAAK,IAAI,EAAE;MACzBnE,QAAQ,EAAE,EAAE;MAAE;MACdoE,KAAK,EAAEuE,MAAM,CAACvE,KAAK,IAAI,EAAE;MACzBC,QAAQ,EAAEsE,MAAM,CAACtE,QAAQ,IAAI,EAAE;MAC/BC,WAAW,EAAEqE,MAAM,CAACrE,WAAW,IAAI,SAAS;MAC5CC,gBAAgB,EAAEoE,MAAM,CAACpE,gBAAgB,IAAI;IAC/C,CAAC,CAAC;IACFxC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM6G,YAAY,GAAGA,CAAA,KAAM;IACzBhB,SAAS,CAAC,CAAC;IACX7F,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAM8G,oBAAoB,GAAG,MAAON,QAAQ,IAAK;IAC/C,IAAI;MACF5E,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMmB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;MAErE;MACA,MAAM4D,GAAG,GAAG,IAAI9B,IAAI,CAAC,CAAC;MACtB,MAAM+B,KAAK,GAAG,IAAI/B,IAAI,CAAC,CAAC;MACxB+B,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;MAEnC,MAAM,CAACC,oBAAoB,EAAEC,0BAA0B,EAAEC,mBAAmB,CAAC,GAAG,MAAMtD,OAAO,CAACC,GAAG,CAAC,CAChGC,KAAK,CAAC,GAAGV,MAAM,6CAA6CyD,KAAK,CAACM,WAAW,CAAC,CAAC,QAAQP,GAAG,CAACO,WAAW,CAAC,CAAC,EAAE,EAAE;QAC1GpD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,mDAAmDyD,KAAK,CAACM,WAAW,CAAC,CAAC,QAAQP,GAAG,CAACO,WAAW,CAAC,CAAC,EAAE,EAAE;QAChHpD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,4CAA4CyD,KAAK,CAACM,WAAW,CAAC,CAAC,QAAQP,GAAG,CAACO,WAAW,CAAC,CAAC,EAAE,EAAE;QACzGpD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,MAAMwE,QAAQ,GAAGJ,oBAAoB,CAAChD,EAAE,GAAG,MAAMgD,oBAAoB,CAAC9C,IAAI,CAAC,CAAC,GAAG,IAAI;MACnF,MAAMmD,WAAW,GAAGJ,0BAA0B,CAACjD,EAAE,GAAG,MAAMiD,0BAA0B,CAAC/C,IAAI,CAAC,CAAC,GAAG,IAAI;MAClG,MAAMoD,UAAU,GAAGJ,mBAAmB,CAAClD,EAAE,GAAG,MAAMkD,mBAAmB,CAAChD,IAAI,CAAC,CAAC,GAAG,IAAI;MAEnF3C,kBAAkB,CAAC;QACjBgG,YAAY,EAAEH,QAAQ;QACtBI,kBAAkB,EAAEH,WAAW;QAC/BI,WAAW,EAAEH;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDmB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,SAAS;MACRE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMiG,iBAAiB,GAAIjB,MAAM,IAAK;IACpCtF,2BAA2B,CAACsF,MAAM,CAAC;IACnC1F,mBAAmB,CAAC,IAAI,CAAC;IACzB4F,oBAAoB,CAACF,MAAM,CAACN,GAAG,CAAC;EAClC,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAIlB,MAAM,IAAK;IACjCpF,wBAAwB,CAACoF,MAAM,CAAC;IAChCxF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAM2G,uBAAuB,GAAInB,MAAM,IAAK;IAC1C,MAAMzD,OAAO,GAAGC,OAAO,CAACC,GAAG,CAAC2E,sBAAsB,IAAIvB,MAAM,CAACwB,QAAQ,CAACC,MAAM;IAC5E,OAAO;AACX;AACA;AACA,sBAAsB/E,OAAO;AAC7B;AACA,eAAeyD,MAAM,CAACN,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;EACR,CAAC;;EAED;EACA,MAAM6B,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,IAAI,GAAGL,uBAAuB,CAACxG,qBAAqB,CAAC;IAC3D8G,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAM;MAC7C1G,aAAa,CAAC,IAAI,CAAC;MACnB2G,UAAU,CAAC,MAAM3G,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,oBACElE,OAAA;IAAK8K,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC/K,OAAA,CAAC3B,YAAY;MAAC2M,MAAM,EAAErJ,aAAc;MAACsJ,OAAO,EAAEA,CAAA,KAAMrJ,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAoJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjIrL,OAAA,CAAC1B,WAAW;MAACuG,aAAa,EAAEA,aAAc;MAAChD,SAAS,EAAEA;IAAU;MAAAqJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnErL,OAAA;MAAM8K,SAAS,EAAE,GAAGhG,UAAU,oCAAqC;MAAAiG,QAAA,eACjE/K,OAAA;QAAK8K,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzB/K,OAAA;UAAK8K,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtF/K,OAAA;YAAA+K,QAAA,gBACE/K,OAAA;cAAI8K,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvErL,OAAA;cAAG8K,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACNrL,OAAA;YACE8K,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAErC,YAAa;YAAA8B,QAAA,gBAEtB/K,OAAA,CAACtB,IAAI;cAACoM,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNrL,OAAA;UAAK8K,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjF/K,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C/K,OAAA;cAAK8K,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/K,OAAA;gBAAA+K,QAAA,gBACE/K,OAAA;kBAAG8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClErL,OAAA;kBAAG8K,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtI,OAAO,GAAG,KAAK,GAAGF,OAAO,CAAC5B;gBAAM;kBAAAuK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACNrL,OAAA;gBAAK8K,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrF/K,OAAA,CAAChB,KAAK;kBAAC8L,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrL,OAAA;cAAK8K,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/K,OAAA;gBAAM8K,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,GAAC,EAAClI,KAAK,CAACE,mBAAmB,EAAC,MAAI;cAAA;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5FrL,OAAA;gBAAM8K,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrL,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C/K,OAAA;cAAK8K,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/K,OAAA;gBAAA+K,QAAA,gBACE/K,OAAA;kBAAG8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnErL,OAAA;kBAAG8K,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtI,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACuJ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB,KAAK,QAAQ,CAAC,CAACrL;gBAAM;kBAAAuK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,eACNrL,OAAA;gBAAK8K,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF/K,OAAA,CAACjB,UAAU;kBAAC+L,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrL,OAAA;cAAK8K,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/K,OAAA;gBAAM8K,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAElI,KAAK,CAACG,UAAU,CAACiJ,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1FrL,OAAA;gBAAM8K,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrL,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C/K,OAAA;cAAK8K,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/K,OAAA;gBAAA+K,QAAA,gBACE/K,OAAA;kBAAG8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClErL,OAAA;kBAAG8K,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtI,OAAO,GAAG,KAAK,GAAGF,OAAO,CAAC2J,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC;oBAAA,IAAAK,YAAA;oBAAA,OAAKD,GAAG,IAAI,EAAAC,YAAA,GAAAL,CAAC,CAACM,SAAS,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,aAAa,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAACC,cAAc,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrK,CAAC,eACNrL,OAAA;gBAAK8K,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF/K,OAAA,CAACrB,GAAG;kBAACmM,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrL,OAAA;cAAK8K,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/K,OAAA;gBAAM8K,SAAS,EAAE,uBAAuBjI,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAA8H,QAAA,GACnGlI,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,YAAY,CAACgJ,OAAO,CAAC,CAAC,CAAC,EAAC,GACrE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrL,OAAA;gBAAM8K,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrL,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C/K,OAAA;cAAK8K,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/K,OAAA;gBAAA+K,QAAA,gBACE/K,OAAA;kBAAG8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzErL,OAAA;kBAAG8K,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtI,OAAO,GAAG,KAAK,GAAGI,KAAK,CAACK,WAAW,CAACqJ,cAAc,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACNrL,OAAA;gBAAK8K,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF/K,OAAA,CAACP,QAAQ;kBAACqL,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrL,OAAA;cAAK8K,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/K,OAAA;gBAAM8K,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChD,CAAA5H,eAAe,aAAfA,eAAe,wBAAArC,sBAAA,GAAfqC,eAAe,CAAE2D,OAAO,cAAAhG,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0B0L,kBAAkB,cAAAzL,sBAAA,uBAA5CA,sBAAA,CAA8CkL,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,eACnE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrL,OAAA;gBAAM8K,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNrL,OAAA,CAACxB,eAAe;UAAAuM,QAAA,EACb5I,SAAS,iBACRnC,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBgB,IAAI,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1F/K,OAAA,CAACzB,MAAM,CAACgN,GAAG;cACTC,OAAO,EAAE;gBAAEkB,KAAK,EAAE,IAAI;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCgB,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,0EAA0E;cAAAC,QAAA,gBAGpF/K,OAAA;gBAAK8K,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrE/K,OAAA;kBAAK8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD/K,OAAA;oBAAI8K,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EACzC1I,aAAa,GAAG,aAAa,GAAG;kBAAgB;oBAAA6I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACLrL,OAAA;oBACE8K,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAMlJ,YAAY,CAAC,KAAK,CAAE;oBAAA2I,QAAA,eAEnC/K,OAAA,CAACd,CAAC;sBAAC4L,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrL,OAAA;gBAAK8K,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClB/K,OAAA;kBAAM2M,QAAQ,EAAEtK,aAAa,GAAGoG,gBAAgB,GAAGP,eAAgB;kBAAC4C,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvF/K,OAAA;oBAAK8K,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/K,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFrL,OAAA;wBACE4M,IAAI,EAAC,MAAM;wBACXhF,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAE1D,UAAU,CAACE,WAAY;wBAC9BwI,QAAQ,EAAEnF,gBAAiB;wBAC3BoF,QAAQ;wBACRhC,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAoB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNrL,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFrL,OAAA;wBACE4M,IAAI,EAAC,MAAM;wBACXhF,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAE1D,UAAU,CAACG,WAAY;wBAC9BuI,QAAQ,EAAEnF,gBAAiB;wBAC3BoF,QAAQ;wBACRhC,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAoB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrL,OAAA;oBAAK8K,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/K,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/ErL,OAAA;wBACE4M,IAAI,EAAC,OAAO;wBACZhF,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAE1D,UAAU,CAACK,KAAM;wBACxBqI,QAAQ,EAAEnF,gBAAiB;wBAC3BoF,QAAQ;wBACRhC,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAqB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNrL,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/ErL,OAAA;wBACE4M,IAAI,EAAC,KAAK;wBACVhF,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAE1D,UAAU,CAACM,KAAM;wBACxBoI,QAAQ,EAAEnF,gBAAiB;wBAC3BoD,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAoB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrL,OAAA;oBAAK8K,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/K,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjFrL,OAAA;wBACE4M,IAAI,EAAC,KAAK;wBACVhF,IAAI,EAAC,SAAS;wBACdC,KAAK,EAAE1D,UAAU,CAACI,OAAQ;wBAC1BsI,QAAQ,EAAEnF,gBAAiB;wBAC3BoD,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAqB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNrL,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClFrL,OAAA;wBACE4M,IAAI,EAAC,MAAM;wBACXhF,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAE1D,UAAU,CAACO,QAAS;wBAC3BmI,QAAQ,EAAEnF,gBAAiB;wBAC3BoD,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAwB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrL,OAAA;oBAAK8K,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/K,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFrL,OAAA;wBACE4H,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAE1D,UAAU,CAACQ,WAAY;wBAC9BkI,QAAQ,EAAEnF,gBAAiB;wBAC3BoD,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3J/K,OAAA;0BAAQ6H,KAAK,EAAC,SAAS;0BAAAkD,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxCrL,OAAA;0BAAQ6H,KAAK,EAAC,WAAW;0BAAAkD,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5CrL,OAAA;0BAAQ6H,KAAK,EAAC,MAAM;0BAAAkD,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNrL,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAO8K,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC3FrL,OAAA;wBACE4H,IAAI,EAAC,kBAAkB;wBACvBC,KAAK,EAAE1D,UAAU,CAACS,gBAAiB;wBACnCiI,QAAQ,EAAEnF,gBAAiB;wBAC3BoD,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3J/K,OAAA;0BAAQ6H,KAAK,EAAC,OAAO;0BAAAkD,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpCrL,OAAA;0BAAQ6H,KAAK,EAAC,SAAS;0BAAAkD,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxCrL,OAAA;0BAAQ6H,KAAK,EAAC,YAAY;0BAAAkD,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAAChJ,aAAa,iBACbrC,OAAA;oBAAA+K,QAAA,gBACE/K,OAAA;sBAAO8K,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClFrL,OAAA;sBAAK8K,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzB/K,OAAA;wBACE4M,IAAI,EAAC,MAAM;wBACXhF,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAE1D,UAAU,CAAC9D,QAAS;wBAC3BwM,QAAQ,EAAEnF,gBAAiB;wBAC3BoF,QAAQ;wBACRhC,SAAS,EAAC,iJAAiJ;wBAC3JiC,WAAW,EAAC;sBAAgB;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACFrL,OAAA;wBACE4M,IAAI,EAAC,QAAQ;wBACbtB,OAAO,EAAEtD,qBAAsB;wBAC/B8C,SAAS,EAAC,gGAAgG;wBAAAC,QAAA,EAC3G;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDrL,OAAA;oBAAK8K,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,gBACvE/K,OAAA;sBACE4M,IAAI,EAAC,QAAQ;sBACbtB,OAAO,EAAEA,CAAA,KAAMlJ,YAAY,CAAC,KAAK,CAAE;sBACnC0I,SAAS,EAAC,0GAA0G;sBAAAC,QAAA,EACrH;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrL,OAAA;sBACE4M,IAAI,EAAC,QAAQ;sBACb9B,SAAS,EAAC,yGAAyG;sBAAAC,QAAA,EAElH1I,aAAa,GAAG,eAAe,GAAG;oBAAe;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBrL,OAAA;UAAK8K,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD/K,OAAA;YAAK8K,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C/K,OAAA;cAAK8K,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrB/K,OAAA;gBACE4M,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/BlF,KAAK,EAAE9F,WAAY;gBACnB8K,QAAQ,EAAGlF,CAAC,IAAK3F,cAAc,CAAC2F,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAChDiD,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrL,OAAA;cAAK8K,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B/K,OAAA;gBACE6H,KAAK,EAAE5F,cAAe;gBACtB4K,QAAQ,EAAGlF,CAAC,IAAKzF,iBAAiB,CAACyF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACnDiD,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5I/K,OAAA;kBAAQ6H,KAAK,EAAC,KAAK;kBAAAkD,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCrL,OAAA;kBAAQ6H,KAAK,EAAC,QAAQ;kBAAAkD,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrL,OAAA;kBAAQ6H,KAAK,EAAC,SAAS;kBAAAkD,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrL,OAAA;UAAK8K,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5D/K,OAAA;YAAK8K,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B/K,OAAA;cAAO8K,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpD/K,OAAA;gBAAO8K,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3B/K,OAAA;kBAAA+K,QAAA,gBACE/K,OAAA;oBAAI8K,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GrL,OAAA;oBAAI8K,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChIrL,OAAA;oBAAI8K,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnIrL,OAAA;oBAAI8K,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/HrL,OAAA;oBAAI8K,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpIrL,OAAA;oBAAI8K,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrL,OAAA;gBAAO8K,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDtI,OAAO,gBACNzC,OAAA;kBAAA+K,QAAA,eACE/K,OAAA;oBAAIgN,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eAC/C/K,OAAA;sBAAK8K,SAAS,EAAC;oBAAuE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH1I,KAAK,gBACP3C,OAAA;kBAAA+K,QAAA,eACE/K,OAAA;oBAAIgN,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,yBACtC,EAACpI,KAAK;kBAAA;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH9I,OAAO,CAAC5B,MAAM,KAAK,CAAC,gBACtBX,OAAA;kBAAA+K,QAAA,eACE/K,OAAA;oBAAIgN,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAEL9I,OAAO,CAAC0K,GAAG,CAAEjE,MAAM;kBAAA,IAAAkE,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA;kBAAA,oBACjB5N,OAAA,CAACzB,MAAM,CAACsP,EAAE;oBAERrC,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAE;oBACxBE,OAAO,EAAE;sBAAEF,OAAO,EAAE;oBAAE,CAAE;oBACxBX,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5B/K,OAAA;sBAAI8K,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,eACzC/K,OAAA;wBAAK8K,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChC/K,OAAA;0BAAK8K,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,eACtC/K,OAAA;4BAAK8K,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,EAC7F,EAAAmC,mBAAA,GAAAlE,MAAM,CAAC3E,WAAW,cAAA6I,mBAAA,uBAAlBA,mBAAA,CAAoB3M,MAAM,CAAC,CAAC,CAAC,KAAI;0BAAG;4BAAA2K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNrL,OAAA;0BAAK8K,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnB/K,OAAA;4BAAK8K,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAE/B,MAAM,CAAC3E;0BAAW;4BAAA6G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7ErL,OAAA;4BAAK8K,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE/B,MAAM,CAACxE;0BAAK;4BAAA0G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3DrL,OAAA;4BAAK8K,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC7C,EAAAoC,iBAAA,GAAAnE,MAAM,CAACqD,SAAS,cAAAc,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBb,aAAa,cAAAc,qBAAA,uBAA/BA,qBAAA,CAAiCb,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,kBAAW,EAAC,EAAAc,kBAAA,GAAArE,MAAM,CAACqD,SAAS,cAAAgB,kBAAA,uBAAhBA,kBAAA,CAAkBS,cAAc,KAAI,GAAG,EAAC,cAChH;0BAAA;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLrL,OAAA;sBAAI8K,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9D/K,OAAA;wBAAK8K,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAAuC,kBAAA,GAAAtE,MAAM,CAACqD,SAAS,cAAAiB,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBhB,aAAa,cAAAiB,qBAAA,uBAA/BA,qBAAA,CAAiChB,cAAc,CAAC,CAAC,KAAI;sBAAG;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnHrL,OAAA;wBAAK8K,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAyC,kBAAA,GAAAxE,MAAM,CAACqD,SAAS,cAAAmB,kBAAA,uBAAhBA,kBAAA,CAAkBO,YAAY,KAAI,GAAG,EAAC,WAAS;sBAAA;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACLrL,OAAA;sBAAI8K,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9D/K,OAAA;wBAAK8K,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAAE,EAAA0C,kBAAA,GAAAzE,MAAM,CAACqD,SAAS,cAAAoB,kBAAA,uBAAhBA,kBAAA,CAAkBK,cAAc,KAAI,GAAG,EAAC,GAAC;sBAAA;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnGrL,OAAA;wBAAK8K,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAAC,EAAC,EAAA2C,kBAAA,GAAA1E,MAAM,CAACqD,SAAS,cAAAqB,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBM,OAAO,cAAAL,qBAAA,uBAAzBA,qBAAA,CAA2BpB,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,UAAQ;sBAAA;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC,eACLrL,OAAA;sBAAI8K,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9D/K,OAAA;wBAAK8K,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC/K,OAAA;0BAAM8K,SAAS,EAAE,2EACf9B,MAAM,CAACgD,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GACtEhD,MAAM,CAACgD,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GACnE,+BAA+B,EAC9B;0BAAAjB,QAAA,EACA/B,MAAM,CAACgD;wBAAkB;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACPrL,OAAA;0BAAM8K,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAE7D,gBAAgB,EAAA0G,kBAAA,GAAC5E,MAAM,CAACqD,SAAS,cAAAuB,kBAAA,uBAAhBA,kBAAA,CAAkBtG,UAAU;wBAAC;0BAAA4D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLrL,OAAA;sBAAI8K,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9D/K,OAAA;wBAAM8K,SAAS,EAAE,2EACf9B,MAAM,CAACpE,gBAAgB,KAAK,YAAY,GAAG,+BAA+B,GAC1EoE,MAAM,CAACpE,gBAAgB,KAAK,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,EAChG;wBAAAmG,QAAA,EACA/B,MAAM,CAACpE;sBAAgB;wBAAAsG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLrL,OAAA;sBAAI8K,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACxE/K,OAAA;wBAAK8K,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzC/K,OAAA;0BACE8K,SAAS,EAAC,+FAA+F;0BACzGQ,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAACjB,MAAM,CAAE;0BACzCiF,KAAK,EAAC,cAAc;0BAAAlD,QAAA,eAEpB/K,OAAA,CAACrB,GAAG;4BAACmM,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACTrL,OAAA;0BACE8K,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAClB,MAAM,CAAE;0BACtCiF,KAAK,EAAC,kBAAkB;0BAAAlD,QAAA,eAExB/K,OAAA,CAACf,IAAI;4BAAC6L,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTrL,OAAA;0BACE8K,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAMvC,aAAa,CAACC,MAAM,CAAE;0BACrCiF,KAAK,EAAC,aAAa;0BAAAlD,QAAA,eAEnB/K,OAAA,CAACpB,IAAI;4BAACkM,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTrL,OAAA;0BACE8K,SAAS,EAAC,kFAAkF;0BAC5FQ,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAACK,MAAM,CAACN,GAAG,CAAE;0BAC9CuF,KAAK,EAAC,eAAe;0BAAAlD,QAAA,eAErB/K,OAAA,CAACnB,MAAM;4BAACiM,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAhFArC,MAAM,CAACN,GAAG;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiFN,CAAC;gBAAA,CACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrL,OAAA,CAACxB,eAAe;UAAAuM,QAAA,EACb1H,gBAAgB,IAAII,wBAAwB,iBAC3CzD,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBgB,IAAI,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1F/K,OAAA,CAACzB,MAAM,CAACgN,GAAG;cACTC,OAAO,EAAE;gBAAEkB,KAAK,EAAE,IAAI;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCgB,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAGzF/K,OAAA;gBAAK8K,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrE/K,OAAA;kBAAK8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD/K,OAAA;oBAAK8K,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/K,OAAA;sBAAK8K,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,EACtG,EAAA/J,qBAAA,GAAAyC,wBAAwB,CAACY,WAAW,cAAArD,qBAAA,uBAApCA,qBAAA,CAAsCT,MAAM,CAAC,CAAC,CAAC,KAAI;oBAAG;sBAAA2K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNrL,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAI8K,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EACzCtH,wBAAwB,CAACY;sBAAW;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,eACLrL,OAAA;wBAAG8K,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjCtH,wBAAwB,CAACe;sBAAK;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrL,OAAA;oBACE8K,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAMhI,mBAAmB,CAAC,KAAK,CAAE;oBAAAyH,QAAA,eAE1C/K,OAAA,CAACd,CAAC;sBAAC4L,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrL,OAAA;gBAAK8K,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EACzDhH,gBAAgB,gBACf/D,OAAA;kBAAK8K,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,eACrD/K,OAAA;oBAAK8K,SAAS,EAAC;kBAAiE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,gBAENrL,OAAA;kBAAK8K,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBAExB/K,OAAA;oBAAK8K,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnE/K,OAAA;sBAAK8K,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,eACxE/K,OAAA;wBAAK8K,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C/K,OAAA;0BAAK8K,SAAS,EAAC,mEAAmE;0BAAAC,QAAA,eAChF/K,OAAA,CAAChB,KAAK;4BAAC8L,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACNrL,OAAA;0BAAA+K,QAAA,gBACE/K,OAAA;4BAAG8K,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAc;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACnErL,OAAA;4BAAG8K,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC3C,CAAAlH,eAAe,aAAfA,eAAe,wBAAA5C,qBAAA,GAAf4C,eAAe,CAAEiG,YAAY,cAAA7I,qBAAA,wBAAAC,sBAAA,GAA7BD,qBAAA,CAA+BiN,WAAW,cAAAhN,sBAAA,uBAA1CA,sBAAA,CAA4CgL,MAAM,CAAC,CAACC,GAAG,EAAEgC,GAAG,KAAKhC,GAAG,IAAIgC,GAAG,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI;0BAAC;4BAAAlD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENrL,OAAA;sBAAK8K,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1E/K,OAAA;wBAAK8K,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C/K,OAAA;0BAAK8K,SAAS,EAAC,oEAAoE;0BAAAC,QAAA,eACjF/K,OAAA,CAACV,KAAK;4BAACwL,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACNrL,OAAA;0BAAA+K,QAAA,gBACE/K,OAAA;4BAAG8K,SAAS,EAAC,oCAAoC;4BAAAC,QAAA,EAAC;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAClErL,OAAA;4BAAG8K,SAAS,EAAC,kCAAkC;4BAAAC,QAAA,GAC5CvK,IAAI,CAAC6N,KAAK,CAAC,CAAAxK,eAAe,aAAfA,eAAe,wBAAA1C,sBAAA,GAAf0C,eAAe,CAAEiG,YAAY,cAAA3I,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+B+M,WAAW,cAAA9M,sBAAA,uBAA1CA,sBAAA,CAA4C8K,MAAM,CAAC,CAACC,GAAG,EAAEgC,GAAG,KAAKhC,GAAG,IAAIgC,GAAG,CAACG,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,CAAAzK,eAAe,aAAfA,eAAe,wBAAAxC,sBAAA,GAAfwC,eAAe,CAAEiG,YAAY,cAAAzI,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+B6M,WAAW,cAAA5M,sBAAA,uBAA1CA,sBAAA,CAA4CX,MAAM,KAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,GAClL;0BAAA;4BAAAuK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENrL,OAAA;sBAAK8K,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5E/K,OAAA;wBAAK8K,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C/K,OAAA;0BAAK8K,SAAS,EAAC,qEAAqE;0BAAAC,QAAA,eAClF/K,OAAA,CAACX,SAAS;4BAACyL,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,eACNrL,OAAA;0BAAA+K,QAAA,gBACE/K,OAAA;4BAAG8K,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAC/DrL,OAAA;4BAAG8K,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAC7C,CAAAlH,eAAe,aAAfA,eAAe,wBAAAtC,qBAAA,GAAfsC,eAAe,CAAEkG,kBAAkB,cAAAxI,qBAAA,uBAAnCA,qBAAA,CAAqCZ,MAAM,KAAI;0BAAC;4BAAAuK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENrL,OAAA;sBAAK8K,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5E/K,OAAA;wBAAK8K,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C/K,OAAA;0BAAK8K,SAAS,EAAC,qEAAqE;0BAAAC,QAAA,eAClF/K,OAAA,CAACT,UAAU;4BAACuL,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACNrL,OAAA;0BAAA+K,QAAA,gBACE/K,OAAA;4BAAG8K,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAC;0BAAO;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAC9DrL,OAAA;4BAAG8K,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAC7C,CAAAlH,eAAe,aAAfA,eAAe,wBAAArC,qBAAA,GAAfqC,eAAe,CAAEmG,WAAW,cAAAxI,qBAAA,uBAA5BA,qBAAA,CAA8Bb,MAAM,KAAI;0BAAC;4BAAAuK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrL,OAAA;oBAAK8K,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC/K,OAAA;sBAAI8K,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACxE/K,OAAA,CAACP,QAAQ;wBAACqL,SAAS,EAAC;sBAA6B;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAEtD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrL,OAAA;sBAAK8K,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvB,CAAAlH,eAAe,aAAfA,eAAe,wBAAApC,sBAAA,GAAfoC,eAAe,CAAEiG,YAAY,cAAArI,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+ByM,WAAW,cAAAxM,sBAAA,uBAA1CA,sBAAA,CAA4CmE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACoH,GAAG,CAAC,CAACkB,GAAG,EAAEI,KAAK,kBACpEvO,OAAA;wBAAiB8K,SAAS,EAAC,iEAAiE;wBAAAC,QAAA,gBAC1F/K,OAAA;0BAAK8K,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1C/K,OAAA;4BAAK8K,SAAS,EAAC;0BAAmC;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzDrL,OAAA;4BAAM8K,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAChD,IAAI1D,IAAI,CAAC8G,GAAG,CAAChH,IAAI,CAAC,CAACqH,kBAAkB,CAAC;0BAAC;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNrL,OAAA;0BAAK8K,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GACnCoD,GAAG,CAACC,QAAQ,EAAC,mBAAY,EAAC5N,IAAI,CAAC6N,KAAK,CAACF,GAAG,CAACG,WAAW,IAAI,CAAC,CAAC,EAAC,OAC9D;wBAAA;0BAAApD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,GATEkD,KAAK;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAUV,CACN,CAAC,kBACArL,OAAA;wBAAG8K,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAC;sBAAiC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBACnF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL,CAAAxH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmG,WAAW,KAAInG,eAAe,CAACmG,WAAW,CAACrJ,MAAM,GAAG,CAAC,iBACrEX,OAAA;oBAAK8K,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC/K,OAAA;sBAAI8K,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACxE/K,OAAA,CAACR,OAAO;wBAACsL,SAAS,EAAC;sBAA6B;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAErD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrL,OAAA;sBAAK8K,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EACnDlH,eAAe,CAACmG,WAAW,CAACiD,GAAG,CAAC,CAACwB,MAAM,EAAEF,KAAK,kBAC7CvO,OAAA;wBAAiB8K,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,eAClD/K,OAAA;0BAAK8K,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChD/K,OAAA;4BAAM8K,SAAS,EAAC,8CAA8C;4BAAAC,QAAA,EAC3D0D,MAAM,CAACA,MAAM,IAAI;0BAAS;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACPrL,OAAA;4BAAM8K,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACpC0D,MAAM,CAACL,QAAQ,EAAC,WACnB;0BAAA;4BAAAlD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC,GAREkD,KAAK;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OASV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBrL,OAAA,CAACxB,eAAe;UAAAuM,QAAA,EACbxH,aAAa,IAAII,qBAAqB,iBACrC3D,OAAA,CAACzB,MAAM,CAACgN,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBgB,IAAI,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1F/K,OAAA,CAACzB,MAAM,CAACgN,GAAG;cACTC,OAAO,EAAE;gBAAEkB,KAAK,EAAE,IAAI;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCgB,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAGzF/K,OAAA;gBAAK8K,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnE/K,OAAA;kBAAK8K,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD/K,OAAA;oBAAK8K,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/K,OAAA;sBAAK8K,SAAS,EAAC,mEAAmE;sBAAAC,QAAA,eAChF/K,OAAA,CAACf,IAAI;wBAAC6L,SAAS,EAAC;sBAAoB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACNrL,OAAA;sBAAA+K,QAAA,gBACE/K,OAAA;wBAAI8K,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAC;sBAE7C;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrL,OAAA;wBAAG8K,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjCpH,qBAAqB,CAACU;sBAAW;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrL,OAAA;oBACE8K,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM9H,gBAAgB,CAAC,KAAK,CAAE;oBAAAuH,QAAA,eAEvC/K,OAAA,CAACd,CAAC;sBAAC4L,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrL,OAAA;gBAAK8K,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClB/K,OAAA;kBAAK8K,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBAExB/K,OAAA;oBAAK8K,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC/K,OAAA;sBAAI8K,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAEzD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrL,OAAA;sBAAK8K,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C/K,OAAA;wBAAA+K,QAAA,EAAG;sBAA4D;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnErL,OAAA;wBAAA+K,QAAA,EAAG;sBAAuE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC9ErL,OAAA;wBAAA+K,QAAA,EAAG;sBAAmD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1DrL,OAAA;wBAAA+K,QAAA,EAAG;sBAAoD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrL,OAAA;oBAAK8K,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB/K,OAAA;sBAAK8K,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD/K,OAAA;wBAAI8K,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAEpD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrL,OAAA;wBACEsL,OAAO,EAAEf,mBAAoB;wBAC7BO,SAAS,EAAC,4GAA4G;wBAAAC,QAAA,EAErH9G,UAAU,gBACTjE,OAAA,CAAAE,SAAA;0BAAA6K,QAAA,gBACE/K,OAAA,CAACZ,KAAK;4BAAC0L,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,WAEpC;wBAAA,eAAE,CAAC,gBAEHrL,OAAA,CAAAE,SAAA;0BAAA6K,QAAA,gBACE/K,OAAA,CAACb,IAAI;4BAAC2L,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,aAEnC;wBAAA,eAAE;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENrL,OAAA;sBAAK8K,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,eACzD/K,OAAA;wBAAK8K,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACrC/K,OAAA;0BAAA+K,QAAA,EAAOZ,uBAAuB,CAACxG,qBAAqB;wBAAC;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrL,OAAA;oBAAK8K,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC/K,OAAA;sBAAI8K,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAEzD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrL,OAAA;sBAAK8K,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB/K,OAAA;wBAAK8K,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7C/K,OAAA;0BAAG8K,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACtErL,OAAA;0BAAM8K,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAAC;wBAEtE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrL,OAAA;wBAAK8K,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7C/K,OAAA;0BAAG8K,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAc;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACxErL,OAAA;0BAAM8K,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAAC;wBAEtE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrL,OAAA;oBAAK8K,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/K,OAAA;sBAAI8K,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAE3D;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLrL,OAAA;sBAAG8K,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAC;oBAEvC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxK,EAAA,CA1nCID,OAAO;AAAA8N,EAAA,GAAP9N,OAAO;AA4nCb,eAAeA,OAAO;AAAC,IAAA8N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}