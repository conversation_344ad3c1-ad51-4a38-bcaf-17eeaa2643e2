{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code, X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity, Calendar, Target, Zap, MapPin, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  var _uniqueUsersData$summ2, _uniqueUsersData$summ3, _selectedClientForDet, _clientAnalytics$tota, _clientAnalytics$tota2, _clientAnalytics$tota3, _clientAnalytics$tota4, _clientAnalytics$uniq;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [codeOptions, setCodeOptions] = useState({\n    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',\n    productSize: '42',\n    productType: 'watches',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    buttonText: 'Try On Virtually'\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n  const fetchClients = async () => {\n    try {\n      var _clientsData$stats, _clientsData$stats2, _clientsData$stats3;\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([fetch(`${apiUrl}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        var _uniqueUsersData$summ;\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = ((_uniqueUsersData$summ = uniqueUsersData.summary) === null || _uniqueUsersData$summ === void 0 ? void 0 : _uniqueUsersData$summ.totalUniqueUsers) || 0;\n      }\n      setStats({\n        newClientsThisMonth: ((_clientsData$stats = clientsData.stats) === null || _clientsData$stats === void 0 ? void 0 : _clientsData$stats.newClientsThisMonth) || 0,\n        activeRate: ((_clientsData$stats2 = clientsData.stats) === null || _clientsData$stats2 === void 0 ? void 0 : _clientsData$stats2.activeRate) || 0,\n        tryOnsGrowth: ((_clientsData$stats3 = clientsData.stats) === null || _clientsData$stats3 === void 0 ? void 0 : _clientsData$stats3.tryOnsGrowth) || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = date => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n  const handleAddClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteClient = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openEditModal = client => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '',\n      // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async clientId => {\n    try {\n      var _aggregatedData, _clientSpecificData, _clientSpecificData$a, _aggregatedData2, _aggregatedData3, _aggregatedData4, _clientSpecificData2, _clientSpecificData2$, _aggregatedData5, _aggregatedData6, _clientSpecificData3, _clientSpecificData3$, _aggregatedData7, _aggregatedData8;\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n\n      // Use admin endpoints to get specific client data and admin clients endpoint for aggregated data\n      const [clientDetailResponse, adminClientsResponse] = await Promise.all([fetch(`${apiUrl}/api/clients/${clientId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      let clientSpecificData = null;\n      let aggregatedData = null;\n      if (clientDetailResponse.ok) {\n        clientSpecificData = await clientDetailResponse.json();\n      }\n      if (adminClientsResponse.ok) {\n        const allClientsData = await adminClientsResponse.json();\n        // Find the specific client in the aggregated data\n        aggregatedData = allClientsData.find(client => client.clientId === clientId || client._id === clientId || client.clientName === (selectedClientForDetails === null || selectedClientForDetails === void 0 ? void 0 : selectedClientForDetails.companyName) || client.email === (selectedClientForDetails === null || selectedClientForDetails === void 0 ? void 0 : selectedClientForDetails.email));\n      }\n\n      // Combine the data sources\n      const combinedAnalytics = {\n        clientDetail: clientSpecificData,\n        aggregatedData: aggregatedData,\n        totalSessions: ((_aggregatedData = aggregatedData) === null || _aggregatedData === void 0 ? void 0 : _aggregatedData.sessions) || ((_clientSpecificData = clientSpecificData) === null || _clientSpecificData === void 0 ? void 0 : (_clientSpecificData$a = _clientSpecificData.analytics) === null || _clientSpecificData$a === void 0 ? void 0 : _clientSpecificData$a.totalSessions) || 0,\n        avgDuration: ((_aggregatedData2 = aggregatedData) === null || _aggregatedData2 === void 0 ? void 0 : _aggregatedData2.avgDuration) || 0,\n        totalInteractions: ((_aggregatedData3 = aggregatedData) === null || _aggregatedData3 === void 0 ? void 0 : _aggregatedData3.totalInteractions) || 0,\n        conversionRate: ((_aggregatedData4 = aggregatedData) === null || _aggregatedData4 === void 0 ? void 0 : _aggregatedData4.conversionRate) || ((_clientSpecificData2 = clientSpecificData) === null || _clientSpecificData2 === void 0 ? void 0 : (_clientSpecificData2$ = _clientSpecificData2.analytics) === null || _clientSpecificData2$ === void 0 ? void 0 : _clientSpecificData2$.conversionRate) || 0,\n        uniqueUsers: ((_aggregatedData5 = aggregatedData) === null || _aggregatedData5 === void 0 ? void 0 : _aggregatedData5.uniqueUsers) || 0,\n        lastActive: ((_aggregatedData6 = aggregatedData) === null || _aggregatedData6 === void 0 ? void 0 : _aggregatedData6.lastActive) || ((_clientSpecificData3 = clientSpecificData) === null || _clientSpecificData3 === void 0 ? void 0 : (_clientSpecificData3$ = _clientSpecificData3.analytics) === null || _clientSpecificData3$ === void 0 ? void 0 : _clientSpecificData3$.lastActive),\n        deviceBreakdown: ((_aggregatedData7 = aggregatedData) === null || _aggregatedData7 === void 0 ? void 0 : _aggregatedData7.deviceBreakdown) || [],\n        recentSessions: ((_aggregatedData8 = aggregatedData) === null || _aggregatedData8 === void 0 ? void 0 : _aggregatedData8.recentSessions) || []\n      };\n      setClientAnalytics(combinedAnalytics);\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = client => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = client => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = client => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    const {\n      productImageUrl,\n      productSize,\n      productType,\n      buttonStyle,\n      buttonSize,\n      buttonText\n    } = codeOptions;\n    const buttonStyles = {\n      primary: {\n        backgroundColor: '#2D8C88',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#236b68'\n      },\n      outline: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: '2px solid #2D8C88',\n        hoverColor: '#2D8C88'\n      },\n      minimal: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: 'none',\n        hoverColor: 'rgba(45, 140, 136, 0.1)'\n      },\n      dark: {\n        backgroundColor: '#333',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#555'\n      }\n    };\n    const buttonSizes = {\n      small: {\n        padding: '8px 16px',\n        fontSize: '14px'\n      },\n      medium: {\n        padding: '12px 24px',\n        fontSize: '16px'\n      },\n      large: {\n        padding: '16px 32px',\n        fontSize: '18px'\n      }\n    };\n    const style = buttonStyles[buttonStyle];\n    const size = buttonSizes[buttonSize];\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '${productSize}', productType = '${productType}') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('${productImageUrl}', '${productSize}', '${productType}')\"\n  style=\"\n    background-color: ${style.backgroundColor};\n    color: ${style.color};\n    border: ${style.border};\n    padding: ${size.padding};\n    font-size: ${size.fontSize};\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='${style.hoverColor}'\"\n  onmouseout=\"this.style.backgroundColor='${style.backgroundColor}'\"\n>\n  ${buttonText}\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: openAddModal,\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [\"+\", stats.newClientsThisMonth, \" new\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [stats.activeRate.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics;\n                    return sum + (((_c$analytics = c.analytics) === null || _c$analytics === void 0 ? void 0 : _c$analytics.totalSessions) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.tryOnsGrowth >= 0 ? '+' : '', stats.tryOnsGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users (by IP)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : stats.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Activity, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-blue-600\",\n                children: [(uniqueUsersData === null || uniqueUsersData === void 0 ? void 0 : (_uniqueUsersData$summ2 = uniqueUsersData.summary) === null || _uniqueUsersData$summ2 === void 0 ? void 0 : (_uniqueUsersData$summ3 = _uniqueUsersData$summ2.avgSessionsPerUser) === null || _uniqueUsersData$summ3 === void 0 ? void 0 : _uniqueUsersData$summ3.toFixed(1)) || '0', \" avg sessions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"per user\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showModal && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: editingClient ? 'Edit Client' : 'Add New Client'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowModal(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: editingClient ? handleEditClient : handleAddClient,\n                  className: \"space-y-5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Company Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"companyName\",\n                        value: clientForm.companyName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter company name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Contact Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"contactName\",\n                        value: clientForm.contactName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter contact name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"email\",\n                        name: \"email\",\n                        value: clientForm.email,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter email address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Phone\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        name: \"phone\",\n                        value: clientForm.phone,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter phone number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Website\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"url\",\n                        name: \"website\",\n                        value: clientForm.website,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"https://example.com\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 693,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Industry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"industry\",\n                        value: clientForm.industry,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"e.g., Fashion, Jewelry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Product Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"productType\",\n                        value: clientForm.productType,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"watches\",\n                          children: \"Watches\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 724,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"bracelets\",\n                          children: \"Bracelets\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 725,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"both\",\n                          children: \"Both\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 726,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 718,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Subscription Plan\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"subscriptionPlan\",\n                        value: clientForm.subscriptionPlan,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"basic\",\n                          children: \"Basic\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"premium\",\n                          children: \"Premium\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 738,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"enterprise\",\n                          children: \"Enterprise\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 739,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 23\n                  }, this), !editingClient && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"password\",\n                        value: clientForm.password,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: handleSuggestPassword,\n                        className: \"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\",\n                        children: \"Generate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 757,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-end space-x-3 pt-4 border-t border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowModal(false),\n                      className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 770,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"submit\",\n                      className: \"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\",\n                      children: editingClient ? 'Update Client' : 'Create Client'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 777,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Conversion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 825,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 21\n                }, this) : error ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-red-600\",\n                    children: [\"Error loading clients: \", error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this) : clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-gray-500\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 21\n                }, this) : clients.map(client => {\n                  var _client$companyName, _client$analytics, _client$analytics$tot, _client$analytics2, _client$analytics3, _client$analytics3$to, _client$analytics4, _client$analytics5, _client$analytics6, _client$analytics7;\n                  return /*#__PURE__*/_jsxDEV(motion.tr, {\n                    initial: {\n                      opacity: 0\n                    },\n                    animate: {\n                      opacity: 1\n                    },\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 h-10 w-10\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                            children: ((_client$companyName = client.companyName) === null || _client$companyName === void 0 ? void 0 : _client$companyName.charAt(0)) || 'C'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 861,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 860,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"ml-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: client.companyName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 866,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: client.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 867,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500 lg:hidden\",\n                            children: [((_client$analytics = client.analytics) === null || _client$analytics === void 0 ? void 0 : (_client$analytics$tot = _client$analytics.totalSessions) === null || _client$analytics$tot === void 0 ? void 0 : _client$analytics$tot.toLocaleString()) || '0', \" try-ons \\u2022 \", ((_client$analytics2 = client.analytics) === null || _client$analytics2 === void 0 ? void 0 : _client$analytics2.uniqueUsers) || '0', \" unique users\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 868,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 865,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 859,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: ((_client$analytics3 = client.analytics) === null || _client$analytics3 === void 0 ? void 0 : (_client$analytics3$to = _client$analytics3.totalSessions) === null || _client$analytics3$to === void 0 ? void 0 : _client$analytics3$to.toLocaleString()) || '0'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 875,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics4 = client.analytics) === null || _client$analytics4 === void 0 ? void 0 : _client$analytics4.productCount) || '0', \" products\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 876,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [((_client$analytics5 = client.analytics) === null || _client$analytics5 === void 0 ? void 0 : _client$analytics5.conversionRate) || '0', \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 879,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics6 = client.analytics) === null || _client$analytics6 === void 0 ? void 0 : _client$analytics6.uniqueUsers) || '0', \" unique users\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 880,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 878,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                          children: client.subscriptionStatus\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 884,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatLastActive((_client$analytics7 = client.analytics) === null || _client$analytics7 === void 0 ? void 0 : _client$analytics7.lastActive)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 891,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 883,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 882,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' : client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n                        children: client.subscriptionPlan\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 895,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\",\n                          onClick: () => handleViewDetails(client),\n                          title: \"View Details\",\n                          children: /*#__PURE__*/_jsxDEV(Eye, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 909,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 904,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\",\n                          onClick: () => handleViewCode(client),\n                          title: \"Integration Code\",\n                          children: /*#__PURE__*/_jsxDEV(Code, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 916,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 911,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\",\n                          onClick: () => openEditModal(client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 923,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 918,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors\",\n                          onClick: () => handleDeleteClient(client._id),\n                          title: \"Delete Client\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 930,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 925,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 903,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 902,\n                      columnNumber: 25\n                    }, this)]\n                  }, client._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showDetailsPopup && selectedClientForDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold\",\n                      children: ((_selectedClientForDet = selectedClientForDetails.companyName) === null || _selectedClientForDet === void 0 ? void 0 : _selectedClientForDet.charAt(0)) || 'C'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 961,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: selectedClientForDetails.companyName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 965,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/80 text-sm\",\n                        children: selectedClientForDetails.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 964,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowDetailsPopup(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 977,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\",\n                children: loadingAnalytics ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center py-12\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Users, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 995,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 994,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-blue-600\",\n                            children: \"Total Sessions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 998,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-blue-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$tota = clientAnalytics.totalSessions) === null || _clientAnalytics$tota === void 0 ? void 0 : _clientAnalytics$tota.toLocaleString()) || '0'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 999,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 997,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 992,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Clock, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1009,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1008,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-green-600\",\n                            children: \"Avg Duration\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1012,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-green-900\",\n                            children: [Math.round((clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.avgDuration) || 0), \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1013,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1011,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1007,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1006,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1023,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1022,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-purple-600\",\n                            children: \"Interactions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1026,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-purple-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$tota2 = clientAnalytics.totalInteractions) === null || _clientAnalytics$tota2 === void 0 ? void 0 : _clientAnalytics$tota2.toLocaleString()) || '0'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1027,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1025,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1021,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1020,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Activity, {\n                            className: \"h-5 w-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1037,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1036,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-orange-600\",\n                            children: \"Conversion Rate\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1040,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xl font-bold text-orange-900\",\n                            children: [(clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.conversionRate) || '0', \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1041,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1039,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Activity, {\n                        className: \"h-5 w-5 mr-2 text-[#2D8C88]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1052,\n                        columnNumber: 29\n                      }, this), \"Client Summary\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1051,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Total Sessions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1058,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-bold text-gray-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$tota3 = clientAnalytics.totalSessions) === null || _clientAnalytics$tota3 === void 0 ? void 0 : _clientAnalytics$tota3.toLocaleString()) || '0'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1059,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1057,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Average Duration\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1064,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-bold text-gray-900\",\n                            children: [Math.round((clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.avgDuration) || 0), \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1065,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1063,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Total Interactions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1070,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-bold text-gray-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$tota4 = clientAnalytics.totalInteractions) === null || _clientAnalytics$tota4 === void 0 ? void 0 : _clientAnalytics$tota4.toLocaleString()) || '0'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1071,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1069,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1056,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Conversion Rate\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1078,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-bold text-gray-900\",\n                            children: [(clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.conversionRate) || '0', \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1079,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1077,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Unique Users\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1084,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-bold text-gray-900\",\n                            children: (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : (_clientAnalytics$uniq = clientAnalytics.uniqueUsers) === null || _clientAnalytics$uniq === void 0 ? void 0 : _clientAnalytics$uniq.toLocaleString()) || '0'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1085,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1083,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between py-2 px-3 bg-white rounded-lg\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Last Active\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1090,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-bold text-gray-900\",\n                            children: clientAnalytics !== null && clientAnalytics !== void 0 && clientAnalytics.lastActive ? new Date(clientAnalytics.lastActive).toLocaleDateString() : 'Never'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1091,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1089,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1076,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1055,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 25\n                  }, this), (clientAnalytics === null || clientAnalytics === void 0 ? void 0 : clientAnalytics.deviceBreakdown) && clientAnalytics.deviceBreakdown.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Monitor, {\n                        className: \"h-5 w-5 mr-2 text-[#2D8C88]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1103,\n                        columnNumber: 31\n                      }, this), \"Device Usage\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1102,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                      children: clientAnalytics.deviceBreakdown.map((device, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-4\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                            children: device.device || 'Unknown'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1110,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [device.sessions, \" sessions\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1113,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1109,\n                          columnNumber: 35\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1108,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1106,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1101,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showCodePopup && selectedClientForCode && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Code, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1150,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1149,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Integration Code\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1153,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/80 text-sm\",\n                        children: selectedClientForCode.companyName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1156,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1152,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowCodePopup(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1165,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1161,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-blue-900 mb-2\",\n                      children: \"How to integrate ViatrOn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1175,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2 text-sm text-blue-800\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"1. Copy the code below and paste it into your website's HTML\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1179,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1180,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"3. Customize the button styling to match your brand\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1181,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"4. Test the integration to ensure it works correctly\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1182,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1178,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Integration Code\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1189,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: copyCodeToClipboard,\n                        className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\",\n                        children: copiedCode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Check, {\n                            className: \"h-4 w-4 mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1198,\n                            columnNumber: 33\n                          }, this), \"Copied!\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Copy, {\n                            className: \"h-4 w-4 mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1203,\n                            columnNumber: 33\n                          }, this), \"Copy Code\"]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1192,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1188,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gray-900 rounded-xl p-4 overflow-x-auto\",\n                      children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"text-green-400 text-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"code\", {\n                          children: generateIntegrationCode(selectedClientForCode)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1212,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1211,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1210,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1187,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-3\",\n                      children: \"Example Usage\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1219,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 border\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-700 mb-2\",\n                          children: \"For Watches:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1224,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                          className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                          children: \"openViaTryon('https://example.com/watch.jpg', '42', 'watches')\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1225,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1223,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 border\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-700 mb-2\",\n                          children: \"For Bracelets:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1230,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                          className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                          children: \"openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1231,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1229,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1222,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1218,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-50 rounded-xl p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-yellow-900 mb-2\",\n                      children: \"Need Help?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1240,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-yellow-800\",\n                      children: \"If you need assistance with the integration, please contact our support team or refer to our documentation.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1243,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1172,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1139,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 492,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"GA9SqiyWeMa5eupQPgRf5n403oE=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "AnimatePresence", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "X", "Copy", "Check", "BarChart3", "Clock", "Smartphone", "Monitor", "Activity", "Calendar", "Target", "Zap", "MapPin", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "_uniqueUsersData$summ2", "_uniqueUsersData$summ3", "_selectedClientForDet", "_clientAnalytics$tota", "_clientAnalytics$tota2", "_clientAnalytics$tota3", "_clientAnalytics$tota4", "_clientAnalytics$uniq", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "editingClient", "setEditingClient", "clients", "setClients", "loading", "setLoading", "error", "setError", "stats", "setStats", "newClientsThisMonth", "activeRate", "tryOnsGrowth", "uniqueUsers", "uniqueUsersData", "setUniqueUsersData", "showDetailsPopup", "setShowDetailsPopup", "showCodePopup", "setShowCodePopup", "selectedClientForDetails", "setSelectedClientForDetails", "selectedClientForCode", "setSelectedClientForCode", "clientAnalytics", "setClientAnalytics", "loadingAnalytics", "setLoadingAnalytics", "copiedCode", "setCopiedCode", "codeOptions", "setCodeOptions", "productImageUrl", "productSize", "productType", "buttonStyle", "buttonSize", "buttonText", "clientForm", "setClientForm", "companyName", "contactName", "website", "email", "phone", "industry", "subscriptionPlan", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "fetchClients", "_clientsData$stats", "_clientsData$stats2", "_clientsData$stats3", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "params", "URLSearchParams", "append", "clientsResponse", "uniqueUsersResponse", "Promise", "all", "fetch", "headers", "ok", "errorData", "json", "message", "clientsData", "uniqueUsersCount", "_uniqueUsersData$summ", "summary", "totalUniqueUsers", "err", "console", "formatLastActive", "date", "now", "Date", "lastActive", "diffInHours", "diffInDays", "diffInWeeks", "handleFormChange", "e", "name", "value", "target", "prev", "handleSuggestPassword", "resetForm", "handleAddClient", "preventDefault", "response", "method", "body", "JSON", "stringify", "handleEditClient", "_id", "handleDeleteClient", "clientId", "window", "confirm", "openEditModal", "client", "openAddModal", "fetchClientAnalytics", "_aggregatedData", "_clientSpecificData", "_clientSpecificData$a", "_aggregatedData2", "_aggregatedData3", "_aggregatedData4", "_clientSpecificData2", "_clientSpecificData2$", "_aggregatedData5", "_aggregatedData6", "_clientSpecificData3", "_clientSpecificData3$", "_aggregatedData7", "_aggregatedData8", "end", "start", "setDate", "getDate", "clientDetailResponse", "adminClientsResponse", "toISOString", "clientSpecificData", "aggregatedData", "allClientsData", "find", "clientName", "combinedAnalytics", "clientDetail", "totalSessions", "sessions", "analytics", "avgDuration", "totalInteractions", "conversionRate", "deviceBreakdown", "recentSessions", "handleViewDetails", "handleViewCode", "generateIntegrationCode", "REACT_APP_FRONTEND_URL", "location", "origin", "buttonStyles", "primary", "backgroundColor", "color", "border", "hoverColor", "outline", "minimal", "dark", "buttonSizes", "small", "padding", "fontSize", "medium", "large", "style", "size", "copyCodeToClipboard", "code", "navigator", "clipboard", "writeText", "then", "setTimeout", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "filter", "c", "subscriptionStatus", "toFixed", "reduce", "sum", "_c$analytics", "toLocaleString", "avgSessionsPerUser", "exit", "scale", "onSubmit", "type", "onChange", "required", "placeholder", "colSpan", "map", "_client$companyName", "_client$analytics", "_client$analytics$tot", "_client$analytics2", "_client$analytics3", "_client$analytics3$to", "_client$analytics4", "_client$analytics5", "_client$analytics6", "_client$analytics7", "tr", "productCount", "title", "round", "toLocaleDateString", "device", "index", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code,\n  X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity,\n  Calendar, Target, Zap, MapPin, ChevronRight\n} from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [codeOptions, setCodeOptions] = useState({\n    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',\n    productSize: '42',\n    productType: 'watches',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    buttonText: 'Try On Virtually'\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients?${params}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = uniqueUsersData.summary?.totalUniqueUsers || 0;\n      }\n\n      setStats({\n        newClientsThisMonth: clientsData.stats?.newClientsThisMonth || 0,\n        activeRate: clientsData.stats?.activeRate || 0,\n        tryOnsGrowth: clientsData.stats?.tryOnsGrowth || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = (date) => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n\n  const handleAddClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEditClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClient = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const openEditModal = (client) => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '', // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async (clientId) => {\n    try {\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n\n      // Use admin endpoints to get specific client data and admin clients endpoint for aggregated data\n      const [clientDetailResponse, adminClientsResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients/${clientId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      let clientSpecificData = null;\n      let aggregatedData = null;\n\n      if (clientDetailResponse.ok) {\n        clientSpecificData = await clientDetailResponse.json();\n      }\n\n      if (adminClientsResponse.ok) {\n        const allClientsData = await adminClientsResponse.json();\n        // Find the specific client in the aggregated data\n        aggregatedData = allClientsData.find(client =>\n          client.clientId === clientId ||\n          client._id === clientId ||\n          client.clientName === selectedClientForDetails?.companyName ||\n          client.email === selectedClientForDetails?.email\n        );\n      }\n\n      // Combine the data sources\n      const combinedAnalytics = {\n        clientDetail: clientSpecificData,\n        aggregatedData: aggregatedData,\n        totalSessions: aggregatedData?.sessions || clientSpecificData?.analytics?.totalSessions || 0,\n        avgDuration: aggregatedData?.avgDuration || 0,\n        totalInteractions: aggregatedData?.totalInteractions || 0,\n        conversionRate: aggregatedData?.conversionRate || clientSpecificData?.analytics?.conversionRate || 0,\n        uniqueUsers: aggregatedData?.uniqueUsers || 0,\n        lastActive: aggregatedData?.lastActive || clientSpecificData?.analytics?.lastActive,\n        deviceBreakdown: aggregatedData?.deviceBreakdown || [],\n        recentSessions: aggregatedData?.recentSessions || []\n      };\n\n      setClientAnalytics(combinedAnalytics);\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = (client) => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = (client) => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = (client) => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    const { productImageUrl, productSize, productType, buttonStyle, buttonSize, buttonText } = codeOptions;\n\n    const buttonStyles = {\n      primary: {\n        backgroundColor: '#2D8C88',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#236b68'\n      },\n      outline: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: '2px solid #2D8C88',\n        hoverColor: '#2D8C88'\n      },\n      minimal: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: 'none',\n        hoverColor: 'rgba(45, 140, 136, 0.1)'\n      },\n      dark: {\n        backgroundColor: '#333',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#555'\n      }\n    };\n\n    const buttonSizes = {\n      small: { padding: '8px 16px', fontSize: '14px' },\n      medium: { padding: '12px 24px', fontSize: '16px' },\n      large: { padding: '16px 32px', fontSize: '18px' }\n    };\n\n    const style = buttonStyles[buttonStyle];\n    const size = buttonSizes[buttonSize];\n\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '${productSize}', productType = '${productType}') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('${productImageUrl}', '${productSize}', '${productType}')\"\n  style=\"\n    background-color: ${style.backgroundColor};\n    color: ${style.color};\n    border: ${style.border};\n    padding: ${size.padding};\n    font-size: ${size.fontSize};\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='${style.hoverColor}'\"\n  onmouseout=\"this.style.backgroundColor='${style.backgroundColor}'\"\n>\n  ${buttonText}\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={openAddModal}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+{stats.newClientsThisMonth} new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">{stats.activeRate.toFixed(1)}%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users (by IP)</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : stats.uniqueUsers.toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\n                  <Activity className=\"h-6 w-6 text-purple-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-blue-600\">\n                  {uniqueUsersData?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} avg sessions\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">per user</span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add/Edit Client Modal */}\n          <AnimatePresence>\n            {showModal && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <h2 className=\"text-xl font-bold text-white\">\n                        {editingClient ? 'Edit Client' : 'Add New Client'}\n                      </h2>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowModal(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Form */}\n                  <div className=\"p-6\">\n                    <form onSubmit={editingClient ? handleEditClient : handleAddClient} className=\"space-y-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Company Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"companyName\"\n                            value={clientForm.companyName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter company name\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Contact Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"contactName\"\n                            value={clientForm.contactName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter contact name\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Email</label>\n                          <input\n                            type=\"email\"\n                            name=\"email\"\n                            value={clientForm.email}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter email address\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Phone</label>\n                          <input\n                            type=\"tel\"\n                            name=\"phone\"\n                            value={clientForm.phone}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter phone number\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Website</label>\n                          <input\n                            type=\"url\"\n                            name=\"website\"\n                            value={clientForm.website}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"https://example.com\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Industry</label>\n                          <input\n                            type=\"text\"\n                            name=\"industry\"\n                            value={clientForm.industry}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"e.g., Fashion, Jewelry\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Product Type</label>\n                          <select\n                            name=\"productType\"\n                            value={clientForm.productType}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"watches\">Watches</option>\n                            <option value=\"bracelets\">Bracelets</option>\n                            <option value=\"both\">Both</option>\n                          </select>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Subscription Plan</label>\n                          <select\n                            name=\"subscriptionPlan\"\n                            value={clientForm.subscriptionPlan}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"basic\">Basic</option>\n                            <option value=\"premium\">Premium</option>\n                            <option value=\"enterprise\">Enterprise</option>\n                          </select>\n                        </div>\n                      </div>\n\n                      {!editingClient && (\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Password</label>\n                          <div className=\"flex gap-3\">\n                            <input\n                              type=\"text\"\n                              name=\"password\"\n                              value={clientForm.password}\n                              onChange={handleFormChange}\n                              required\n                              className=\"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                              placeholder=\"Enter password\"\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={handleSuggestPassword}\n                              className=\"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\"\n                            >\n                              Generate\n                            </button>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Action Buttons */}\n                      <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-100\">\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowModal(false)}\n                          className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                        >\n                          Cancel\n                        </button>\n                        <button\n                          type=\"submit\"\n                          className=\"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\"\n                        >\n                          {editingClient ? 'Update Client' : 'Create Client'}\n                        </button>\n                      </div>\n                    </form>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Conversion</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {loading ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center\">\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"></div>\n                      </td>\n                    </tr>\n                  ) : error ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-red-600\">\n                        Error loading clients: {error}\n                      </td>\n                    </tr>\n                  ) : clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-gray-500\">\n                        No clients found\n                      </td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <motion.tr\n                        key={client._id}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"hover:bg-gray-50\"\n                      >\n                        <td className=\"px-4 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                                {client.companyName?.charAt(0) || 'C'}\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{client.companyName}</div>\n                              <div className=\"text-sm text-gray-500\">{client.email}</div>\n                              <div className=\"text-sm text-gray-500 lg:hidden\">\n                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.uniqueUsers || '0'} unique users\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.productCount || '0'} products</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.conversionRate || '0'}%</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.uniqueUsers || '0'} unique users</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                              'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {client.subscriptionStatus}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{formatLastActive(client.analytics?.lastActive)}</span>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {client.subscriptionPlan}\n                          </span>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex justify-end space-x-2\">\n                            <button\n                              className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\"\n                              onClick={() => handleViewDetails(client)}\n                              title=\"View Details\"\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\"\n                              onClick={() => handleViewCode(client)}\n                              title=\"Integration Code\"\n                            >\n                              <Code className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\"\n                              onClick={() => openEditModal(client)}\n                              title=\"Edit Client\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors\"\n                              onClick={() => handleDeleteClient(client._id)}\n                              title=\"Delete Client\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* View Details Popup */}\n          <AnimatePresence>\n            {showDetailsPopup && selectedClientForDetails && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold\">\n                          {selectedClientForDetails.companyName?.charAt(0) || 'C'}\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl font-bold text-white\">\n                            {selectedClientForDetails.companyName}\n                          </h2>\n                          <p className=\"text-white/80 text-sm\">\n                            {selectedClientForDetails.email}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowDetailsPopup(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-80px)]\">\n                    {loadingAnalytics ? (\n                      <div className=\"flex items-center justify-center py-12\">\n                        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\n                      </div>\n                    ) : (\n                      <div className=\"space-y-6\">\n                        {/* Client Info */}\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                          <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center\">\n                                <Users className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-blue-600\">Total Sessions</p>\n                                <p className=\"text-xl font-bold text-blue-900\">\n                                  {clientAnalytics?.totalSessions?.toLocaleString() || '0'}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center\">\n                                <Clock className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-green-600\">Avg Duration</p>\n                                <p className=\"text-xl font-bold text-green-900\">\n                                  {Math.round(clientAnalytics?.avgDuration || 0)}s\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center\">\n                                <BarChart3 className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-purple-600\">Interactions</p>\n                                <p className=\"text-xl font-bold text-purple-900\">\n                                  {clientAnalytics?.totalInteractions?.toLocaleString() || '0'}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center\">\n                                <Activity className=\"h-5 w-5 text-white\" />\n                              </div>\n                              <div>\n                                <p className=\"text-sm font-medium text-orange-600\">Conversion Rate</p>\n                                <p className=\"text-xl font-bold text-orange-900\">\n                                  {clientAnalytics?.conversionRate || '0'}%\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Client Summary */}\n                        <div className=\"bg-gray-50 rounded-xl p-6\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                            <Activity className=\"h-5 w-5 mr-2 text-[#2D8C88]\" />\n                            Client Summary\n                          </h3>\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div className=\"space-y-4\">\n                              <div className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <span className=\"text-sm font-medium text-gray-700\">Total Sessions</span>\n                                <span className=\"text-sm font-bold text-gray-900\">\n                                  {clientAnalytics?.totalSessions?.toLocaleString() || '0'}\n                                </span>\n                              </div>\n                              <div className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <span className=\"text-sm font-medium text-gray-700\">Average Duration</span>\n                                <span className=\"text-sm font-bold text-gray-900\">\n                                  {Math.round(clientAnalytics?.avgDuration || 0)}s\n                                </span>\n                              </div>\n                              <div className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <span className=\"text-sm font-medium text-gray-700\">Total Interactions</span>\n                                <span className=\"text-sm font-bold text-gray-900\">\n                                  {clientAnalytics?.totalInteractions?.toLocaleString() || '0'}\n                                </span>\n                              </div>\n                            </div>\n                            <div className=\"space-y-4\">\n                              <div className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <span className=\"text-sm font-medium text-gray-700\">Conversion Rate</span>\n                                <span className=\"text-sm font-bold text-gray-900\">\n                                  {clientAnalytics?.conversionRate || '0'}%\n                                </span>\n                              </div>\n                              <div className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <span className=\"text-sm font-medium text-gray-700\">Unique Users</span>\n                                <span className=\"text-sm font-bold text-gray-900\">\n                                  {clientAnalytics?.uniqueUsers?.toLocaleString() || '0'}\n                                </span>\n                              </div>\n                              <div className=\"flex items-center justify-between py-2 px-3 bg-white rounded-lg\">\n                                <span className=\"text-sm font-medium text-gray-700\">Last Active</span>\n                                <span className=\"text-sm font-bold text-gray-900\">\n                                  {clientAnalytics?.lastActive ? new Date(clientAnalytics.lastActive).toLocaleDateString() : 'Never'}\n                                </span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Device Breakdown */}\n                        {clientAnalytics?.deviceBreakdown && clientAnalytics.deviceBreakdown.length > 0 && (\n                          <div className=\"bg-gray-50 rounded-xl p-6\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                              <Monitor className=\"h-5 w-5 mr-2 text-[#2D8C88]\" />\n                              Device Usage\n                            </h3>\n                            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                              {clientAnalytics.deviceBreakdown.map((device, index) => (\n                                <div key={index} className=\"bg-white rounded-lg p-4\">\n                                  <div className=\"flex items-center justify-between\">\n                                    <span className=\"text-sm font-medium text-gray-900 capitalize\">\n                                      {device.device || 'Unknown'}\n                                    </span>\n                                    <span className=\"text-sm text-gray-600\">\n                                      {device.sessions} sessions\n                                    </span>\n                                  </div>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* View Code Popup */}\n          <AnimatePresence>\n            {showCodePopup && selectedClientForCode && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center\">\n                          <Code className=\"h-5 w-5 text-white\" />\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl font-bold text-white\">\n                            Integration Code\n                          </h2>\n                          <p className=\"text-white/80 text-sm\">\n                            {selectedClientForCode.companyName}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowCodePopup(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <div className=\"space-y-6\">\n                      {/* Instructions */}\n                      <div className=\"bg-blue-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">\n                          How to integrate ViatrOn\n                        </h3>\n                        <div className=\"space-y-2 text-sm text-blue-800\">\n                          <p>1. Copy the code below and paste it into your website's HTML</p>\n                          <p>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs</p>\n                          <p>3. Customize the button styling to match your brand</p>\n                          <p>4. Test the integration to ensure it works correctly</p>\n                        </div>\n                      </div>\n\n                      {/* Code Block */}\n                      <div className=\"relative\">\n                        <div className=\"flex items-center justify-between mb-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">\n                            Integration Code\n                          </h3>\n                          <button\n                            onClick={copyCodeToClipboard}\n                            className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\"\n                          >\n                            {copiedCode ? (\n                              <>\n                                <Check className=\"h-4 w-4 mr-2\" />\n                                Copied!\n                              </>\n                            ) : (\n                              <>\n                                <Copy className=\"h-4 w-4 mr-2\" />\n                                Copy Code\n                              </>\n                            )}\n                          </button>\n                        </div>\n\n                        <div className=\"bg-gray-900 rounded-xl p-4 overflow-x-auto\">\n                          <pre className=\"text-green-400 text-sm\">\n                            <code>{generateIntegrationCode(selectedClientForCode)}</code>\n                          </pre>\n                        </div>\n                      </div>\n\n                      {/* Example Usage */}\n                      <div className=\"bg-gray-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                          Example Usage\n                        </h3>\n                        <div className=\"space-y-3\">\n                          <div className=\"bg-white rounded-lg p-3 border\">\n                            <p className=\"text-sm font-medium text-gray-700 mb-2\">For Watches:</p>\n                            <code className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                              openViaTryon('https://example.com/watch.jpg', '42', 'watches')\n                            </code>\n                          </div>\n                          <div className=\"bg-white rounded-lg p-3 border\">\n                            <p className=\"text-sm font-medium text-gray-700 mb-2\">For Bracelets:</p>\n                            <code className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                              openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')\n                            </code>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Support Info */}\n                      <div className=\"bg-yellow-50 rounded-xl p-4\">\n                        <h3 className=\"text-lg font-semibold text-yellow-900 mb-2\">\n                          Need Help?\n                        </h3>\n                        <p className=\"text-sm text-yellow-800\">\n                          If you need assistance with the integration, please contact our support team or refer to our documentation.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAC/DC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAC/DC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QACtC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqE,KAAK,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAC;IACjCuE,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAC9E,MAAM,CAACmF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyF,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC;IAC7C6F,eAAe,EAAE,wBAAwB;IACzCC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpG,QAAQ,CAAC;IAC3CqG,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTtE,QAAQ,EAAE,EAAE;IACZuE,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZX,WAAW,EAAE,SAAS;IACtBY,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxD,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0D,UAAU,GAAGxD,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACApD,SAAS,CAAC,MAAM;IACd6G,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACvD,WAAW,EAAEE,cAAc,CAAC,CAAC;EAEjC,MAAMqD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMO,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIvE,WAAW,EAAEsE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAExE,WAAW,CAAC;MACrD,IAAIE,cAAc,KAAK,KAAK,EAAEoE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEtE,cAAc,CAAC;;MAErE;MACA,MAAM,CAACuE,eAAe,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DC,KAAK,CAAC,GAAGV,MAAM,gBAAgBG,MAAM,EAAE,EAAE;QACvCQ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,mCAAmC,EAAE;QAClDW,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,IAAI,CAACc,eAAe,CAACM,EAAE,EAAE;QACvB,MAAMC,SAAS,GAAG,MAAMP,eAAe,CAACQ,IAAI,CAAC,CAAC;QAC9C,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMC,WAAW,GAAG,MAAMV,eAAe,CAACQ,IAAI,CAAC,CAAC;MAChDxE,UAAU,CAAC0E,WAAW,CAAC3E,OAAO,IAAI,EAAE,CAAC;;MAErC;MACA,IAAI4E,gBAAgB,GAAG,CAAC;MACxB,IAAIV,mBAAmB,CAACK,EAAE,EAAE;QAAA,IAAAM,qBAAA;QAC1B,MAAMjE,eAAe,GAAG,MAAMsD,mBAAmB,CAACO,IAAI,CAAC,CAAC;QACxD5D,kBAAkB,CAACD,eAAe,CAAC;QACnCgE,gBAAgB,GAAG,EAAAC,qBAAA,GAAAjE,eAAe,CAACkE,OAAO,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBE,gBAAgB,KAAI,CAAC;MACnE;MAEAxE,QAAQ,CAAC;QACPC,mBAAmB,EAAE,EAAAwC,kBAAA,GAAA2B,WAAW,CAACrE,KAAK,cAAA0C,kBAAA,uBAAjBA,kBAAA,CAAmBxC,mBAAmB,KAAI,CAAC;QAChEC,UAAU,EAAE,EAAAwC,mBAAA,GAAA0B,WAAW,CAACrE,KAAK,cAAA2C,mBAAA,uBAAjBA,mBAAA,CAAmBxC,UAAU,KAAI,CAAC;QAC9CC,YAAY,EAAE,EAAAwC,mBAAA,GAAAyB,WAAW,CAACrE,KAAK,cAAA4C,mBAAA,uBAAjBA,mBAAA,CAAmBxC,YAAY,KAAI,CAAC;QAClDC,WAAW,EAAEiE;MACf,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,yBAAyB,EAAE4E,GAAG,CAAC;MAC7C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;MACrBzE,UAAU,CAAC,EAAE,CAAC;MACdM,QAAQ,CAAC;QACPC,mBAAmB,EAAE,CAAC;QACtBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IACjC,MAAMI,WAAW,GAAGjH,IAAI,CAACC,KAAK,CAAC,CAAC6G,GAAG,GAAGE,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErE,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMC,UAAU,GAAGlH,IAAI,CAACC,KAAK,CAACgH,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,WAAW;IACnD,MAAMC,WAAW,GAAGnH,IAAI,CAACC,KAAK,CAACiH,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,YAAY;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzD,aAAa,CAAC0D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC3D,aAAa,CAAC0D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5H,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAMgI,SAAS,GAAGA,CAAA,KAAM;IACtB5D,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTtE,QAAQ,EAAE,EAAE;MACZuE,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,SAAS;MACtBY,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACF7C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmG,eAAe,GAAG,MAAOP,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,cAAc,EAAE;QACpD0C,MAAM,EAAE,MAAM;QACd/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACgE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBlD,YAAY,CAAC,KAAK,CAAC;MACnBoG,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAE4E,GAAG,CAAC;MAC5C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsG,gBAAgB,GAAG,MAAOd,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgB7D,aAAa,CAAC4G,GAAG,EAAE,EAAE;QACzEL,MAAM,EAAE,KAAK;QACb/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACgE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBlD,YAAY,CAAC,KAAK,CAAC;MACnBoG,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAE4E,GAAG,CAAC;MAC5C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwG,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MACnE;IACF;IAEA,IAAI;MACF3G,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgBiD,QAAQ,EAAE,EAAE;QAChEP,MAAM,EAAE,QAAQ;QAChB/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACiD,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZC,OAAO,CAAC7E,KAAK,CAAC,wBAAwB,EAAE4E,GAAG,CAAC;MAC5C3E,QAAQ,CAAC2E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4G,aAAa,GAAIC,MAAM,IAAK;IAChCjH,gBAAgB,CAACiH,MAAM,CAAC;IACxB3E,aAAa,CAAC;MACZC,WAAW,EAAE0E,MAAM,CAAC1E,WAAW,IAAI,EAAE;MACrCC,WAAW,EAAEyE,MAAM,CAACzE,WAAW,IAAI,EAAE;MACrCC,OAAO,EAAEwE,MAAM,CAACxE,OAAO,IAAI,EAAE;MAC7BC,KAAK,EAAEuE,MAAM,CAACvE,KAAK,IAAI,EAAE;MACzBtE,QAAQ,EAAE,EAAE;MAAE;MACduE,KAAK,EAAEsE,MAAM,CAACtE,KAAK,IAAI,EAAE;MACzBC,QAAQ,EAAEqE,MAAM,CAACrE,QAAQ,IAAI,EAAE;MAC/BX,WAAW,EAAEgF,MAAM,CAAChF,WAAW,IAAI,SAAS;MAC5CY,gBAAgB,EAAEoE,MAAM,CAACpE,gBAAgB,IAAI;IAC/C,CAAC,CAAC;IACF/C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoH,YAAY,GAAGA,CAAA,KAAM;IACzBhB,SAAS,CAAC,CAAC;IACXpG,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMqH,oBAAoB,GAAG,MAAON,QAAQ,IAAK;IAC/C,IAAI;MAAA,IAAAO,eAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACFvG,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAM0B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;MAErE;MACA,MAAM0E,GAAG,GAAG,IAAI5C,IAAI,CAAC,CAAC;MACtB,MAAM6C,KAAK,GAAG,IAAI7C,IAAI,CAAC,CAAC;MACxB6C,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;;MAEnC;MACA,MAAM,CAACC,oBAAoB,EAAEC,oBAAoB,CAAC,GAAG,MAAMnE,OAAO,CAACC,GAAG,CAAC,CACrEC,KAAK,CAAC,GAAGV,MAAM,gBAAgBiD,QAAQ,EAAE,EAAE;QACzCtC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,sCAAsCuE,KAAK,CAACK,WAAW,CAAC,CAAC,QAAQN,GAAG,CAACM,WAAW,CAAC,CAAC,EAAE,EAAE;QACnGjE,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,IAAIqF,kBAAkB,GAAG,IAAI;MAC7B,IAAIC,cAAc,GAAG,IAAI;MAEzB,IAAIJ,oBAAoB,CAAC9D,EAAE,EAAE;QAC3BiE,kBAAkB,GAAG,MAAMH,oBAAoB,CAAC5D,IAAI,CAAC,CAAC;MACxD;MAEA,IAAI6D,oBAAoB,CAAC/D,EAAE,EAAE;QAC3B,MAAMmE,cAAc,GAAG,MAAMJ,oBAAoB,CAAC7D,IAAI,CAAC,CAAC;QACxD;QACAgE,cAAc,GAAGC,cAAc,CAACC,IAAI,CAAC3B,MAAM,IACzCA,MAAM,CAACJ,QAAQ,KAAKA,QAAQ,IAC5BI,MAAM,CAACN,GAAG,KAAKE,QAAQ,IACvBI,MAAM,CAAC4B,UAAU,MAAK1H,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAEoB,WAAW,KAC3D0E,MAAM,CAACvE,KAAK,MAAKvB,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAEuB,KAAK,CAClD,CAAC;MACH;;MAEA;MACA,MAAMoG,iBAAiB,GAAG;QACxBC,YAAY,EAAEN,kBAAkB;QAChCC,cAAc,EAAEA,cAAc;QAC9BM,aAAa,EAAE,EAAA5B,eAAA,GAAAsB,cAAc,cAAAtB,eAAA,uBAAdA,eAAA,CAAgB6B,QAAQ,OAAA5B,mBAAA,GAAIoB,kBAAkB,cAAApB,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB6B,SAAS,cAAA5B,qBAAA,uBAA7BA,qBAAA,CAA+B0B,aAAa,KAAI,CAAC;QAC5FG,WAAW,EAAE,EAAA5B,gBAAA,GAAAmB,cAAc,cAAAnB,gBAAA,uBAAdA,gBAAA,CAAgB4B,WAAW,KAAI,CAAC;QAC7CC,iBAAiB,EAAE,EAAA5B,gBAAA,GAAAkB,cAAc,cAAAlB,gBAAA,uBAAdA,gBAAA,CAAgB4B,iBAAiB,KAAI,CAAC;QACzDC,cAAc,EAAE,EAAA5B,gBAAA,GAAAiB,cAAc,cAAAjB,gBAAA,uBAAdA,gBAAA,CAAgB4B,cAAc,OAAA3B,oBAAA,GAAIe,kBAAkB,cAAAf,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBwB,SAAS,cAAAvB,qBAAA,uBAA7BA,qBAAA,CAA+B0B,cAAc,KAAI,CAAC;QACpGzI,WAAW,EAAE,EAAAgH,gBAAA,GAAAc,cAAc,cAAAd,gBAAA,uBAAdA,gBAAA,CAAgBhH,WAAW,KAAI,CAAC;QAC7C2E,UAAU,EAAE,EAAAsC,gBAAA,GAAAa,cAAc,cAAAb,gBAAA,uBAAdA,gBAAA,CAAgBtC,UAAU,OAAAuC,oBAAA,GAAIW,kBAAkB,cAAAX,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBoB,SAAS,cAAAnB,qBAAA,uBAA7BA,qBAAA,CAA+BxC,UAAU;QACnF+D,eAAe,EAAE,EAAAtB,gBAAA,GAAAU,cAAc,cAAAV,gBAAA,uBAAdA,gBAAA,CAAgBsB,eAAe,KAAI,EAAE;QACtDC,cAAc,EAAE,EAAAtB,gBAAA,GAAAS,cAAc,cAAAT,gBAAA,uBAAdA,gBAAA,CAAgBsB,cAAc,KAAI;MACpD,CAAC;MAED/H,kBAAkB,CAACsH,iBAAiB,CAAC;IACvC,CAAC,CAAC,OAAOzI,KAAK,EAAE;MACd6E,OAAO,CAAC7E,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDmB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,SAAS;MACRE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM8H,iBAAiB,GAAIvC,MAAM,IAAK;IACpC7F,2BAA2B,CAAC6F,MAAM,CAAC;IACnCjG,mBAAmB,CAAC,IAAI,CAAC;IACzBmG,oBAAoB,CAACF,MAAM,CAACN,GAAG,CAAC;EAClC,CAAC;;EAED;EACA,MAAM8C,cAAc,GAAIxC,MAAM,IAAK;IACjC3F,wBAAwB,CAAC2F,MAAM,CAAC;IAChC/F,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMwI,uBAAuB,GAAIzC,MAAM,IAAK;IAC1C,MAAMzD,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACiG,sBAAsB,IAAI7C,MAAM,CAAC8C,QAAQ,CAACC,MAAM;IAC5E,MAAM;MAAE9H,eAAe;MAAEC,WAAW;MAAEC,WAAW;MAAEC,WAAW;MAAEC,UAAU;MAAEC;IAAW,CAAC,GAAGP,WAAW;IAEtG,MAAMiI,YAAY,GAAG;MACnBC,OAAO,EAAE;QACPC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE;QACPJ,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE;MACd,CAAC;MACDE,OAAO,EAAE;QACPL,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC;MACDG,IAAI,EAAE;QACJN,eAAe,EAAE,MAAM;QACvBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd;IACF,CAAC;IAED,MAAMI,WAAW,GAAG;MAClBC,KAAK,EAAE;QAAEC,OAAO,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAO,CAAC;MAChDC,MAAM,EAAE;QAAEF,OAAO,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAO,CAAC;MAClDE,KAAK,EAAE;QAAEH,OAAO,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAO;IAClD,CAAC;IAED,MAAMG,KAAK,GAAGf,YAAY,CAAC5H,WAAW,CAAC;IACvC,MAAM4I,IAAI,GAAGP,WAAW,CAACpI,UAAU,CAAC;IAEpC,OAAO;AACX;AACA,wDAAwDH,WAAW,qBAAqBC,WAAW;AACnG,sBAAsBuB,OAAO;AAC7B;AACA,eAAeyD,MAAM,CAACN,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B5E,eAAe,OAAOC,WAAW,OAAOC,WAAW;AAC9E;AACA,wBAAwB4I,KAAK,CAACb,eAAe;AAC7C,aAAaa,KAAK,CAACZ,KAAK;AACxB,cAAcY,KAAK,CAACX,MAAM;AAC1B,eAAeY,IAAI,CAACL,OAAO;AAC3B,iBAAiBK,IAAI,CAACJ,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA,6CAA6CG,KAAK,CAACV,UAAU;AAC7D,4CAA4CU,KAAK,CAACb,eAAe;AACjE;AACA,IAAI5H,UAAU;AACd,UAAU;EACR,CAAC;;EAED;EACA,MAAM2I,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,IAAI,GAAGtB,uBAAuB,CAACrI,qBAAqB,CAAC;IAC3D4J,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAM;MAC7CxJ,aAAa,CAAC,IAAI,CAAC;MACnByJ,UAAU,CAAC,MAAMzJ,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7D,OAAA;IAAKuN,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCxN,OAAA,CAAC3B,YAAY;MAACoP,MAAM,EAAEnM,aAAc;MAACoM,OAAO,EAAEA,CAAA,KAAMnM,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAkM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjI9N,OAAA,CAAC1B,WAAW;MAACyG,aAAa,EAAEA,aAAc;MAACvD,SAAS,EAAEA;IAAU;MAAAmM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnE9N,OAAA;MAAMuN,SAAS,EAAE,GAAGvI,UAAU,oCAAqC;MAAAwI,QAAA,eACjExN,OAAA;QAAKuN,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBxN,OAAA;UAAKuN,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFxN,OAAA;YAAAwN,QAAA,gBACExN,OAAA;cAAIuN,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE9N,OAAA;cAAGuN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACN9N,OAAA;YACEuN,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAE5E,YAAa;YAAAqE,QAAA,gBAEtBxN,OAAA,CAACtB,IAAI;cAAC6O,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9N,OAAA;UAAKuN,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFxN,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxN,OAAA;cAAKuN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxN,OAAA;gBAAAwN,QAAA,gBACExN,OAAA;kBAAGuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClE9N,OAAA;kBAAGuN,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEpL,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACvB;gBAAM;kBAAAgN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACN9N,OAAA;gBAAKuN,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFxN,OAAA,CAAChB,KAAK;kBAACuO,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9N,OAAA;cAAKuN,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxN,OAAA;gBAAMuN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,GAAC,EAAChL,KAAK,CAACE,mBAAmB,EAAC,MAAI;cAAA;gBAAAiL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5F9N,OAAA;gBAAMuN,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb9N,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxN,OAAA;cAAKuN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxN,OAAA;gBAAAwN,QAAA,gBACExN,OAAA;kBAAGuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnE9N,OAAA;kBAAGuN,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEpL,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACqM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB,KAAK,QAAQ,CAAC,CAAC9N;gBAAM;kBAAAgN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,eACN9N,OAAA;gBAAKuN,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFxN,OAAA,CAACjB,UAAU;kBAACwO,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9N,OAAA;cAAKuN,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxN,OAAA;gBAAMuN,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAEhL,KAAK,CAACG,UAAU,CAAC+L,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1F9N,OAAA;gBAAMuN,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb9N,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxN,OAAA;cAAKuN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxN,OAAA;gBAAAwN,QAAA,gBACExN,OAAA;kBAAGuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClE9N,OAAA;kBAAGuN,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEpL,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACyM,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC;oBAAA,IAAAK,YAAA;oBAAA,OAAKD,GAAG,IAAI,EAAAC,YAAA,GAAAL,CAAC,CAACrD,SAAS,cAAA0D,YAAA,uBAAXA,YAAA,CAAa5D,aAAa,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAAC6D,cAAc,CAAC;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrK,CAAC,eACN9N,OAAA;gBAAKuN,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFxN,OAAA,CAACrB,GAAG;kBAAC4O,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9N,OAAA;cAAKuN,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxN,OAAA;gBAAMuN,SAAS,EAAE,uBAAuB/K,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAA4K,QAAA,GACnGhL,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,YAAY,CAAC8L,OAAO,CAAC,CAAC,CAAC,EAAC,GACrE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP9N,OAAA;gBAAMuN,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb9N,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxN,OAAA;cAAKuN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxN,OAAA;gBAAAwN,QAAA,gBACExN,OAAA;kBAAGuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzE9N,OAAA;kBAAGuN,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEpL,OAAO,GAAG,KAAK,GAAGI,KAAK,CAACK,WAAW,CAACiM,cAAc,CAAC;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACN9N,OAAA;gBAAKuN,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvFxN,OAAA,CAACP,QAAQ;kBAAC8N,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9N,OAAA;cAAKuN,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxN,OAAA;gBAAMuN,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChD,CAAA1K,eAAe,aAAfA,eAAe,wBAAAhC,sBAAA,GAAfgC,eAAe,CAAEkE,OAAO,cAAAlG,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BiO,kBAAkB,cAAAhO,sBAAA,uBAA5CA,sBAAA,CAA8C2N,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,eACnE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP9N,OAAA;gBAAMuN,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN9N,OAAA,CAACxB,eAAe;UAAAgP,QAAA,EACb1L,SAAS,iBACR9B,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1FxN,OAAA,CAACzB,MAAM,CAACyP,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,0EAA0E;cAAAC,QAAA,gBAGpFxN,OAAA;gBAAKuN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrExN,OAAA;kBAAKuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDxN,OAAA;oBAAIuN,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EACzCxL,aAAa,GAAG,aAAa,GAAG;kBAAgB;oBAAA2L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACL9N,OAAA;oBACEuN,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAMhM,YAAY,CAAC,KAAK,CAAE;oBAAAyL,QAAA,eAEnCxN,OAAA,CAACd,CAAC;sBAACqO,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9N,OAAA;gBAAKuN,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClBxN,OAAA;kBAAMkP,QAAQ,EAAElN,aAAa,GAAG2G,gBAAgB,GAAGP,eAAgB;kBAACmF,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvFxN,OAAA;oBAAKuN,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDxN,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtF9N,OAAA;wBACEmP,IAAI,EAAC,MAAM;wBACXrH,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAEzD,UAAU,CAACE,WAAY;wBAC9B4K,QAAQ,EAAExH,gBAAiB;wBAC3ByH,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN9N,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtF9N,OAAA;wBACEmP,IAAI,EAAC,MAAM;wBACXrH,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAEzD,UAAU,CAACG,WAAY;wBAC9B2K,QAAQ,EAAExH,gBAAiB;wBAC3ByH,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKuN,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDxN,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/E9N,OAAA;wBACEmP,IAAI,EAAC,OAAO;wBACZrH,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAEzD,UAAU,CAACK,KAAM;wBACxByK,QAAQ,EAAExH,gBAAiB;wBAC3ByH,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAqB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN9N,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/E9N,OAAA;wBACEmP,IAAI,EAAC,KAAK;wBACVrH,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAEzD,UAAU,CAACM,KAAM;wBACxBwK,QAAQ,EAAExH,gBAAiB;wBAC3B2F,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKuN,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDxN,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjF9N,OAAA;wBACEmP,IAAI,EAAC,KAAK;wBACVrH,IAAI,EAAC,SAAS;wBACdC,KAAK,EAAEzD,UAAU,CAACI,OAAQ;wBAC1B0K,QAAQ,EAAExH,gBAAiB;wBAC3B2F,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAqB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN9N,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClF9N,OAAA;wBACEmP,IAAI,EAAC,MAAM;wBACXrH,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAEzD,UAAU,CAACO,QAAS;wBAC3BuK,QAAQ,EAAExH,gBAAiB;wBAC3B2F,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAwB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9N,OAAA;oBAAKuN,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDxN,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtF9N,OAAA;wBACE8H,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAEzD,UAAU,CAACJ,WAAY;wBAC9BkL,QAAQ,EAAExH,gBAAiB;wBAC3B2F,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3JxN,OAAA;0BAAQ+H,KAAK,EAAC,SAAS;0BAAAyF,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxC9N,OAAA;0BAAQ+H,KAAK,EAAC,WAAW;0BAAAyF,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5C9N,OAAA;0BAAQ+H,KAAK,EAAC,MAAM;0BAAAyF,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACN9N,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAOuN,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC3F9N,OAAA;wBACE8H,IAAI,EAAC,kBAAkB;wBACvBC,KAAK,EAAEzD,UAAU,CAACQ,gBAAiB;wBACnCsK,QAAQ,EAAExH,gBAAiB;wBAC3B2F,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3JxN,OAAA;0BAAQ+H,KAAK,EAAC,OAAO;0BAAAyF,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpC9N,OAAA;0BAAQ+H,KAAK,EAAC,SAAS;0BAAAyF,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxC9N,OAAA;0BAAQ+H,KAAK,EAAC,YAAY;0BAAAyF,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAAC9L,aAAa,iBACbhC,OAAA;oBAAAwN,QAAA,gBACExN,OAAA;sBAAOuN,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClF9N,OAAA;sBAAKuN,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBxN,OAAA;wBACEmP,IAAI,EAAC,MAAM;wBACXrH,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAEzD,UAAU,CAACjE,QAAS;wBAC3B+O,QAAQ,EAAExH,gBAAiB;wBAC3ByH,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACF9N,OAAA;wBACEmP,IAAI,EAAC,QAAQ;wBACbpB,OAAO,EAAE7F,qBAAsB;wBAC/BqF,SAAS,EAAC,gGAAgG;wBAAAC,QAAA,EAC3G;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGD9N,OAAA;oBAAKuN,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,gBACvExN,OAAA;sBACEmP,IAAI,EAAC,QAAQ;sBACbpB,OAAO,EAAEA,CAAA,KAAMhM,YAAY,CAAC,KAAK,CAAE;sBACnCwL,SAAS,EAAC,0GAA0G;sBAAAC,QAAA,EACrH;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT9N,OAAA;sBACEmP,IAAI,EAAC,QAAQ;sBACb5B,SAAS,EAAC,yGAAyG;sBAAAC,QAAA,EAElHxL,aAAa,GAAG,eAAe,GAAG;oBAAe;sBAAA2L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlB9N,OAAA;UAAKuN,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDxN,OAAA;YAAKuN,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CxN,OAAA;cAAKuN,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBxN,OAAA;gBACEmP,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/BvH,KAAK,EAAErG,WAAY;gBACnB0N,QAAQ,EAAGvH,CAAC,IAAKlG,cAAc,CAACkG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAChDwF,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9N,OAAA;cAAKuN,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BxN,OAAA;gBACE+H,KAAK,EAAEnG,cAAe;gBACtBwN,QAAQ,EAAGvH,CAAC,IAAKhG,iBAAiB,CAACgG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACnDwF,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5IxN,OAAA;kBAAQ+H,KAAK,EAAC,KAAK;kBAAAyF,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC9N,OAAA;kBAAQ+H,KAAK,EAAC,QAAQ;kBAAAyF,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC9N,OAAA;kBAAQ+H,KAAK,EAAC,SAAS;kBAAAyF,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9N,OAAA;UAAKuN,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DxN,OAAA;YAAKuN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BxN,OAAA;cAAOuN,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpDxN,OAAA;gBAAOuN,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3BxN,OAAA;kBAAAwN,QAAA,gBACExN,OAAA;oBAAIuN,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1G9N,OAAA;oBAAIuN,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChI9N,OAAA;oBAAIuN,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnI9N,OAAA;oBAAIuN,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/H9N,OAAA;oBAAIuN,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpI9N,OAAA;oBAAIuN,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR9N,OAAA;gBAAOuN,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDpL,OAAO,gBACNpC,OAAA;kBAAAwN,QAAA,eACExN,OAAA;oBAAIuP,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eAC/CxN,OAAA;sBAAKuN,SAAS,EAAC;oBAAuE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACHxL,KAAK,gBACPtC,OAAA;kBAAAwN,QAAA,eACExN,OAAA;oBAAIuP,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,yBACtC,EAAClL,KAAK;kBAAA;oBAAAqL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH5L,OAAO,CAACvB,MAAM,KAAK,CAAC,gBACtBX,OAAA;kBAAAwN,QAAA,eACExN,OAAA;oBAAIuP,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAEL5L,OAAO,CAACsN,GAAG,CAAEtG,MAAM;kBAAA,IAAAuG,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;kBAAA,oBACjBlQ,OAAA,CAACzB,MAAM,CAAC4R,EAAE;oBAERlC,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAE;oBACxBE,OAAO,EAAE;sBAAEF,OAAO,EAAE;oBAAE,CAAE;oBACxBX,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5BxN,OAAA;sBAAIuN,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,eACzCxN,OAAA;wBAAKuN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCxN,OAAA;0BAAKuN,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,eACtCxN,OAAA;4BAAKuN,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,EAC7F,EAAAiC,mBAAA,GAAAvG,MAAM,CAAC1E,WAAW,cAAAiL,mBAAA,uBAAlBA,mBAAA,CAAoBlP,MAAM,CAAC,CAAC,CAAC,KAAI;0BAAG;4BAAAoN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN9N,OAAA;0BAAKuN,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBxN,OAAA;4BAAKuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAEtE,MAAM,CAAC1E;0BAAW;4BAAAmJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7E9N,OAAA;4BAAKuN,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAEtE,MAAM,CAACvE;0BAAK;4BAAAgJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3D9N,OAAA;4BAAKuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC7C,EAAAkC,iBAAA,GAAAxG,MAAM,CAACiC,SAAS,cAAAuE,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBzE,aAAa,cAAA0E,qBAAA,uBAA/BA,qBAAA,CAAiCb,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,kBAAW,EAAC,EAAAc,kBAAA,GAAA1G,MAAM,CAACiC,SAAS,cAAAyE,kBAAA,uBAAhBA,kBAAA,CAAkB/M,WAAW,KAAI,GAAG,EAAC,eAC7G;0BAAA;4BAAA8K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACL9N,OAAA;sBAAIuN,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9DxN,OAAA;wBAAKuN,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAAqC,kBAAA,GAAA3G,MAAM,CAACiC,SAAS,cAAA0E,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkB5E,aAAa,cAAA6E,qBAAA,uBAA/BA,qBAAA,CAAiChB,cAAc,CAAC,CAAC,KAAI;sBAAG;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnH9N,OAAA;wBAAKuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAuC,kBAAA,GAAA7G,MAAM,CAACiC,SAAS,cAAA4E,kBAAA,uBAAhBA,kBAAA,CAAkBK,YAAY,KAAI,GAAG,EAAC,WAAS;sBAAA;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACL9N,OAAA;sBAAIuN,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9DxN,OAAA;wBAAKuN,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAAE,EAAAwC,kBAAA,GAAA9G,MAAM,CAACiC,SAAS,cAAA6E,kBAAA,uBAAhBA,kBAAA,CAAkB1E,cAAc,KAAI,GAAG,EAAC,GAAC;sBAAA;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnG9N,OAAA;wBAAKuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAyC,kBAAA,GAAA/G,MAAM,CAACiC,SAAS,cAAA8E,kBAAA,uBAAhBA,kBAAA,CAAkBpN,WAAW,KAAI,GAAG,EAAC,eAAa;sBAAA;wBAAA8K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACL9N,OAAA;sBAAIuN,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DxN,OAAA;wBAAKuN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCxN,OAAA;0BAAMuN,SAAS,EAAE,2EACfrE,MAAM,CAACuF,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GACtEvF,MAAM,CAACuF,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GACnE,+BAA+B,EAC9B;0BAAAjB,QAAA,EACAtE,MAAM,CAACuF;wBAAkB;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACP9N,OAAA;0BAAMuN,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAEpG,gBAAgB,EAAA8I,kBAAA,GAAChH,MAAM,CAACiC,SAAS,cAAA+E,kBAAA,uBAAhBA,kBAAA,CAAkB1I,UAAU;wBAAC;0BAAAmG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACL9N,OAAA;sBAAIuN,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DxN,OAAA;wBAAMuN,SAAS,EAAE,2EACfrE,MAAM,CAACpE,gBAAgB,KAAK,YAAY,GAAG,+BAA+B,GAC1EoE,MAAM,CAACpE,gBAAgB,KAAK,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,EAChG;wBAAA0I,QAAA,EACAtE,MAAM,CAACpE;sBAAgB;wBAAA6I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACL9N,OAAA;sBAAIuN,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACxExN,OAAA;wBAAKuN,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzCxN,OAAA;0BACEuN,SAAS,EAAC,+FAA+F;0BACzGQ,OAAO,EAAEA,CAAA,KAAMtC,iBAAiB,CAACvC,MAAM,CAAE;0BACzCmH,KAAK,EAAC,cAAc;0BAAA7C,QAAA,eAEpBxN,OAAA,CAACrB,GAAG;4BAAC4O,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACT9N,OAAA;0BACEuN,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAACxC,MAAM,CAAE;0BACtCmH,KAAK,EAAC,kBAAkB;0BAAA7C,QAAA,eAExBxN,OAAA,CAACf,IAAI;4BAACsO,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACT9N,OAAA;0BACEuN,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAM9E,aAAa,CAACC,MAAM,CAAE;0BACrCmH,KAAK,EAAC,aAAa;0BAAA7C,QAAA,eAEnBxN,OAAA,CAACpB,IAAI;4BAAC2O,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACT9N,OAAA;0BACEuN,SAAS,EAAC,kFAAkF;0BAC5FQ,OAAO,EAAEA,CAAA,KAAMlF,kBAAkB,CAACK,MAAM,CAACN,GAAG,CAAE;0BAC9CyH,KAAK,EAAC,eAAe;0BAAA7C,QAAA,eAErBxN,OAAA,CAACnB,MAAM;4BAAC0O,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAhFA5E,MAAM,CAACN,GAAG;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiFN,CAAC;gBAAA,CACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9N,OAAA,CAACxB,eAAe;UAAAgP,QAAA,EACbxK,gBAAgB,IAAII,wBAAwB,iBAC3CpD,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1FxN,OAAA,CAACzB,MAAM,CAACyP,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAGzFxN,OAAA;gBAAKuN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrExN,OAAA;kBAAKuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDxN,OAAA;oBAAKuN,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CxN,OAAA;sBAAKuN,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,EACtG,EAAAxM,qBAAA,GAAAoC,wBAAwB,CAACoB,WAAW,cAAAxD,qBAAA,uBAApCA,qBAAA,CAAsCT,MAAM,CAAC,CAAC,CAAC,KAAI;oBAAG;sBAAAoN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACN9N,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAIuN,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EACzCpK,wBAAwB,CAACoB;sBAAW;wBAAAmJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,eACL9N,OAAA;wBAAGuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjCpK,wBAAwB,CAACuB;sBAAK;wBAAAgJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9N,OAAA;oBACEuN,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM9K,mBAAmB,CAAC,KAAK,CAAE;oBAAAuK,QAAA,eAE1CxN,OAAA,CAACd,CAAC;sBAACqO,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9N,OAAA;gBAAKuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EACzD9J,gBAAgB,gBACf1D,OAAA;kBAAKuN,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,eACrDxN,OAAA;oBAAKuN,SAAS,EAAC;kBAAiE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,gBAEN9N,OAAA;kBAAKuN,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBAExBxN,OAAA;oBAAKuN,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnExN,OAAA;sBAAKuN,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,eACxExN,OAAA;wBAAKuN,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1CxN,OAAA;0BAAKuN,SAAS,EAAC,mEAAmE;0BAAAC,QAAA,eAChFxN,OAAA,CAAChB,KAAK;4BAACuO,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACN9N,OAAA;0BAAAwN,QAAA,gBACExN,OAAA;4BAAGuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAc;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACnE9N,OAAA;4BAAGuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC3C,CAAAhK,eAAe,aAAfA,eAAe,wBAAAvC,qBAAA,GAAfuC,eAAe,CAAEyH,aAAa,cAAAhK,qBAAA,uBAA9BA,qBAAA,CAAgC6N,cAAc,CAAC,CAAC,KAAI;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN9N,OAAA;sBAAKuN,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1ExN,OAAA;wBAAKuN,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1CxN,OAAA;0BAAKuN,SAAS,EAAC,oEAAoE;0BAAAC,QAAA,eACjFxN,OAAA,CAACV,KAAK;4BAACiO,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACN9N,OAAA;0BAAAwN,QAAA,gBACExN,OAAA;4BAAGuN,SAAS,EAAC,oCAAoC;4BAAAC,QAAA,EAAC;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAClE9N,OAAA;4BAAGuN,SAAS,EAAC,kCAAkC;4BAAAC,QAAA,GAC5ChN,IAAI,CAAC8P,KAAK,CAAC,CAAA9M,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4H,WAAW,KAAI,CAAC,CAAC,EAAC,GACjD;0BAAA;4BAAAuC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN9N,OAAA;sBAAKuN,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5ExN,OAAA;wBAAKuN,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1CxN,OAAA;0BAAKuN,SAAS,EAAC,qEAAqE;0BAAAC,QAAA,eAClFxN,OAAA,CAACX,SAAS;4BAACkO,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,eACN9N,OAAA;0BAAAwN,QAAA,gBACExN,OAAA;4BAAGuN,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAC;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACnE9N,OAAA;4BAAGuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAC7C,CAAAhK,eAAe,aAAfA,eAAe,wBAAAtC,sBAAA,GAAfsC,eAAe,CAAE6H,iBAAiB,cAAAnK,sBAAA,uBAAlCA,sBAAA,CAAoC4N,cAAc,CAAC,CAAC,KAAI;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3D,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN9N,OAAA;sBAAKuN,SAAS,EAAC,+DAA+D;sBAAAC,QAAA,eAC5ExN,OAAA;wBAAKuN,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1CxN,OAAA;0BAAKuN,SAAS,EAAC,qEAAqE;0BAAAC,QAAA,eAClFxN,OAAA,CAACP,QAAQ;4BAAC8N,SAAS,EAAC;0BAAoB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eACN9N,OAAA;0BAAAwN,QAAA,gBACExN,OAAA;4BAAGuN,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAC;0BAAe;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACtE9N,OAAA;4BAAGuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,GAC7C,CAAAhK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8H,cAAc,KAAI,GAAG,EAAC,GAC1C;0BAAA;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN9N,OAAA;oBAAKuN,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxCxN,OAAA;sBAAIuN,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACxExN,OAAA,CAACP,QAAQ;wBAAC8N,SAAS,EAAC;sBAA6B;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,kBAEtD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL9N,OAAA;sBAAKuN,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpDxN,OAAA;wBAAKuN,SAAS,EAAC,WAAW;wBAAAC,QAAA,gBACxBxN,OAAA;0BAAKuN,SAAS,EAAC,iEAAiE;0BAAAC,QAAA,gBAC9ExN,OAAA;4BAAMuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAc;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzE9N,OAAA;4BAAMuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC9C,CAAAhK,eAAe,aAAfA,eAAe,wBAAArC,sBAAA,GAAfqC,eAAe,CAAEyH,aAAa,cAAA9J,sBAAA,uBAA9BA,sBAAA,CAAgC2N,cAAc,CAAC,CAAC,KAAI;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN9N,OAAA;0BAAKuN,SAAS,EAAC,iEAAiE;0BAAAC,QAAA,gBAC9ExN,OAAA;4BAAMuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAgB;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3E9N,OAAA;4BAAMuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC9ChN,IAAI,CAAC8P,KAAK,CAAC,CAAA9M,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4H,WAAW,KAAI,CAAC,CAAC,EAAC,GACjD;0BAAA;4BAAAuC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN9N,OAAA;0BAAKuN,SAAS,EAAC,iEAAiE;0BAAAC,QAAA,gBAC9ExN,OAAA;4BAAMuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7E9N,OAAA;4BAAMuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC9C,CAAAhK,eAAe,aAAfA,eAAe,wBAAApC,sBAAA,GAAfoC,eAAe,CAAE6H,iBAAiB,cAAAjK,sBAAA,uBAAlCA,sBAAA,CAAoC0N,cAAc,CAAC,CAAC,KAAI;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9N,OAAA;wBAAKuN,SAAS,EAAC,WAAW;wBAAAC,QAAA,gBACxBxN,OAAA;0BAAKuN,SAAS,EAAC,iEAAiE;0BAAAC,QAAA,gBAC9ExN,OAAA;4BAAMuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAe;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC1E9N,OAAA;4BAAMuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC9C,CAAAhK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8H,cAAc,KAAI,GAAG,EAAC,GAC1C;0BAAA;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN9N,OAAA;0BAAKuN,SAAS,EAAC,iEAAiE;0BAAAC,QAAA,gBAC9ExN,OAAA;4BAAMuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACvE9N,OAAA;4BAAMuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC9C,CAAAhK,eAAe,aAAfA,eAAe,wBAAAnC,qBAAA,GAAfmC,eAAe,CAAEX,WAAW,cAAAxB,qBAAA,uBAA5BA,qBAAA,CAA8ByN,cAAc,CAAC,CAAC,KAAI;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN9N,OAAA;0BAAKuN,SAAS,EAAC,iEAAiE;0BAAAC,QAAA,gBAC9ExN,OAAA;4BAAMuN,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAW;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACtE9N,OAAA;4BAAMuN,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,EAC9ChK,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEgE,UAAU,GAAG,IAAID,IAAI,CAAC/D,eAAe,CAACgE,UAAU,CAAC,CAAC+I,kBAAkB,CAAC,CAAC,GAAG;0BAAO;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9F,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL,CAAAtK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+H,eAAe,KAAI/H,eAAe,CAAC+H,eAAe,CAAC5K,MAAM,GAAG,CAAC,iBAC7EX,OAAA;oBAAKuN,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxCxN,OAAA;sBAAIuN,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACxExN,OAAA,CAACR,OAAO;wBAAC+N,SAAS,EAAC;sBAA6B;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAErD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL9N,OAAA;sBAAKuN,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EACnDhK,eAAe,CAAC+H,eAAe,CAACiE,GAAG,CAAC,CAACgB,MAAM,EAAEC,KAAK,kBACjDzQ,OAAA;wBAAiBuN,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,eAClDxN,OAAA;0BAAKuN,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDxN,OAAA;4BAAMuN,SAAS,EAAC,8CAA8C;4BAAAC,QAAA,EAC3DgD,MAAM,CAACA,MAAM,IAAI;0BAAS;4BAAA7C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACP9N,OAAA;4BAAMuN,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACpCgD,MAAM,CAACtF,QAAQ,EAAC,WACnB;0BAAA;4BAAAyC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC,GARE2C,KAAK;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OASV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlB9N,OAAA,CAACxB,eAAe;UAAAgP,QAAA,EACbtK,aAAa,IAAII,qBAAqB,iBACrCtD,OAAA,CAACzB,MAAM,CAACyP,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1FxN,OAAA,CAACzB,MAAM,CAACyP,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAGzFxN,OAAA;gBAAKuN,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnExN,OAAA;kBAAKuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDxN,OAAA;oBAAKuN,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CxN,OAAA;sBAAKuN,SAAS,EAAC,mEAAmE;sBAAAC,QAAA,eAChFxN,OAAA,CAACf,IAAI;wBAACsO,SAAS,EAAC;sBAAoB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACN9N,OAAA;sBAAAwN,QAAA,gBACExN,OAAA;wBAAIuN,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAC;sBAE7C;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL9N,OAAA;wBAAGuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjClK,qBAAqB,CAACkB;sBAAW;wBAAAmJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9N,OAAA;oBACEuN,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM5K,gBAAgB,CAAC,KAAK,CAAE;oBAAAqK,QAAA,eAEvCxN,OAAA,CAACd,CAAC;sBAACqO,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9N,OAAA;gBAAKuN,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClBxN,OAAA;kBAAKuN,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBAExBxN,OAAA;oBAAKuN,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxCxN,OAAA;sBAAIuN,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAEzD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL9N,OAAA;sBAAKuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9CxN,OAAA;wBAAAwN,QAAA,EAAG;sBAA4D;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnE9N,OAAA;wBAAAwN,QAAA,EAAG;sBAAuE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC9E9N,OAAA;wBAAAwN,QAAA,EAAG;sBAAmD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1D9N,OAAA;wBAAAwN,QAAA,EAAG;sBAAoD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN9N,OAAA;oBAAKuN,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBxN,OAAA;sBAAKuN,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDxN,OAAA;wBAAIuN,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAEpD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL9N,OAAA;wBACE+N,OAAO,EAAEf,mBAAoB;wBAC7BO,SAAS,EAAC,4GAA4G;wBAAAC,QAAA,EAErH5J,UAAU,gBACT5D,OAAA,CAAAE,SAAA;0BAAAsN,QAAA,gBACExN,OAAA,CAACZ,KAAK;4BAACmO,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,WAEpC;wBAAA,eAAE,CAAC,gBAEH9N,OAAA,CAAAE,SAAA;0BAAAsN,QAAA,gBACExN,OAAA,CAACb,IAAI;4BAACoO,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,aAEnC;wBAAA,eAAE;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAEN9N,OAAA;sBAAKuN,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,eACzDxN,OAAA;wBAAKuN,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,eACrCxN,OAAA;0BAAAwN,QAAA,EAAO7B,uBAAuB,CAACrI,qBAAqB;wBAAC;0BAAAqK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN9N,OAAA;oBAAKuN,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxCxN,OAAA;sBAAIuN,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAEzD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL9N,OAAA;sBAAKuN,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxBxN,OAAA;wBAAKuN,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7CxN,OAAA;0BAAGuN,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACtE9N,OAAA;0BAAMuN,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAAC;wBAEtE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN9N,OAAA;wBAAKuN,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7CxN,OAAA;0BAAGuN,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAc;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACxE9N,OAAA;0BAAMuN,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAAC;wBAEtE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN9N,OAAA;oBAAKuN,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CxN,OAAA;sBAAIuN,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAE3D;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL9N,OAAA;sBAAGuN,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAC;oBAEvC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjN,EAAA,CArtCID,OAAO;AAAA8P,EAAA,GAAP9P,OAAO;AAutCb,eAAeA,OAAO;AAAC,IAAA8P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}