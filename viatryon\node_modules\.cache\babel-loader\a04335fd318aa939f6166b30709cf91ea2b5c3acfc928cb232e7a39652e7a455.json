{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  var _stats$avgSessionsPer, _selectedClient$compa, _selectedClient$analy, _selectedClient$analy2, _selectedClient$analy3, _selectedClient$analy4, _selectedClient$analy5, _selectedClient$analy6, _selectedClient$analy7;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showCodeModal, setShowCodeModal] = useState(false);\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    revenueGrowth: 0,\n    avgSessionsPerUser: 0\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n      const response = await fetch(`${apiUrl}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n      const data = await response.json();\n      setClients(data.clients || []);\n      setStats(data.stats || {\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        revenueGrowth: 0,\n        avgSessionsPerUser: 0\n      });\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        revenueGrowth: 0,\n        avgSessionsPerUser: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = date => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n  const handleAddClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteClient = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openEditModal = client => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '',\n      // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n  const openDetailsModal = client => {\n    setSelectedClient(client);\n    setShowDetailsModal(true);\n  };\n  const openCodeModal = client => {\n    setSelectedClient(client);\n    setShowCodeModal(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: openAddModal,\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [\"+\", stats.newClientsThisMonth, \" new\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [stats.activeRate.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics;\n                    return sum + (((_c$analytics = c.analytics) === null || _c$analytics === void 0 ? void 0 : _c$analytics.totalSessions) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.tryOnsGrowth >= 0 ? '+' : '', stats.tryOnsGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics2, _c$analytics2$uniqueU;\n                    return sum + (((_c$analytics2 = c.analytics) === null || _c$analytics2 === void 0 ? void 0 : (_c$analytics2$uniqueU = _c$analytics2.uniqueUsers) === null || _c$analytics2$uniqueU === void 0 ? void 0 : _c$analytics2$uniqueU.length) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: [\"Avg \", ((_stats$avgSessionsPer = stats.avgSessionsPerUser) === null || _stats$avgSessionsPer === void 0 ? void 0 : _stats$avgSessionsPer.toFixed(1)) || '0', \" sessions/user\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600\",\n              onClick: () => setShowModal(false),\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: editingClient ? 'Edit Client' : 'Add New Client'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: editingClient ? handleEditClient : handleAddClient,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Company Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"companyName\",\n                    value: clientForm.companyName,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Contact Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"contactName\",\n                    value: clientForm.contactName,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    name: \"email\",\n                    value: clientForm.email,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Website\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"url\",\n                    name: \"website\",\n                    value: clientForm.website,\n                    onChange: handleFormChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phone\",\n                    value: clientForm.phone,\n                    onChange: handleFormChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Industry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"industry\",\n                    value: clientForm.industry,\n                    onChange: handleFormChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Product Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"productType\",\n                    value: clientForm.productType,\n                    onChange: handleFormChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"watches\",\n                      children: \"Watches\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"bracelets\",\n                      children: \"Bracelets\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"both\",\n                      children: \"Both\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Subscription Plan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"subscriptionPlan\",\n                    value: clientForm.subscriptionPlan,\n                    onChange: handleFormChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"basic\",\n                      children: \"Basic\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"premium\",\n                      children: \"Premium\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"enterprise\",\n                      children: \"Enterprise\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), !editingClient && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"password\",\n                    value: clientForm.password,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: handleSuggestPassword,\n                    className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                    children: \"Suggest\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end space-x-3 pt-4 border-t border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowModal(false),\n                  className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: editingClient ? 'Update Client' : 'Add Client'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Conversion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this) : error ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-red-600\",\n                    children: [\"Error loading clients: \", error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this) : clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-gray-500\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 21\n                }, this) : clients.map(client => {\n                  var _client$companyName, _client$analytics, _client$analytics$tot, _client$analytics2, _client$analytics3, _client$analytics3$to, _client$analytics4, _client$analytics5, _client$analytics6, _client$analytics6$re, _client$analytics7;\n                  return /*#__PURE__*/_jsxDEV(motion.tr, {\n                    initial: {\n                      opacity: 0\n                    },\n                    animate: {\n                      opacity: 1\n                    },\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 h-10 w-10\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                            children: ((_client$companyName = client.companyName) === null || _client$companyName === void 0 ? void 0 : _client$companyName.charAt(0)) || 'C'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 631,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 630,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"ml-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: client.companyName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 636,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: client.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 637,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500 lg:hidden\",\n                            children: [((_client$analytics = client.analytics) === null || _client$analytics === void 0 ? void 0 : (_client$analytics$tot = _client$analytics.totalSessions) === null || _client$analytics$tot === void 0 ? void 0 : _client$analytics$tot.toLocaleString()) || '0', \" try-ons \\u2022 \", ((_client$analytics2 = client.analytics) === null || _client$analytics2 === void 0 ? void 0 : _client$analytics2.conversionRate) || '0', \"% conversion\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 638,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 635,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 629,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: ((_client$analytics3 = client.analytics) === null || _client$analytics3 === void 0 ? void 0 : (_client$analytics3$to = _client$analytics3.totalSessions) === null || _client$analytics3$to === void 0 ? void 0 : _client$analytics3$to.toLocaleString()) || '0'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics4 = client.analytics) === null || _client$analytics4 === void 0 ? void 0 : _client$analytics4.productCount) || '0', \" products\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [((_client$analytics5 = client.analytics) === null || _client$analytics5 === void 0 ? void 0 : _client$analytics5.conversionRate) || '0', \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 649,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\"$\", ((_client$analytics6 = client.analytics) === null || _client$analytics6 === void 0 ? void 0 : (_client$analytics6$re = _client$analytics6.revenue) === null || _client$analytics6$re === void 0 ? void 0 : _client$analytics6$re.toLocaleString()) || '0', \" revenue\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                          children: client.subscriptionStatus\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 654,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatLastActive((_client$analytics7 = client.analytics) === null || _client$analytics7 === void 0 ? void 0 : _client$analytics7.lastActive)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 661,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' : client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n                        children: client.subscriptionPlan\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-1\",\n                          title: \"View Details\",\n                          onClick: () => openDetailsModal(client),\n                          children: /*#__PURE__*/_jsxDEV(Eye, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 679,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 674,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-blue-600 hover:text-blue-800 p-1\",\n                          title: \"Integration Code\",\n                          onClick: () => openCodeModal(client),\n                          children: /*#__PURE__*/_jsxDEV(Code, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 686,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 681,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-gray-600 hover:text-gray-800 p-1\",\n                          onClick: () => openEditModal(client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 693,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 688,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-red-600 hover:text-red-800 p-1\",\n                          onClick: () => handleDeleteClient(client._id),\n                          title: \"Delete Client\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 700,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 695,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 672,\n                      columnNumber: 25\n                    }, this)]\n                  }, client._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this), showDetailsModal && selectedClient && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600\",\n          onClick: () => setShowDetailsModal(false),\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-16 w-16 rounded-full bg-[#2D8C88] flex items-center justify-center text-white text-2xl\",\n            children: ((_selectedClient$compa = selectedClient.companyName) === null || _selectedClient$compa === void 0 ? void 0 : _selectedClient$compa.charAt(0)) || 'C'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: selectedClient.companyName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: selectedClient.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Company Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Contact Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedClient.contactName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedClient.website || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedClient.phone || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Industry\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedClient.industry || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: ((_selectedClient$analy = selectedClient.analytics) === null || _selectedClient$analy === void 0 ? void 0 : (_selectedClient$analy2 = _selectedClient$analy.totalSessions) === null || _selectedClient$analy2 === void 0 ? void 0 : _selectedClient$analy2.toLocaleString()) || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Unique Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: ((_selectedClient$analy3 = selectedClient.analytics) === null || _selectedClient$analy3 === void 0 ? void 0 : (_selectedClient$analy4 = _selectedClient$analy3.uniqueUsers) === null || _selectedClient$analy4 === void 0 ? void 0 : (_selectedClient$analy5 = _selectedClient$analy4.length) === null || _selectedClient$analy5 === void 0 ? void 0 : _selectedClient$analy5.toLocaleString()) || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Conversion Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: [((_selectedClient$analy6 = selectedClient.analytics) === null || _selectedClient$analy6 === void 0 ? void 0 : _selectedClient$analy6.conversionRate) || '0', \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Last Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: formatLastActive((_selectedClient$analy7 = selectedClient.analytics) === null || _selectedClient$analy7 === void 0 ? void 0 : _selectedClient$analy7.lastActive)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-6 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Subscription Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-500\",\n                children: \"Plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 capitalize\",\n                children: selectedClient.subscriptionPlan\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-500\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${selectedClient.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : selectedClient.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                children: selectedClient.subscriptionStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 9\n    }, this), showCodeModal && selectedClient && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600\",\n          onClick: () => setShowCodeModal(false),\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: [\"Integration Code for \", selectedClient.companyName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"HTML Integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                className: \"text-sm text-gray-800 overflow-x-auto\",\n                children: `<script src=\"https://tryon.viatryon.com/sdk.js\" data-client-id=\"${selectedClient._id}\"></script>`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"React Component\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                className: \"text-sm text-gray-800 overflow-x-auto\",\n                children: `import { VirtualTryOn } from '@viatryon/react';\n\nfunction App() {\n  return (\n    <VirtualTryOn\n      clientId=\"${selectedClient._id}\"\n      productId=\"your-product-id\"\n    />\n  );\n}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"API Key\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-800 break-all\",\n                children: selectedClient.apiKey || 'No API key generated'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"6b4gPtSvztI8rJKDTU5Dyt+Y4p4=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "jsxDEV", "_jsxDEV", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "_stats$avgSessionsPer", "_selectedClient$compa", "_selectedClient$analy", "_selectedClient$analy2", "_selectedClient$analy3", "_selectedClient$analy4", "_selectedClient$analy5", "_selectedClient$analy6", "_selectedClient$analy7", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "showDetailsModal", "setShowDetailsModal", "showCodeModal", "setShowCodeModal", "selectedClient", "setSelectedClient", "editingClient", "setEditingClient", "clients", "setClients", "loading", "setLoading", "error", "setError", "stats", "setStats", "newClientsThisMonth", "activeRate", "tryOnsGrowth", "revenueGrowth", "avgSessionsPerUser", "clientForm", "setClientForm", "companyName", "contactName", "website", "email", "phone", "industry", "productType", "subscriptionPlan", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "fetchClients", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "params", "URLSearchParams", "append", "response", "fetch", "headers", "ok", "errorData", "json", "message", "data", "err", "console", "formatLastActive", "date", "now", "Date", "lastActive", "diffInHours", "diffInDays", "diffInWeeks", "handleFormChange", "e", "name", "value", "target", "prev", "handleSuggestPassword", "resetForm", "handleAddClient", "preventDefault", "method", "body", "JSON", "stringify", "handleEditClient", "_id", "handleDeleteClient", "clientId", "window", "confirm", "openEditModal", "client", "openAddModal", "openDetailsModal", "openCodeModal", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "filter", "c", "subscriptionStatus", "toFixed", "reduce", "sum", "_c$analytics", "analytics", "totalSessions", "toLocaleString", "_c$analytics2", "_c$analytics2$uniqueU", "uniqueUsers", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "onChange", "required", "placeholder", "colSpan", "map", "_client$companyName", "_client$analytics", "_client$analytics$tot", "_client$analytics2", "_client$analytics3", "_client$analytics3$to", "_client$analytics4", "_client$analytics5", "_client$analytics6", "_client$analytics6$re", "_client$analytics7", "tr", "conversionRate", "productCount", "revenue", "title", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showCodeModal, setShowCodeModal] = useState(false);\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    revenueGrowth: 0,\n    avgSessionsPerUser: 0\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      const response = await fetch(`${apiUrl}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n\n      const data = await response.json();\n      setClients(data.clients || []);\n      setStats(data.stats || {\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        revenueGrowth: 0,\n        avgSessionsPerUser: 0\n      });\n\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        revenueGrowth: 0,\n        avgSessionsPerUser: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = (date) => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n\n  const handleAddClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEditClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClient = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n\n      await fetchClients();\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const openEditModal = (client) => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '', // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  const openDetailsModal = (client) => {\n    setSelectedClient(client);\n    setShowDetailsModal(true);\n  };\n\n  const openCodeModal = (client) => {\n    setSelectedClient(client);\n    setShowCodeModal(true);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={openAddModal}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+{stats.newClientsThisMonth} new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">{stats.activeRate.toFixed(1)}%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.uniqueUsers?.length || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-purple-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-gray-600\">\n                  Avg {stats.avgSessionsPerUser?.toFixed(1) || '0'} sessions/user\n                </span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add/Edit Client Modal */}\n          {showModal && (\n            <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n              <div className=\"bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative\">\n                <button\n                  className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600\"\n                  onClick={() => setShowModal(false)}\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">{editingClient ? 'Edit Client' : 'Add New Client'}</h2>\n                <form onSubmit={editingClient ? handleEditClient : handleAddClient} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Company Name</label>\n                      <input\n                        type=\"text\"\n                        name=\"companyName\"\n                        value={clientForm.companyName}\n                        onChange={handleFormChange}\n                        required\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Contact Name</label>\n                      <input\n                        type=\"text\"\n                        name=\"contactName\"\n                        value={clientForm.contactName}\n                        onChange={handleFormChange}\n                        required\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={clientForm.email}\n                        onChange={handleFormChange}\n                        required\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Website</label>\n                      <input\n                        type=\"url\"\n                        name=\"website\"\n                        value={clientForm.website}\n                        onChange={handleFormChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone</label>\n                      <input\n                        type=\"tel\"\n                        name=\"phone\"\n                        value={clientForm.phone}\n                        onChange={handleFormChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Industry</label>\n                      <input\n                        type=\"text\"\n                        name=\"industry\"\n                        value={clientForm.industry}\n                        onChange={handleFormChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Product Type</label>\n                      <select\n                        name=\"productType\"\n                        value={clientForm.productType}\n                        onChange={handleFormChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      >\n                        <option value=\"watches\">Watches</option>\n                        <option value=\"bracelets\">Bracelets</option>\n                        <option value=\"both\">Both</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Subscription Plan</label>\n                      <select\n                        name=\"subscriptionPlan\"\n                        value={clientForm.subscriptionPlan}\n                        onChange={handleFormChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      >\n                        <option value=\"basic\">Basic</option>\n                        <option value=\"premium\">Premium</option>\n                        <option value=\"enterprise\">Enterprise</option>\n                      </select>\n                    </div>\n                  </div>\n                  {!editingClient && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Password</label>\n                      <div className=\"flex gap-2\">\n                        <input\n                          type=\"text\"\n                          name=\"password\"\n                          value={clientForm.password}\n                          onChange={handleFormChange}\n                          required\n                          className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={handleSuggestPassword}\n                          className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                        >\n                          Suggest\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                  <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowModal(false)}\n                      className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                    >\n                      Cancel\n                    </button>\n                    <button\n                      type=\"submit\"\n                      className=\"px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                    >\n                      {editingClient ? 'Update Client' : 'Add Client'}\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </div>\n          )}\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Conversion</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {loading ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center\">\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"></div>\n                      </td>\n                    </tr>\n                  ) : error ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-red-600\">\n                        Error loading clients: {error}\n                      </td>\n                    </tr>\n                  ) : clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-gray-500\">\n                        No clients found\n                      </td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <motion.tr\n                        key={client._id}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"hover:bg-gray-50\"\n                      >\n                        <td className=\"px-4 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                                {client.companyName?.charAt(0) || 'C'}\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{client.companyName}</div>\n                              <div className=\"text-sm text-gray-500\">{client.email}</div>\n                              <div className=\"text-sm text-gray-500 lg:hidden\">\n                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.conversionRate || '0'}% conversion\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.productCount || '0'} products</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.conversionRate || '0'}%</div>\n                          <div className=\"text-sm text-gray-500\">${client.analytics?.revenue?.toLocaleString() || '0'} revenue</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                              'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {client.subscriptionStatus}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{formatLastActive(client.analytics?.lastActive)}</span>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {client.subscriptionPlan}\n                          </span>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex justify-end space-x-2\">\n                            <button\n                              className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-1\"\n                              title=\"View Details\"\n                              onClick={() => openDetailsModal(client)}\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-blue-600 hover:text-blue-800 p-1\"\n                              title=\"Integration Code\"\n                              onClick={() => openCodeModal(client)}\n                            >\n                              <Code className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-gray-600 hover:text-gray-800 p-1\"\n                              onClick={() => openEditModal(client)}\n                              title=\"Edit Client\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-red-600 hover:text-red-800 p-1\"\n                              onClick={() => handleDeleteClient(client._id)}\n                              title=\"Delete Client\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Client Details Modal */}\n      {showDetailsModal && selectedClient && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n          <div className=\"bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative\">\n            <button\n              className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600\"\n              onClick={() => setShowDetailsModal(false)}\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n            <div className=\"flex items-center space-x-4 mb-6\">\n              <div className=\"h-16 w-16 rounded-full bg-[#2D8C88] flex items-center justify-center text-white text-2xl\">\n                {selectedClient.companyName?.charAt(0) || 'C'}\n              </div>\n              <div>\n                <h2 className=\"text-2xl font-bold text-gray-900\">{selectedClient.companyName}</h2>\n                <p className=\"text-gray-600\">{selectedClient.email}</p>\n              </div>\n            </div>\n            <div className=\"grid grid-cols-2 gap-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Company Information</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Contact Name</p>\n                    <p className=\"text-gray-900\">{selectedClient.contactName}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Website</p>\n                    <p className=\"text-gray-900\">{selectedClient.website || 'N/A'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Phone</p>\n                    <p className=\"text-gray-900\">{selectedClient.phone || 'N/A'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Industry</p>\n                    <p className=\"text-gray-900\">{selectedClient.industry || 'N/A'}</p>\n                  </div>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Analytics</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Total Try-Ons</p>\n                    <p className=\"text-gray-900\">{selectedClient.analytics?.totalSessions?.toLocaleString() || '0'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Unique Users</p>\n                    <p className=\"text-gray-900\">{selectedClient.analytics?.uniqueUsers?.length?.toLocaleString() || '0'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Conversion Rate</p>\n                    <p className=\"text-gray-900\">{selectedClient.analytics?.conversionRate || '0'}%</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-500\">Last Active</p>\n                    <p className=\"text-gray-900\">{formatLastActive(selectedClient.analytics?.lastActive)}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Subscription Details</h3>\n              <div className=\"grid grid-cols-2 gap-6\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-500\">Plan</p>\n                  <p className=\"text-gray-900 capitalize\">{selectedClient.subscriptionPlan}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-500\">Status</p>\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                    selectedClient.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                    selectedClient.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                    'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    {selectedClient.subscriptionStatus}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Integration Code Modal */}\n      {showCodeModal && selectedClient && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n          <div className=\"bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 relative\">\n            <button\n              className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600\"\n              onClick={() => setShowCodeModal(false)}\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Integration Code for {selectedClient.companyName}</h2>\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">HTML Integration</h3>\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <pre className=\"text-sm text-gray-800 overflow-x-auto\">\n                    {`<script src=\"https://tryon.viatryon.com/sdk.js\" data-client-id=\"${selectedClient._id}\"></script>`}\n                  </pre>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">React Component</h3>\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <pre className=\"text-sm text-gray-800 overflow-x-auto\">\n                    {`import { VirtualTryOn } from '@viatryon/react';\n\nfunction App() {\n  return (\n    <VirtualTryOn\n      clientId=\"${selectedClient._id}\"\n      productId=\"your-product-id\"\n    />\n  );\n}`}\n                  </pre>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">API Key</h3>\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <p className=\"text-sm text-gray-800 break-all\">{selectedClient.apiKey || 'No API key generated'}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Clients; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC;IACjC8D,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC;IAC3CqE,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTtD,QAAQ,EAAE,EAAE;IACZuD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,SAAS;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxC,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0C,UAAU,GAAGxC,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACArC,SAAS,CAAC,MAAM;IACd8E,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACvC,WAAW,EAAEE,cAAc,CAAC,CAAC;EAEjC,MAAMqC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMO,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIpD,WAAW,EAAEmD,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAErD,WAAW,CAAC;MACrD,IAAIE,cAAc,KAAK,KAAK,EAAEiD,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEnD,cAAc,CAAC;MAErE,MAAMoD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,MAAM,gBAAgBG,MAAM,EAAE,EAAE;QAC9DK,OAAO,EAAE;UACP,eAAe,EAAE,UAAUhB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACc,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIhB,KAAK,CAACe,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClC5C,UAAU,CAAC8C,IAAI,CAAC/C,OAAO,IAAI,EAAE,CAAC;MAC9BO,QAAQ,CAACwC,IAAI,CAACzC,KAAK,IAAI;QACrBE,mBAAmB,EAAE,CAAC;QACtBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,kBAAkB,EAAE;MACtB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOoC,GAAG,EAAE;MACZC,OAAO,CAAC7C,KAAK,CAAC,yBAAyB,EAAE4C,GAAG,CAAC;MAC7C3C,QAAQ,CAAC2C,GAAG,CAACF,OAAO,CAAC;MACrB7C,UAAU,CAAC,EAAE,CAAC;MACdM,QAAQ,CAAC;QACPC,mBAAmB,EAAE,CAAC;QACtBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,kBAAkB,EAAE;MACtB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IACjC,MAAMI,WAAW,GAAGxF,IAAI,CAACC,KAAK,CAAC,CAACoF,GAAG,GAAGE,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErE,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMC,UAAU,GAAGzF,IAAI,CAACC,KAAK,CAACuF,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,WAAW;IACnD,MAAMC,WAAW,GAAG1F,IAAI,CAACC,KAAK,CAACwF,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,YAAY;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChD,aAAa,CAACiD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClClD,aAAa,CAACiD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnG,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAMuG,SAAS,GAAGA,CAAA,KAAM;IACtBnD,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTtD,QAAQ,EAAE,EAAE;MACZuD,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,SAAS;MACtBC,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACFvB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmE,eAAe,GAAG,MAAOP,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,MAAM,cAAc,EAAE;QACpDkC,MAAM,EAAE,MAAM;QACd1B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUhB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACD2C,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC1D,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAAC2B,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIhB,KAAK,CAACe,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMrB,YAAY,CAAC,CAAC;MACpBlC,YAAY,CAAC,KAAK,CAAC;MACnB0E,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAE4C,GAAG,CAAC;MAC5C3C,QAAQ,CAAC2C,GAAG,CAACF,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqE,gBAAgB,GAAG,MAAOb,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,MAAM,gBAAgBpC,aAAa,CAAC2E,GAAG,EAAE,EAAE;QACzEL,MAAM,EAAE,KAAK;QACb1B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUhB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACD2C,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC1D,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAAC2B,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIhB,KAAK,CAACe,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMrB,YAAY,CAAC,CAAC;MACpBlC,YAAY,CAAC,KAAK,CAAC;MACnB0E,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAE4C,GAAG,CAAC;MAC5C3C,QAAQ,CAAC2C,GAAG,CAACF,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuE,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MACnE;IACF;IAEA,IAAI;MACF1E,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,MAAM,gBAAgByC,QAAQ,EAAE,EAAE;QAChEP,MAAM,EAAE,QAAQ;QAChB1B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUhB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACc,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIhB,KAAK,CAACe,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMrB,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZC,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAE4C,GAAG,CAAC;MAC5C3C,QAAQ,CAAC2C,GAAG,CAACF,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2E,aAAa,GAAIC,MAAM,IAAK;IAChChF,gBAAgB,CAACgF,MAAM,CAAC;IACxBjE,aAAa,CAAC;MACZC,WAAW,EAAEgE,MAAM,CAAChE,WAAW,IAAI,EAAE;MACrCC,WAAW,EAAE+D,MAAM,CAAC/D,WAAW,IAAI,EAAE;MACrCC,OAAO,EAAE8D,MAAM,CAAC9D,OAAO,IAAI,EAAE;MAC7BC,KAAK,EAAE6D,MAAM,CAAC7D,KAAK,IAAI,EAAE;MACzBtD,QAAQ,EAAE,EAAE;MAAE;MACduD,KAAK,EAAE4D,MAAM,CAAC5D,KAAK,IAAI,EAAE;MACzBC,QAAQ,EAAE2D,MAAM,CAAC3D,QAAQ,IAAI,EAAE;MAC/BC,WAAW,EAAE0D,MAAM,CAAC1D,WAAW,IAAI,SAAS;MAC5CC,gBAAgB,EAAEyD,MAAM,CAACzD,gBAAgB,IAAI;IAC/C,CAAC,CAAC;IACF/B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyF,YAAY,GAAGA,CAAA,KAAM;IACzBf,SAAS,CAAC,CAAC;IACX1E,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM0F,gBAAgB,GAAIF,MAAM,IAAK;IACnClF,iBAAiB,CAACkF,MAAM,CAAC;IACzBtF,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMyF,aAAa,GAAIH,MAAM,IAAK;IAChClF,iBAAiB,CAACkF,MAAM,CAAC;IACzBpF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,oBACElC,OAAA;IAAK0H,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC3H,OAAA,CAACb,YAAY;MAACyI,MAAM,EAAEvG,aAAc;MAACwG,OAAO,EAAEA,CAAA,KAAMvG,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjIjI,OAAA,CAACZ,WAAW;MAAC0E,aAAa,EAAEA,aAAc;MAACvC,SAAS,EAAEA;IAAU;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnEjI,OAAA;MAAM0H,SAAS,EAAE,GAAG3D,UAAU,oCAAqC;MAAA4D,QAAA,eACjE3H,OAAA;QAAK0H,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzB3H,OAAA;UAAK0H,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtF3H,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEjI,OAAA;cAAG0H,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACNjI,OAAA;YACE0H,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAEX,YAAa;YAAAI,QAAA,gBAEtB3H,OAAA,CAACT,IAAI;cAACmI,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNjI,OAAA;UAAK0H,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjF3H,OAAA,CAACX,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C3H,OAAA;cAAK0H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEjI,OAAA;kBAAG0H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAElF,OAAO,GAAG,KAAK,GAAGF,OAAO,CAAC9B;gBAAM;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACNjI,OAAA;gBAAK0H,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrF3H,OAAA,CAACH,KAAK;kBAAC6H,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjI,OAAA;cAAK0H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3H,OAAA;gBAAM0H,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,GAAC,EAAC9E,KAAK,CAACE,mBAAmB,EAAC,MAAI;cAAA;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5FjI,OAAA;gBAAM0H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbjI,OAAA,CAACX,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C3H,OAAA;cAAK0H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnEjI,OAAA;kBAAG0H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAElF,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACmG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB,KAAK,QAAQ,CAAC,CAACnI;gBAAM;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,eACNjI,OAAA;gBAAK0H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF3H,OAAA,CAACJ,UAAU;kBAAC8H,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjI,OAAA;cAAK0H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3H,OAAA;gBAAM0H,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAE9E,KAAK,CAACG,UAAU,CAAC6F,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1FjI,OAAA;gBAAM0H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbjI,OAAA,CAACX,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C3H,OAAA;cAAK0H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEjI,OAAA;kBAAG0H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAElF,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACuG,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC;oBAAA,IAAAK,YAAA;oBAAA,OAAKD,GAAG,IAAI,EAAAC,YAAA,GAAAL,CAAC,CAACM,SAAS,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,aAAa,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAACC,cAAc,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrK,CAAC,eACNjI,OAAA;gBAAK0H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF3H,OAAA,CAACR,GAAG;kBAACkI,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjI,OAAA;cAAK0H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3H,OAAA;gBAAM0H,SAAS,EAAE,uBAAuB7E,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAA0E,QAAA,GACnG9E,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,YAAY,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAC,GACrE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjI,OAAA;gBAAM0H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbjI,OAAA,CAACX,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C3H,OAAA;cAAK0H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjEjI,OAAA;kBAAG0H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAElF,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACuG,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC;oBAAA,IAAAS,aAAA,EAAAC,qBAAA;oBAAA,OAAKN,GAAG,IAAI,EAAAK,aAAA,GAAAT,CAAC,CAACM,SAAS,cAAAG,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaE,WAAW,cAAAD,qBAAA,uBAAxBA,qBAAA,CAA0B5I,MAAM,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAAC0I,cAAc,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3K,CAAC,eACNjI,OAAA;gBAAK0H,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF3H,OAAA,CAACH,KAAK;kBAAC6H,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjI,OAAA;cAAK0H,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB3H,OAAA;gBAAM0H,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,MAC9C,EAAC,EAAA/G,qBAAA,GAAAiC,KAAK,CAACM,kBAAkB,cAAAvC,qBAAA,uBAAxBA,qBAAA,CAA0BiI,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,gBACnD;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGLpG,SAAS,iBACR7B,OAAA;UAAK0H,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzF3H,OAAA;YAAK0H,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1E3H,OAAA;cACE0H,SAAS,EAAC,0DAA0D;cACpEQ,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,KAAK,CAAE;cAAA6F,QAAA,eAEnC3H,OAAA;gBAAKuJ,KAAK,EAAC,4BAA4B;gBAAC7B,SAAS,EAAC,SAAS;gBAAC8B,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAA/B,QAAA,eAC/G3H,OAAA;kBAAM2J,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTjI,OAAA;cAAI0H,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEtF,aAAa,GAAG,aAAa,GAAG;YAAgB;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7GjI,OAAA;cAAM+J,QAAQ,EAAE1H,aAAa,GAAG0E,gBAAgB,GAAGN,eAAgB;cAACiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvF3H,OAAA;gBAAK0H,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD3H,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFjI,OAAA;oBACEgK,IAAI,EAAC,MAAM;oBACX7D,IAAI,EAAC,aAAa;oBAClBC,KAAK,EAAEhD,UAAU,CAACE,WAAY;oBAC9B2G,QAAQ,EAAEhE,gBAAiB;oBAC3BiE,QAAQ;oBACRxC,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFjI,OAAA;oBACEgK,IAAI,EAAC,MAAM;oBACX7D,IAAI,EAAC,aAAa;oBAClBC,KAAK,EAAEhD,UAAU,CAACG,WAAY;oBAC9B0G,QAAQ,EAAEhE,gBAAiB;oBAC3BiE,QAAQ;oBACRxC,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EjI,OAAA;oBACEgK,IAAI,EAAC,OAAO;oBACZ7D,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAEhD,UAAU,CAACK,KAAM;oBACxBwG,QAAQ,EAAEhE,gBAAiB;oBAC3BiE,QAAQ;oBACRxC,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EjI,OAAA;oBACEgK,IAAI,EAAC,KAAK;oBACV7D,IAAI,EAAC,SAAS;oBACdC,KAAK,EAAEhD,UAAU,CAACI,OAAQ;oBAC1ByG,QAAQ,EAAEhE,gBAAiB;oBAC3ByB,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EjI,OAAA;oBACEgK,IAAI,EAAC,KAAK;oBACV7D,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAEhD,UAAU,CAACM,KAAM;oBACxBuG,QAAQ,EAAEhE,gBAAiB;oBAC3ByB,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChFjI,OAAA;oBACEgK,IAAI,EAAC,MAAM;oBACX7D,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAEhD,UAAU,CAACO,QAAS;oBAC3BsG,QAAQ,EAAEhE,gBAAiB;oBAC3ByB,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFjI,OAAA;oBACEmG,IAAI,EAAC,aAAa;oBAClBC,KAAK,EAAEhD,UAAU,CAACQ,WAAY;oBAC9BqG,QAAQ,EAAEhE,gBAAiB;oBAC3ByB,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5I3H,OAAA;sBAAQoG,KAAK,EAAC,SAAS;sBAAAuB,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCjI,OAAA;sBAAQoG,KAAK,EAAC,WAAW;sBAAAuB,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CjI,OAAA;sBAAQoG,KAAK,EAAC,MAAM;sBAAAuB,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNjI,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAO0H,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzFjI,OAAA;oBACEmG,IAAI,EAAC,kBAAkB;oBACvBC,KAAK,EAAEhD,UAAU,CAACS,gBAAiB;oBACnCoG,QAAQ,EAAEhE,gBAAiB;oBAC3ByB,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5I3H,OAAA;sBAAQoG,KAAK,EAAC,OAAO;sBAAAuB,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCjI,OAAA;sBAAQoG,KAAK,EAAC,SAAS;sBAAAuB,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCjI,OAAA;sBAAQoG,KAAK,EAAC,YAAY;sBAAAuB,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL,CAAC5F,aAAa,iBACbrC,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAO0H,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChFjI,OAAA;kBAAK0H,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3H,OAAA;oBACEgK,IAAI,EAAC,MAAM;oBACX7D,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAEhD,UAAU,CAACjD,QAAS;oBAC3B8J,QAAQ,EAAEhE,gBAAiB;oBAC3BiE,QAAQ;oBACRxC,SAAS,EAAC;kBAAkI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC,eACFjI,OAAA;oBACEgK,IAAI,EAAC,QAAQ;oBACb9B,OAAO,EAAE3B,qBAAsB;oBAC/BmB,SAAS,EAAC,2IAA2I;oBAAAC,QAAA,EACtJ;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eACDjI,OAAA;gBAAK0H,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,gBACvE3H,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACb9B,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,KAAK,CAAE;kBACnC4F,SAAS,EAAC,qJAAqJ;kBAAAC,QAAA,EAChK;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjI,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbtC,SAAS,EAAC,0IAA0I;kBAAAC,QAAA,EAEnJtF,aAAa,GAAG,eAAe,GAAG;gBAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDjI,OAAA;UAAK0H,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD3H,OAAA;YAAK0H,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C3H,OAAA;cAAK0H,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrB3H,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/B/D,KAAK,EAAE3E,WAAY;gBACnBwI,QAAQ,EAAG/D,CAAC,IAAKxE,cAAc,CAACwE,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAChDsB,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjI,OAAA;cAAK0H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B3H,OAAA;gBACEoG,KAAK,EAAEzE,cAAe;gBACtBsI,QAAQ,EAAG/D,CAAC,IAAKtE,iBAAiB,CAACsE,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACnDsB,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5I3H,OAAA;kBAAQoG,KAAK,EAAC,KAAK;kBAAAuB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCjI,OAAA;kBAAQoG,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCjI,OAAA;kBAAQoG,KAAK,EAAC,SAAS;kBAAAuB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjI,OAAA;UAAK0H,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5D3H,OAAA;YAAK0H,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B3H,OAAA;cAAO0H,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpD3H,OAAA;gBAAO0H,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3B3H,OAAA;kBAAA2H,QAAA,gBACE3H,OAAA;oBAAI0H,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GjI,OAAA;oBAAI0H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChIjI,OAAA;oBAAI0H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnIjI,OAAA;oBAAI0H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/HjI,OAAA;oBAAI0H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpIjI,OAAA;oBAAI0H,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRjI,OAAA;gBAAO0H,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDlF,OAAO,gBACNzC,OAAA;kBAAA2H,QAAA,eACE3H,OAAA;oBAAIoK,OAAO,EAAC,GAAG;oBAAC1C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eAC/C3H,OAAA;sBAAK0H,SAAS,EAAC;oBAAuE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACHtF,KAAK,gBACP3C,OAAA;kBAAA2H,QAAA,eACE3H,OAAA;oBAAIoK,OAAO,EAAC,GAAG;oBAAC1C,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,yBACtC,EAAChF,KAAK;kBAAA;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH1F,OAAO,CAAC9B,MAAM,KAAK,CAAC,gBACtBT,OAAA;kBAAA2H,QAAA,eACE3H,OAAA;oBAAIoK,OAAO,EAAC,GAAG;oBAAC1C,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAEL1F,OAAO,CAAC8H,GAAG,CAAE/C,MAAM;kBAAA,IAAAgD,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA;kBAAA,oBACjBhL,OAAA,CAACX,MAAM,CAAC4L,EAAE;oBAER7C,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAE;oBACxBE,OAAO,EAAE;sBAAEF,OAAO,EAAE;oBAAE,CAAE;oBACxBX,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5B3H,OAAA;sBAAI0H,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,eACzC3H,OAAA;wBAAK0H,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChC3H,OAAA;0BAAK0H,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,eACtC3H,OAAA;4BAAK0H,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,EAC7F,EAAA2C,mBAAA,GAAAhD,MAAM,CAAChE,WAAW,cAAAgH,mBAAA,uBAAlBA,mBAAA,CAAoBjK,MAAM,CAAC,CAAC,CAAC,KAAI;0BAAG;4BAAAyH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNjI,OAAA;0BAAK0H,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnB3H,OAAA;4BAAK0H,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAEL,MAAM,CAAChE;0BAAW;4BAAAwE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7EjI,OAAA;4BAAK0H,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAEL,MAAM,CAAC7D;0BAAK;4BAAAqE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3DjI,OAAA;4BAAK0H,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC7C,EAAA4C,iBAAA,GAAAjD,MAAM,CAAC2B,SAAS,cAAAsB,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBrB,aAAa,cAAAsB,qBAAA,uBAA/BA,qBAAA,CAAiCrB,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,kBAAW,EAAC,EAAAsB,kBAAA,GAAAnD,MAAM,CAAC2B,SAAS,cAAAwB,kBAAA,uBAAhBA,kBAAA,CAAkBS,cAAc,KAAI,GAAG,EAAC,cAChH;0BAAA;4BAAApD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLjI,OAAA;sBAAI0H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9D3H,OAAA;wBAAK0H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAA+C,kBAAA,GAAApD,MAAM,CAAC2B,SAAS,cAAAyB,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBxB,aAAa,cAAAyB,qBAAA,uBAA/BA,qBAAA,CAAiCxB,cAAc,CAAC,CAAC,KAAI;sBAAG;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnHjI,OAAA;wBAAK0H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAiD,kBAAA,GAAAtD,MAAM,CAAC2B,SAAS,cAAA2B,kBAAA,uBAAhBA,kBAAA,CAAkBO,YAAY,KAAI,GAAG,EAAC,WAAS;sBAAA;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACLjI,OAAA;sBAAI0H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9D3H,OAAA;wBAAK0H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAAE,EAAAkD,kBAAA,GAAAvD,MAAM,CAAC2B,SAAS,cAAA4B,kBAAA,uBAAhBA,kBAAA,CAAkBK,cAAc,KAAI,GAAG,EAAC,GAAC;sBAAA;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnGjI,OAAA;wBAAK0H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAAC,EAAC,EAAAmD,kBAAA,GAAAxD,MAAM,CAAC2B,SAAS,cAAA6B,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBM,OAAO,cAAAL,qBAAA,uBAAzBA,qBAAA,CAA2B5B,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,UAAQ;sBAAA;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC,eACLjI,OAAA;sBAAI0H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9D3H,OAAA;wBAAK0H,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC3H,OAAA;0BAAM0H,SAAS,EAAE,2EACfJ,MAAM,CAACsB,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GACtEtB,MAAM,CAACsB,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GACnE,+BAA+B,EAC9B;0BAAAjB,QAAA,EACAL,MAAM,CAACsB;wBAAkB;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACPjI,OAAA;0BAAM0H,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAElC,gBAAgB,EAAAuF,kBAAA,GAAC1D,MAAM,CAAC2B,SAAS,cAAA+B,kBAAA,uBAAhBA,kBAAA,CAAkBnF,UAAU;wBAAC;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLjI,OAAA;sBAAI0H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9D3H,OAAA;wBAAM0H,SAAS,EAAE,2EACfJ,MAAM,CAACzD,gBAAgB,KAAK,YAAY,GAAG,+BAA+B,GAC1EyD,MAAM,CAACzD,gBAAgB,KAAK,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,EAChG;wBAAA8D,QAAA,EACAL,MAAM,CAACzD;sBAAgB;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLjI,OAAA;sBAAI0H,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACxE3H,OAAA;wBAAK0H,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzC3H,OAAA;0BACE0H,SAAS,EAAC,4CAA4C;0BACtD2D,KAAK,EAAC,cAAc;0BACpBnD,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAACF,MAAM,CAAE;0BAAAK,QAAA,eAExC3H,OAAA,CAACR,GAAG;4BAACkI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACTjI,OAAA;0BACE0H,SAAS,EAAC,uCAAuC;0BACjD2D,KAAK,EAAC,kBAAkB;0BACxBnD,OAAO,EAAEA,CAAA,KAAMT,aAAa,CAACH,MAAM,CAAE;0BAAAK,QAAA,eAErC3H,OAAA,CAACF,IAAI;4BAAC4H,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTjI,OAAA;0BACE0H,SAAS,EAAC,uCAAuC;0BACjDQ,OAAO,EAAEA,CAAA,KAAMb,aAAa,CAACC,MAAM,CAAE;0BACrC+D,KAAK,EAAC,aAAa;0BAAA1D,QAAA,eAEnB3H,OAAA,CAACP,IAAI;4BAACiI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTjI,OAAA;0BACE0H,SAAS,EAAC,qCAAqC;0BAC/CQ,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAACK,MAAM,CAACN,GAAG,CAAE;0BAC9CqE,KAAK,EAAC,eAAe;0BAAA1D,QAAA,eAErB3H,OAAA,CAACN,MAAM;4BAACgI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAhFAX,MAAM,CAACN,GAAG;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiFN,CAAC;gBAAA,CACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGNlG,gBAAgB,IAAII,cAAc,iBACjCnC,OAAA;MAAK0H,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF3H,OAAA;QAAK0H,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAC1E3H,OAAA;UACE0H,SAAS,EAAC,0DAA0D;UACpEQ,OAAO,EAAEA,CAAA,KAAMlG,mBAAmB,CAAC,KAAK,CAAE;UAAA2F,QAAA,eAE1C3H,OAAA;YAAKuJ,KAAK,EAAC,4BAA4B;YAAC7B,SAAS,EAAC,SAAS;YAAC8B,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAA/B,QAAA,eAC/G3H,OAAA;cAAM2J,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTjI,OAAA;UAAK0H,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C3H,OAAA;YAAK0H,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EACtG,EAAA9G,qBAAA,GAAAsB,cAAc,CAACmB,WAAW,cAAAzC,qBAAA,uBAA1BA,qBAAA,CAA4BR,MAAM,CAAC,CAAC,CAAC,KAAI;UAAG;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNjI,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAExF,cAAc,CAACmB;YAAW;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClFjI,OAAA;cAAG0H,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAExF,cAAc,CAACsB;YAAK;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjI,OAAA;UAAK0H,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC3H,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFjI,OAAA;cAAK0H,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjEjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAExF,cAAc,CAACoB;gBAAW;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNjI,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAExF,cAAc,CAACqB,OAAO,IAAI;gBAAK;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNjI,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC1DjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAExF,cAAc,CAACuB,KAAK,IAAI;gBAAK;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNjI,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7DjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAExF,cAAc,CAACwB,QAAQ,IAAI;gBAAK;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjI,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEjI,OAAA;cAAK0H,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3H,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,EAAA7G,qBAAA,GAAAqB,cAAc,CAAC8G,SAAS,cAAAnI,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BoI,aAAa,cAAAnI,sBAAA,uBAAvCA,sBAAA,CAAyCoI,cAAc,CAAC,CAAC,KAAI;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACNjI,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjEjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,EAAA3G,sBAAA,GAAAmB,cAAc,CAAC8G,SAAS,cAAAjI,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BsI,WAAW,cAAArI,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCR,MAAM,cAAAS,sBAAA,uBAA7CA,sBAAA,CAA+CiI,cAAc,CAAC,CAAC,KAAI;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CAAC,eACNjI,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpEjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAE,EAAAxG,sBAAA,GAAAgB,cAAc,CAAC8G,SAAS,cAAA9H,sBAAA,uBAAxBA,sBAAA,CAA0B+J,cAAc,KAAI,GAAG,EAAC,GAAC;gBAAA;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNjI,OAAA;gBAAA2H,QAAA,gBACE3H,OAAA;kBAAG0H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChEjI,OAAA;kBAAG0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAElC,gBAAgB,EAAArE,sBAAA,GAACe,cAAc,CAAC8G,SAAS,cAAA7H,sBAAA,uBAAxBA,sBAAA,CAA0ByE,UAAU;gBAAC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjI,OAAA;UAAK0H,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjD3H,OAAA;YAAI0H,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFjI,OAAA;YAAK0H,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3H,OAAA;cAAA2H,QAAA,gBACE3H,OAAA;gBAAG0H,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzDjI,OAAA;gBAAG0H,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExF,cAAc,CAAC0B;cAAgB;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACNjI,OAAA;cAAA2H,QAAA,gBACE3H,OAAA;gBAAG0H,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3DjI,OAAA;gBAAM0H,SAAS,EAAE,2EACfvF,cAAc,CAACyG,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GAC9EzG,cAAc,CAACyG,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GAC3E,+BAA+B,EAC9B;gBAAAjB,QAAA,EACAxF,cAAc,CAACyG;cAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAhG,aAAa,IAAIE,cAAc,iBAC9BnC,OAAA;MAAK0H,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF3H,OAAA;QAAK0H,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAC1E3H,OAAA;UACE0H,SAAS,EAAC,0DAA0D;UACpEQ,OAAO,EAAEA,CAAA,KAAMhG,gBAAgB,CAAC,KAAK,CAAE;UAAAyF,QAAA,eAEvC3H,OAAA;YAAKuJ,KAAK,EAAC,4BAA4B;YAAC7B,SAAS,EAAC,SAAS;YAAC8B,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAA/B,QAAA,eAC/G3H,OAAA;cAAM2J,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTjI,OAAA;UAAI0H,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GAAC,uBAAqB,EAACxF,cAAc,CAACmB,WAAW;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5GjI,OAAA;UAAK0H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3H,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EjI,OAAA;cAAK0H,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC3H,OAAA;gBAAK0H,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EACnD,mEAAmExF,cAAc,CAAC6E,GAAG;cAAa;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjI,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EjI,OAAA;cAAK0H,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC3H,OAAA;gBAAK0H,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EACnD;AACrB;AACA;AACA;AACA;AACA,kBAAkBxF,cAAc,CAAC6E,GAAG;AACpC;AACA;AACA;AACA;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACqB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjI,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAI0H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEjI,OAAA;cAAK0H,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC3H,OAAA;gBAAG0H,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAExF,cAAc,CAACmJ,MAAM,IAAI;cAAsB;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtH,EAAA,CAr0BID,OAAO;AAAA6K,EAAA,GAAP7K,OAAO;AAu0Bb,eAAeA,OAAO;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}