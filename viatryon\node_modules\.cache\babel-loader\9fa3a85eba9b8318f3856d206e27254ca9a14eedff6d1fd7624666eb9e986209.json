{"ast": null, "code": "/**\n * Utility for removing white backgrounds from images\n * Only removes pure white and very close whites to preserve light gray watches\n */\n\n/**\n * Remove white background from an image\n * @param {string} imageSrc - Image source URL or data URL\n * @param {number} tolerance - Color tolerance for white detection (0-255, default: 5)\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeWhiteBackground = async (imageSrc, tolerance = 5) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0);\n\n        // Get image data\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        // Create a copy of the image data for edge detection\n        const edgeData = new Uint8ClampedArray(data.length);\n        edgeData.set(data);\n\n        // Process pixels\n        for (let i = 0; i < data.length; i += 4) {\n          const red = data[i];\n          const green = data[i + 1];\n          const blue = data[i + 2];\n\n          // Check if pixel is white or very close to white\n          const isWhite = isWhitePixel(red, green, blue, tolerance);\n          if (isWhite) {\n            // Check if this is an edge pixel\n            const isEdge = isEdgePixel(edgeData, i, canvas.width);\n            if (!isEdge) {\n              // Make pixel transparent only if it's not an edge\n              data[i + 3] = 0;\n            }\n          }\n        }\n\n        // Put the modified image data back\n        ctx.putImageData(imageData, 0, 0);\n\n        // Convert to data URL\n        const processedImageUrl = canvas.toDataURL('image/png');\n        resolve(processedImageUrl);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n    img.src = imageSrc;\n  });\n};\n\n/**\n * Check if a pixel is white or very close to white\n * @param {number} r - Red value (0-255)\n * @param {number} g - Green value (0-255)\n * @param {number} b - Blue value (0-255)\n * @param {number} tolerance - Tolerance for white detection\n * @returns {boolean} - True if pixel is considered white\n */\nconst isWhitePixel = (r, g, b, tolerance) => {\n  // Pure white check\n  if (r === 255 && g === 255 && b === 255) {\n    return true;\n  }\n\n  // Near-white check with tolerance\n  // Only consider pixels that are very close to pure white\n  const whiteThreshold = 255 - tolerance;\n  return r >= whiteThreshold && g >= whiteThreshold && b >= whiteThreshold;\n};\n\n/**\n * Check if a pixel is part of an edge\n * @param {Uint8ClampedArray} data - Image data\n * @param {number} index - Pixel index\n * @param {number} width - Image width\n * @returns {boolean} - True if pixel is part of an edge\n */\nconst isEdgePixel = (data, index, width) => {\n  const pixelSize = 4;\n  const height = data.length / (width * pixelSize);\n  const x = index / pixelSize % width;\n  const y = Math.floor(index / pixelSize / width);\n\n  // Skip edge pixels of the image\n  if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {\n    return true;\n  }\n\n  // Check surrounding pixels for non-white colors\n  const surroundingPixels = [data[index - width * pixelSize - pixelSize],\n  // top-left\n  data[index - width * pixelSize],\n  // top\n  data[index - width * pixelSize + pixelSize],\n  // top-right\n  data[index - pixelSize],\n  // left\n  data[index + pixelSize],\n  // right\n  data[index + width * pixelSize - pixelSize],\n  // bottom-left\n  data[index + width * pixelSize],\n  // bottom\n  data[index + width * pixelSize + pixelSize] // bottom-right\n  ];\n\n  // If any surrounding pixel is not white, this is an edge pixel\n  return surroundingPixels.some((pixel, i) => {\n    if (pixel === undefined) return false;\n    const r = data[index - width * pixelSize - pixelSize + i * pixelSize];\n    const g = data[index - width * pixelSize - pixelSize + i * pixelSize + 1];\n    const b = data[index - width * pixelSize - pixelSize + i * pixelSize + 2];\n    return !isWhitePixel(r, g, b, 5);\n  });\n};\n\n/**\n * Batch process multiple images to remove white backgrounds\n * @param {string[]} imageSrcs - Array of image source URLs\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string[]>} - Array of processed image data URLs\n */\nexport const batchRemoveWhiteBackground = async (imageSrcs, tolerance = 10) => {\n  const promises = imageSrcs.map(src => removeWhiteBackground(src, tolerance));\n  return Promise.all(promises);\n};\n\n/**\n * Process an image file and return processed data URL\n * @param {File} file - Image file\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string>} - Processed image data URL\n */\nexport const processImageFile = async (file, tolerance = 10) => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = async e => {\n      try {\n        const processedImage = await removeWhiteBackground(e.target.result, tolerance);\n        resolve(processedImage);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    reader.onerror = () => {\n      reject(new Error('Failed to read file'));\n    };\n    reader.readAsDataURL(file);\n  });\n};\n\n/**\n * Check if an image has a white background\n * @param {string} imageSrc - Image source URL\n * @returns {Promise<boolean>} - True if image has significant white background\n */\nexport const hasWhiteBackground = async imageSrc => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        let whitePixelCount = 0;\n        const totalPixels = data.length / 4;\n        for (let i = 0; i < data.length; i += 4) {\n          const red = data[i];\n          const green = data[i + 1];\n          const blue = data[i + 2];\n          if (isWhitePixel(red, green, blue, 10)) {\n            whitePixelCount++;\n          }\n        }\n\n        // Consider image to have white background if more than 30% pixels are white\n        const whitePercentage = whitePixelCount / totalPixels * 100;\n        resolve(whitePercentage > 30);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n    img.src = imageSrc;\n  });\n};", "map": {"version": 3, "names": ["removeWhiteBackground", "imageSrc", "tolerance", "Promise", "resolve", "reject", "img", "Image", "crossOrigin", "onload", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "imageData", "getImageData", "data", "edgeData", "Uint8ClampedArray", "length", "set", "i", "red", "green", "blue", "<PERSON><PERSON><PERSON><PERSON>", "isWhitePixel", "isEdge", "isEdgePixel", "putImageData", "processedImageUrl", "toDataURL", "error", "onerror", "Error", "src", "r", "g", "b", "whiteT<PERSON>eshold", "index", "pixelSize", "x", "y", "Math", "floor", "surroundingPixels", "some", "pixel", "undefined", "batchRemoveWhiteBackground", "imageSrcs", "promises", "map", "all", "processImageFile", "file", "reader", "FileReader", "e", "processedImage", "target", "result", "readAsDataURL", "hasWhiteBackground", "whitePixelCount", "totalPixels", "whitePercentage"], "sources": ["D:/Via/test/viatryon/src/utils/backgroundRemover.js"], "sourcesContent": ["/**\n * Utility for removing white backgrounds from images\n * Only removes pure white and very close whites to preserve light gray watches\n */\n\n/**\n * Remove white background from an image\n * @param {string} imageSrc - Image source URL or data URL\n * @param {number} tolerance - Color tolerance for white detection (0-255, default: 5)\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeWhiteBackground = async (imageSrc, tolerance = 5) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0);\n\n        // Get image data\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        // Create a copy of the image data for edge detection\n        const edgeData = new Uint8ClampedArray(data.length);\n        edgeData.set(data);\n\n        // Process pixels\n        for (let i = 0; i < data.length; i += 4) {\n          const red = data[i];\n          const green = data[i + 1];\n          const blue = data[i + 2];\n\n          // Check if pixel is white or very close to white\n          const isWhite = isWhitePixel(red, green, blue, tolerance);\n\n          if (isWhite) {\n            // Check if this is an edge pixel\n            const isEdge = isEdgePixel(edgeData, i, canvas.width);\n            \n            if (!isEdge) {\n              // Make pixel transparent only if it's not an edge\n              data[i + 3] = 0;\n            }\n          }\n        }\n\n        // Put the modified image data back\n        ctx.putImageData(imageData, 0, 0);\n\n        // Convert to data URL\n        const processedImageUrl = canvas.toDataURL('image/png');\n        resolve(processedImageUrl);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    img.src = imageSrc;\n  });\n};\n\n/**\n * Check if a pixel is white or very close to white\n * @param {number} r - Red value (0-255)\n * @param {number} g - Green value (0-255)\n * @param {number} b - Blue value (0-255)\n * @param {number} tolerance - Tolerance for white detection\n * @returns {boolean} - True if pixel is considered white\n */\nconst isWhitePixel = (r, g, b, tolerance) => {\n  // Pure white check\n  if (r === 255 && g === 255 && b === 255) {\n    return true;\n  }\n\n  // Near-white check with tolerance\n  // Only consider pixels that are very close to pure white\n  const whiteThreshold = 255 - tolerance;\n  return r >= whiteThreshold && g >= whiteThreshold && b >= whiteThreshold;\n};\n\n/**\n * Check if a pixel is part of an edge\n * @param {Uint8ClampedArray} data - Image data\n * @param {number} index - Pixel index\n * @param {number} width - Image width\n * @returns {boolean} - True if pixel is part of an edge\n */\nconst isEdgePixel = (data, index, width) => {\n  const pixelSize = 4;\n  const height = data.length / (width * pixelSize);\n  const x = (index / pixelSize) % width;\n  const y = Math.floor((index / pixelSize) / width);\n\n  // Skip edge pixels of the image\n  if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {\n    return true;\n  }\n\n  // Check surrounding pixels for non-white colors\n  const surroundingPixels = [\n    data[index - width * pixelSize - pixelSize], // top-left\n    data[index - width * pixelSize], // top\n    data[index - width * pixelSize + pixelSize], // top-right\n    data[index - pixelSize], // left\n    data[index + pixelSize], // right\n    data[index + width * pixelSize - pixelSize], // bottom-left\n    data[index + width * pixelSize], // bottom\n    data[index + width * pixelSize + pixelSize] // bottom-right\n  ];\n\n  // If any surrounding pixel is not white, this is an edge pixel\n  return surroundingPixels.some((pixel, i) => {\n    if (pixel === undefined) return false;\n    const r = data[index - width * pixelSize - pixelSize + (i * pixelSize)];\n    const g = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 1];\n    const b = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 2];\n    return !isWhitePixel(r, g, b, 5);\n  });\n};\n\n/**\n * Batch process multiple images to remove white backgrounds\n * @param {string[]} imageSrcs - Array of image source URLs\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string[]>} - Array of processed image data URLs\n */\nexport const batchRemoveWhiteBackground = async (imageSrcs, tolerance = 10) => {\n  const promises = imageSrcs.map(src => removeWhiteBackground(src, tolerance));\n  return Promise.all(promises);\n};\n\n/**\n * Process an image file and return processed data URL\n * @param {File} file - Image file\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string>} - Processed image data URL\n */\nexport const processImageFile = async (file, tolerance = 10) => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n\n    reader.onload = async (e) => {\n      try {\n        const processedImage = await removeWhiteBackground(e.target.result, tolerance);\n        resolve(processedImage);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    reader.onerror = () => {\n      reject(new Error('Failed to read file'));\n    };\n\n    reader.readAsDataURL(file);\n  });\n};\n\n/**\n * Check if an image has a white background\n * @param {string} imageSrc - Image source URL\n * @returns {Promise<boolean>} - True if image has significant white background\n */\nexport const hasWhiteBackground = async (imageSrc) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        let whitePixelCount = 0;\n        const totalPixels = data.length / 4;\n\n        for (let i = 0; i < data.length; i += 4) {\n          const red = data[i];\n          const green = data[i + 1];\n          const blue = data[i + 2];\n\n          if (isWhitePixel(red, green, blue, 10)) {\n            whitePixelCount++;\n          }\n        }\n\n        // Consider image to have white background if more than 30% pixels are white\n        const whitePercentage = (whitePixelCount / totalPixels) * 100;\n        resolve(whitePercentage > 30);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    img.src = imageSrc;\n  });\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,qBAAqB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,SAAS,GAAG,CAAC,KAAK;EACtE,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,MAAM;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QAEnCJ,MAAM,CAACK,KAAK,GAAGT,GAAG,CAACS,KAAK;QACxBL,MAAM,CAACM,MAAM,GAAGV,GAAG,CAACU,MAAM;;QAE1B;QACAH,GAAG,CAACI,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;QAExB;QACA,MAAMY,SAAS,GAAGL,GAAG,CAACM,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;QACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;;QAE3B;QACA,MAAMC,QAAQ,GAAG,IAAIC,iBAAiB,CAACF,IAAI,CAACG,MAAM,CAAC;QACnDF,QAAQ,CAACG,GAAG,CAACJ,IAAI,CAAC;;QAElB;QACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACG,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMC,GAAG,GAAGN,IAAI,CAACK,CAAC,CAAC;UACnB,MAAME,KAAK,GAAGP,IAAI,CAACK,CAAC,GAAG,CAAC,CAAC;UACzB,MAAMG,IAAI,GAAGR,IAAI,CAACK,CAAC,GAAG,CAAC,CAAC;;UAExB;UACA,MAAMI,OAAO,GAAGC,YAAY,CAACJ,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE1B,SAAS,CAAC;UAEzD,IAAI2B,OAAO,EAAE;YACX;YACA,MAAME,MAAM,GAAGC,WAAW,CAACX,QAAQ,EAAEI,CAAC,EAAEf,MAAM,CAACK,KAAK,CAAC;YAErD,IAAI,CAACgB,MAAM,EAAE;cACX;cACAX,IAAI,CAACK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;YACjB;UACF;QACF;;QAEA;QACAZ,GAAG,CAACoB,YAAY,CAACf,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEjC;QACA,MAAMgB,iBAAiB,GAAGxB,MAAM,CAACyB,SAAS,CAAC,WAAW,CAAC;QACvD/B,OAAO,CAAC8B,iBAAiB,CAAC;MAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACd/B,MAAM,CAAC+B,KAAK,CAAC;MACf;IACF,CAAC;IAED9B,GAAG,CAAC+B,OAAO,GAAG,MAAM;MAClBhC,MAAM,CAAC,IAAIiC,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC3C,CAAC;IAEDhC,GAAG,CAACiC,GAAG,GAAGtC,QAAQ;EACpB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,YAAY,GAAGA,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAExC,SAAS,KAAK;EAC3C;EACA,IAAIsC,CAAC,KAAK,GAAG,IAAIC,CAAC,KAAK,GAAG,IAAIC,CAAC,KAAK,GAAG,EAAE;IACvC,OAAO,IAAI;EACb;;EAEA;EACA;EACA,MAAMC,cAAc,GAAG,GAAG,GAAGzC,SAAS;EACtC,OAAOsC,CAAC,IAAIG,cAAc,IAAIF,CAAC,IAAIE,cAAc,IAAID,CAAC,IAAIC,cAAc;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMX,WAAW,GAAGA,CAACZ,IAAI,EAAEwB,KAAK,EAAE7B,KAAK,KAAK;EAC1C,MAAM8B,SAAS,GAAG,CAAC;EACnB,MAAM7B,MAAM,GAAGI,IAAI,CAACG,MAAM,IAAIR,KAAK,GAAG8B,SAAS,CAAC;EAChD,MAAMC,CAAC,GAAIF,KAAK,GAAGC,SAAS,GAAI9B,KAAK;EACrC,MAAMgC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEL,KAAK,GAAGC,SAAS,GAAI9B,KAAK,CAAC;;EAEjD;EACA,IAAI+B,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK/B,KAAK,GAAG,CAAC,IAAIgC,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK/B,MAAM,GAAG,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;;EAEA;EACA,MAAMkC,iBAAiB,GAAG,CACxB9B,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,CAAC;EAAE;EAC7CzB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,CAAC;EAAE;EACjCzB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,CAAC;EAAE;EAC7CzB,IAAI,CAACwB,KAAK,GAAGC,SAAS,CAAC;EAAE;EACzBzB,IAAI,CAACwB,KAAK,GAAGC,SAAS,CAAC;EAAE;EACzBzB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,CAAC;EAAE;EAC7CzB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,CAAC;EAAE;EACjCzB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,CAAC,CAAC;EAAA,CAC7C;;EAED;EACA,OAAOK,iBAAiB,CAACC,IAAI,CAAC,CAACC,KAAK,EAAE3B,CAAC,KAAK;IAC1C,IAAI2B,KAAK,KAAKC,SAAS,EAAE,OAAO,KAAK;IACrC,MAAMb,CAAC,GAAGpB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,GAAIpB,CAAC,GAAGoB,SAAU,CAAC;IACvE,MAAMJ,CAAC,GAAGrB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,GAAIpB,CAAC,GAAGoB,SAAU,GAAG,CAAC,CAAC;IAC3E,MAAMH,CAAC,GAAGtB,IAAI,CAACwB,KAAK,GAAG7B,KAAK,GAAG8B,SAAS,GAAGA,SAAS,GAAIpB,CAAC,GAAGoB,SAAU,GAAG,CAAC,CAAC;IAC3E,OAAO,CAACf,YAAY,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;EAClC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMY,0BAA0B,GAAG,MAAAA,CAAOC,SAAS,EAAErD,SAAS,GAAG,EAAE,KAAK;EAC7E,MAAMsD,QAAQ,GAAGD,SAAS,CAACE,GAAG,CAAClB,GAAG,IAAIvC,qBAAqB,CAACuC,GAAG,EAAErC,SAAS,CAAC,CAAC;EAC5E,OAAOC,OAAO,CAACuD,GAAG,CAACF,QAAQ,CAAC;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,gBAAgB,GAAG,MAAAA,CAAOC,IAAI,EAAE1D,SAAS,GAAG,EAAE,KAAK;EAC9D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMwD,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAE/BD,MAAM,CAACpD,MAAM,GAAG,MAAOsD,CAAC,IAAK;MAC3B,IAAI;QACF,MAAMC,cAAc,GAAG,MAAMhE,qBAAqB,CAAC+D,CAAC,CAACE,MAAM,CAACC,MAAM,EAAEhE,SAAS,CAAC;QAC9EE,OAAO,CAAC4D,cAAc,CAAC;MACzB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACd/B,MAAM,CAAC+B,KAAK,CAAC;MACf;IACF,CAAC;IAEDyB,MAAM,CAACxB,OAAO,GAAG,MAAM;MACrBhC,MAAM,CAAC,IAAIiC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC1C,CAAC;IAEDuB,MAAM,CAACM,aAAa,CAACP,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,kBAAkB,GAAG,MAAOnE,QAAQ,IAAK;EACpD,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,MAAM;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QAEnCJ,MAAM,CAACK,KAAK,GAAGT,GAAG,CAACS,KAAK;QACxBL,MAAM,CAACM,MAAM,GAAGV,GAAG,CAACU,MAAM;QAC1BH,GAAG,CAACI,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAExB,MAAMY,SAAS,GAAGL,GAAG,CAACM,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;QACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;QAE3B,IAAIiD,eAAe,GAAG,CAAC;QACvB,MAAMC,WAAW,GAAGlD,IAAI,CAACG,MAAM,GAAG,CAAC;QAEnC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACG,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMC,GAAG,GAAGN,IAAI,CAACK,CAAC,CAAC;UACnB,MAAME,KAAK,GAAGP,IAAI,CAACK,CAAC,GAAG,CAAC,CAAC;UACzB,MAAMG,IAAI,GAAGR,IAAI,CAACK,CAAC,GAAG,CAAC,CAAC;UAExB,IAAIK,YAAY,CAACJ,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE,EAAE,CAAC,EAAE;YACtCyC,eAAe,EAAE;UACnB;QACF;;QAEA;QACA,MAAME,eAAe,GAAIF,eAAe,GAAGC,WAAW,GAAI,GAAG;QAC7DlE,OAAO,CAACmE,eAAe,GAAG,EAAE,CAAC;MAC/B,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACd/B,MAAM,CAAC+B,KAAK,CAAC;MACf;IACF,CAAC;IAED9B,GAAG,CAAC+B,OAAO,GAAG,MAAM;MAClBhC,MAAM,CAAC,IAAIiC,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC3C,CAAC;IAEDhC,GAAG,CAACiC,GAAG,GAAGtC,QAAQ;EACpB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}