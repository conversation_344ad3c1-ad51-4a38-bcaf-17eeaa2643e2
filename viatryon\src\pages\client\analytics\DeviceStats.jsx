import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Smartphone, Monitor, Globe, Users } from 'lucide-react';

const DeviceStats = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [deviceStats, setDeviceStats] = useState([]);
  const timeRanges = ['7d', '30d', '90d', '1y'];

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  // Fetch device stats data
  useEffect(() => {
    const fetchDeviceStats = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        const response = await fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setDeviceStats(data);
        } else {
          console.error('Failed to fetch device stats data');
        }
      } catch (error) {
        console.error('Error fetching device stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDeviceStats();
  }, [timeRange]);

  // Format data for charts
  const deviceData = deviceStats.map(device => ({
    name: device._id,
    value: device.sessions,
    conversionRate: device.conversionRate,
    avgDuration: device.avgDuration
  }));

  // Extract browser and OS data from device details
  const browserData = [];
  const osData = [];

  deviceStats.forEach(device => {
    device.details?.forEach(detail => {
      if (detail.browser) {
        const existing = browserData.find(b => b.name === detail.browser);
        if (existing) {
          existing.value += detail.sessions;
        } else {
          browserData.push({ name: detail.browser, value: detail.sessions });
        }
      }

      if (detail.os) {
        const existing = osData.find(o => o.name === detail.os);
        if (existing) {
          existing.value += detail.sessions;
        } else {
          osData.push({ name: detail.os, value: detail.sessions });
        }
      }
    });
  });

  // Format device trends data from API
  const deviceTrends = deviceStats.reduce((acc, device) => {
    device.trends?.forEach(trend => {
      const existingDay = acc.find(d => d.date === trend.date);
      if (existingDay) {
        existingDay[device._id.toLowerCase()] = trend.sessions;
      } else {
        acc.push({
          date: trend.date,
          [device._id.toLowerCase()]: trend.sessions
        });
      }
    });
    return acc;
  }, []).sort((a, b) => new Date(a.date) - new Date(b.date));

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Calculate metrics
  const totalSessions = deviceData.reduce((sum, device) => sum + device.value, 0);
  const mobileSessions = deviceData.find(d => d.name.toLowerCase() === 'mobile')?.value || 0;
  const desktopSessions = deviceData.find(d => d.name.toLowerCase() === 'desktop')?.value || 0;
  const tabletSessions = deviceData.find(d => d.name.toLowerCase() === 'tablet')?.value || 0;

  const metrics = [
    {
      title: 'Total Sessions',
      value: totalSessions.toLocaleString(),
      change: 'No change',
      trend: 'neutral',
      icon: <Users className="h-6 w-6 text-[#2D8C88]" />
    },
    {
      title: 'Mobile Usage',
      value: `${((mobileSessions / totalSessions) * 100).toFixed(1)}%`,
      change: 'No change',
      trend: 'neutral',
      icon: <Smartphone className="h-6 w-6 text-blue-500" />
    },
    {
      title: 'Desktop Usage',
      value: `${((desktopSessions / totalSessions) * 100).toFixed(1)}%`,
      change: 'No change',
      trend: 'neutral',
      icon: <Monitor className="h-6 w-6 text-green-500" />
    },
    {
      title: 'Tablet Usage',
      value: `${((tabletSessions / totalSessions) * 100).toFixed(1)}%`,
      change: 'No change',
      trend: 'neutral',
      icon: <Globe className="h-6 w-6 text-purple-500" />
    }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {timeRanges.map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                {metric.icon}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Device Usage Pie Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Device Usage Distribution</h3>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={150}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value, name) => [
                    `${value.toLocaleString()} sessions`,
                    name
                  ]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Browser Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Browser Distribution</h3>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={browserData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={150}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {browserData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value, name) => [
                    `${value.toLocaleString()} sessions`,
                    name
                  ]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* OS Distribution */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-xl shadow-sm p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Operating System Distribution</h3>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={osData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={150}
                fill="#8884d8"
                dataKey="value"
              >
                {osData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value, name) => [
                  `${value.toLocaleString()} sessions`,
                  name
                ]}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </motion.div>
    </div>
  );
};

export default DeviceStats;