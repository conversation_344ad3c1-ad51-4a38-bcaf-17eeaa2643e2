import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

const ClientSidebar = ({ isOpen, onClose, collapsed, setCollapsed }) => {
  const location = useLocation();
  const user = JSON.parse(localStorage.getItem('user')) || {};

  const menuItems = [
    {
      title: 'Dashboard',
      path: '/client/dashboard',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
      ),
    },
    {
      title: 'Client Settings',
      path: '/client/settings',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
    },
    {
      title: 'Analytics',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      subItems: [
        {
          title: 'Overview',
          path: '/client/analytics/overview',
        },
        {
          title: 'Product Performance',
          path: '/client/analytics/product-performance',
        },
        {
          title: 'User Engagement',
          path: '/client/analytics/user-engagement',
        },
        {
          title: 'Time Analysis',
          path: '/client/analytics/time-analysis',
        },
        {
          title: 'Device Stats',
          path: '/client/analytics/device-stats',
        }
      ],
    },
  ];

  // Sidebar for mobile (animated)
  const mobileSidebar = (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={onClose}
          />
          <motion.div
            initial={{ x: -280 }}
            animate={{ x: 0 }}
            exit={{ x: -280 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50 w-[280px] md:hidden"
          >
            {renderSidebarContent()}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  // Sidebar for desktop (always visible)
  const desktopSidebar = (
    <div className="hidden md:block fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50" style={{ width: collapsed ? 80 : 280 }}>
      {renderSidebarContent()}
    </div>
  );

  function renderSidebarContent() {
    return (
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <Link to="/" className="flex items-center justify-center w-full">
            <img
              src="/imgs/logo-only.png"
              alt="ViaTryon"
              className={collapsed ? 'h-8 w-8 transition-all duration-200' : 'h-8 w-auto transition-all duration-200'}
              style={collapsed ? { minWidth: 32 } : { minWidth: 32, marginRight: 8 }}
            />
            {!collapsed && (
              <span className="font-serif text-xl font-medium text-[#1F2937] ml-2">ViaTryon</span>
            )}
          </Link>
          <button
            onClick={() => setCollapsed(!collapsed)}
            className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 hidden md:block"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={collapsed ? "M13 5l7 7-7 7M5 5l7 7-7 7" : "M11 19l-7-7 7-7m8 14l-7-7 7-7"}
              />
            </svg>
          </button>
        </div>
        {/* Menu Items */}
        <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
          {menuItems.map((item) => (
            <div key={item.path || item.title}>
              {item.subItems ? (
                <div className="space-y-1">
                  <div className={`flex items-center space-x-3 px-4 py-3 rounded-lg ${
                    location.pathname.startsWith('/client/analytics')
                      ? 'bg-[#2D8C88]/10 text-[#2D8C88]'
                      : 'text-gray-600'
                  }`}>
                    <span className="flex-shrink-0">{item.icon}</span>
                    {!collapsed && <span className="font-medium">{item.title}</span>}
                  </div>
                  {!collapsed && (
                    <div className="ml-8 space-y-1">
                      {item.subItems.map((subItem) => (
                        <Link
                          key={subItem.path}
                          to={subItem.path}
                          className={`block px-4 py-2 rounded-lg text-sm ${
                            location.pathname === subItem.path
                              ? 'bg-[#2D8C88]/10 text-[#2D8C88]'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {subItem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  to={item.path}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${
                    location.pathname === item.path
                      ? 'bg-[#2D8C88]/10 text-[#2D8C88]'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    if (window.innerWidth < 768) {
                      onClose();
                    }
                  }}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  {!collapsed && <span className="font-medium">{item.title}</span>}
                </Link>
              )}
            </div>
          ))}
        </nav>
        {/* User Profile */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            {!collapsed && (
              <div>
                <p className="text-sm font-medium text-gray-900">Client User</p>
                <p className="text-xs text-gray-500">{user.email}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {mobileSidebar}
      {desktopSidebar}
    </>
  );
};

export default ClientSidebar; 