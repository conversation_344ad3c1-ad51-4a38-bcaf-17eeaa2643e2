{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Watches.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { loadHeroImage } from '../utils/imageLoader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Watches = () => {\n  _s();\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [watchCollection, setWatchCollection] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [heroWatch, setHeroWatch] = useState({\n    image: '/imgs/watches_hero/watch_1_hero.png'\n  });\n\n  // Filter categories\n  const filterCategories = [{\n    id: 'all',\n    name: 'All Watches'\n  }, {\n    id: 'mens',\n    name: 'Men\\'s'\n  }, {\n    id: 'womens',\n    name: 'Women\\'s'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }];\n\n  // Load watch collection with processed images\n  useEffect(() => {\n    const loadWatches = async () => {\n      try {\n        const collections = await getProductCollections();\n        setWatchCollection(collections.watches || []);\n\n        // Load hero image (pre-processed without background)\n        const heroImageUrl = await loadHeroImage('watches');\n        if (heroImageUrl) {\n          setHeroWatch({\n            image: heroImageUrl\n          });\n        } else if (collections.watches && collections.watches.length > 0) {\n          // Fallback to first collection image\n          setHeroWatch({\n            image: collections.watches[0].image\n          });\n        }\n      } catch (error) {\n        console.error('Error loading watch collection:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadWatches();\n  }, []);\n\n  // Filter watches based on active filter\n  const filteredWatches = activeFilter === 'all' ? watchCollection : watchCollection.filter(watch => watch.categories.includes(activeFilter));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative min-h-[100vh] pt-24 pb-16 md:pt-32 md:pb-24 lg:pt-40 lg:pb-32 overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2 text-center lg:text-left\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6\n              },\n              className: \"inline-block px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-[#2D8C88]\",\n                children: \"Experience Luxury\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6\n              },\n              className: \"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-serif text-[#1F2937] mb-4 md:mb-6 leading-tight\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"italic\",\n                children: \"Discover Our\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n                children: \"Watch Collection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"text-base md:text-lg lg:text-xl text-[#1F2937] mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans font-light leading-relaxed\",\n              children: \"Explore our curated selection of timepieces, from classic elegance to modern innovation, all available for virtual try-on.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 justify-center lg:justify-start mb-8 md:mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/virtual-try-on?category=watches\",\n                className: \"relative text-white px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center group\",\n                style: {\n                  backgroundColor: '#2D8C88'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this), \"Try On Virtually\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/bracelets\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"relative bg-white border-2 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center w-full sm:w-auto group\",\n                  style: {\n                    borderColor: '#2D8C88',\n                    color: '#2D8C88'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 6h16M4 12h16M4 18h7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 139,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 23\n                    }, this), \"View Bracelets\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"lg:w-1/2 relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:hidden absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-[18rem] h-[18rem] sm:w-[22rem] sm:h-[22rem] rounded-full bg-gradient-to-br from-[#2D8C88]/10 to-[#F28C38]/10 shadow-lg border border-white/20 backdrop-blur-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden lg:block absolute w-[44rem] h-[44rem] rounded-full shadow-inner border\",\n                style: {\n                  background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n                  borderColor: 'rgba(242, 140, 56, 0.2)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: heroWatch.image,\n                alt: \"Luxury Watch\",\n                className: \"h-[16rem] sm:h-[20rem] md:h-[28rem] lg:h-[36rem] w-auto object-contain drop-shadow-[0_8px_25px_rgba(0,0,0,0.15)] max-w-full transform-gpu\",\n                style: {\n                  maxHeight: '100%',\n                  width: 'auto',\n                  objectFit: 'contain',\n                  transform: 'translateZ(0)',\n                  backfaceVisibility: 'hidden',\n                  WebkitTransform: 'translateZ(0)',\n                  WebkitBackfaceVisibility: 'hidden'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -bottom-8 sm:-bottom-12 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-2/3 sm:w-3/4 h-6 sm:h-8 md:h-12 rounded-full blur-xl md:blur-2xl\",\n                style: {\n                  backgroundColor: 'rgba(31, 41, 55, 0.15)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-12 md:mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl lg:text-4xl font-serif text-[#1F2937] mb-4\",\n            children: \"Explore Our Timepieces\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base\",\n            children: \"Browse our collection of premium watches, each crafted with precision and available for virtual try-on.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"flex flex-wrap justify-center gap-2 md:gap-4 mb-8 md:mb-12\",\n          children: filterCategories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveFilter(category.id),\n            className: \"px-4 md:px-8 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-full transition-all flex items-center min-h-[44px] group\",\n            style: {\n              backgroundColor: activeFilter === category.id ? '#2D8C88' : '#FFFFFF',\n              color: activeFilter === category.id ? '#FFFFFF' : '#1F2937',\n              border: '1px solid #E5E7EB'\n            },\n            children: category.name\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-16 md:py-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-10 md:h-12 w-10 md:w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n              children: \"Loading watches with background removal...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8\",\n          children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: filteredWatches.map((watch, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              exit: {\n                opacity: 0,\n                scale: 0.9\n              },\n              className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative aspect-square mb-4 overflow-hidden rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: watch.image,\n                  alt: watch.name,\n                  className: \"w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-serif text-[#1F2937] mb-2\",\n                children: watch.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#2D8C88] font-medium mb-4\",\n                children: [\"$\", watch.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: `/virtual-try-on?category=watches&product=${index}`,\n                  className: \"text-[#2D8C88] hover:text-[#F28C38] transition-colors duration-200 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-1\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 25\n                  }, this), \"Try On\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this)]\n            }, `${watch.name}-${index}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Find Your Perfect\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Watch Today\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Try on our stunning collection of watches virtually with our cutting-edge AR technology. No downloads needed, just pure shopping magic.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/virtual-try-on\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this), \"Try It Now\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/bracelets\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this), \"Explore Bracelets\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(Watches, \"Q9xZ500WFDkvEy7U70ogjj2aDVc=\");\n_c = Watches;\nexport default Watches;\nvar _c;\n$RefreshReg$(_c, \"Watches\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "Link", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "loadHeroImage", "jsxDEV", "_jsxDEV", "Watches", "_s", "activeFilter", "setActiveFilter", "watchCollection", "setWatchCollection", "loading", "setLoading", "heroWatch", "setHeroWatch", "image", "filterCategories", "id", "name", "loadWatches", "collections", "watches", "heroImageUrl", "length", "error", "console", "filteredWatches", "filter", "watch", "categories", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "div", "initial", "opacity", "y", "animate", "transition", "duration", "h1", "p", "delay", "to", "backgroundColor", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "borderColor", "color", "scale", "src", "alt", "maxHeight", "width", "objectFit", "transform", "backfaceVisibility", "WebkitTransform", "WebkitBackfaceVisibility", "whileInView", "viewport", "once", "map", "category", "onClick", "border", "index", "exit", "price", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Watches.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { loadHeroImage } from '../utils/imageLoader';\n\nconst Watches = () => {\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [watchCollection, setWatchCollection] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [heroWatch, setHeroWatch] = useState({ image: '/imgs/watches_hero/watch_1_hero.png' });\n\n  // Filter categories\n  const filterCategories = [\n    { id: 'all', name: 'All Watches' },\n    { id: 'mens', name: 'Men\\'s' },\n    { id: 'womens', name: 'Women\\'s' },\n    { id: 'luxury', name: 'Luxury' },\n    { id: 'new', name: 'New Arrivals' }\n  ];\n\n  // Load watch collection with processed images\n  useEffect(() => {\n    const loadWatches = async () => {\n      try {\n        const collections = await getProductCollections();\n        setWatchCollection(collections.watches || []);\n\n        // Load hero image (pre-processed without background)\n        const heroImageUrl = await loadHeroImage('watches');\n        if (heroImageUrl) {\n          setHeroWatch({ image: heroImageUrl });\n        } else if (collections.watches && collections.watches.length > 0) {\n          // Fallback to first collection image\n          setHeroWatch({ image: collections.watches[0].image });\n        }\n      } catch (error) {\n        console.error('Error loading watch collection:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadWatches();\n  }, []);\n\n  // Filter watches based on active filter\n  const filteredWatches = activeFilter === 'all'\n    ? watchCollection\n    : watchCollection.filter(watch => watch.categories.includes(activeFilter));\n\n  return (\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section\n        className=\"relative min-h-[100vh] pt-24 pb-16 md:pt-32 md:pb-24 lg:pt-40 lg:pb-32 overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`,\n        }}\n      >\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n            }}\n          />\n          <div\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n            }}\n          />\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\">\n            <div className=\"lg:w-1/2 text-center lg:text-left\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                className=\"inline-block px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm mb-6\"\n              >\n                <span className=\"text-sm font-medium text-[#2D8C88]\">Experience Luxury</span>\n              </motion.div>\n\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-serif text-[#1F2937] mb-4 md:mb-6 leading-tight\"\n              >\n                <span className=\"italic\">Discover Our</span>\n                <br />\n                <span className=\"font-medium bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\n                  Watch Collection\n                </span>\n              </motion.h1>\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"text-base md:text-lg lg:text-xl text-[#1F2937] mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans font-light leading-relaxed\"\n              >\n                Explore our curated selection of timepieces, from classic elegance to modern innovation, all available for virtual try-on.\n              </motion.p>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n                className=\"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 justify-center lg:justify-start mb-8 md:mb-12\"\n              >\n                <Link\n                  to=\"/virtual-try-on?category=watches\"\n                  className=\"relative text-white px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center group\"\n                  style={{ backgroundColor: '#2D8C88' }}\n                >\n                  <span className=\"flex items-center justify-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Try On Virtually\n                  </span>\n                </Link>\n                <Link to=\"/bracelets\">\n                  <button\n                    className=\"relative bg-white border-2 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center w-full sm:w-auto group\"\n                    style={{ borderColor: '#2D8C88', color: '#2D8C88' }}\n                  >\n                    <span className=\"flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h7\" />\n                      </svg>\n                      View Bracelets\n                    </span>\n                  </button>\n                </Link>\n              </motion.div>\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8 }}\n              className=\"lg:w-1/2 relative\"\n            >\n              <div className=\"relative flex items-center justify-center\">\n                {/* Mobile-specific background */}\n                <div className=\"lg:hidden absolute inset-0 flex items-center justify-center\">\n                  <div className=\"w-[18rem] h-[18rem] sm:w-[22rem] sm:h-[22rem] rounded-full bg-gradient-to-br from-[#2D8C88]/10 to-[#F28C38]/10 shadow-lg border border-white/20 backdrop-blur-sm\"></div>\n                </div>\n                \n                {/* Desktop background */}\n                <div className=\"hidden lg:block absolute w-[44rem] h-[44rem] rounded-full shadow-inner border\"\n                  style={{\n                    background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n                    borderColor: 'rgba(242, 140, 56, 0.2)',\n                  }}\n                />\n                <img\n                  src={heroWatch.image}\n                  alt=\"Luxury Watch\"\n                  className=\"h-[16rem] sm:h-[20rem] md:h-[28rem] lg:h-[36rem] w-auto object-contain drop-shadow-[0_8px_25px_rgba(0,0,0,0.15)] max-w-full transform-gpu\"\n                  style={{\n                    maxHeight: '100%',\n                    width: 'auto',\n                    objectFit: 'contain',\n                    transform: 'translateZ(0)',\n                    backfaceVisibility: 'hidden',\n                    WebkitTransform: 'translateZ(0)',\n                    WebkitBackfaceVisibility: 'hidden'\n                  }}\n                />\n                <div\n                  className=\"absolute -bottom-8 sm:-bottom-12 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-2/3 sm:w-3/4 h-6 sm:h-8 md:h-12 rounded-full blur-xl md:blur-2xl\"\n                  style={{ backgroundColor: 'rgba(31, 41, 55, 0.15)' }}\n                />\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Filterable Gallery Section */}\n      <section className=\"py-16 md:py-20 bg-white\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-12 md:mb-16\"\n          >\n            <h2 className=\"text-2xl md:text-3xl lg:text-4xl font-serif text-[#1F2937] mb-4\">Explore Our Timepieces</h2>\n            <p className=\"text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base\">\n              Browse our collection of premium watches, each crafted with precision and available for virtual try-on.\n            </p>\n          </motion.div>\n\n          {/* Filter Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"flex flex-wrap justify-center gap-2 md:gap-4 mb-8 md:mb-12\"\n          >\n            {filterCategories.map((category) => (\n              <button\n                key={category.id}\n                onClick={() => setActiveFilter(category.id)}\n                className=\"px-4 md:px-8 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-full transition-all flex items-center min-h-[44px] group\"\n                style={{\n                  backgroundColor: activeFilter === category.id ? '#2D8C88' : '#FFFFFF',\n                  color: activeFilter === category.id ? '#FFFFFF' : '#1F2937',\n                  border: '1px solid #E5E7EB'\n                }}\n              >\n                {category.name}\n              </button>\n            ))}\n          </motion.div>\n\n          {/* Product Grid */}\n          {loading ? (\n            <div className=\"flex items-center justify-center py-16 md:py-20\">\n              <div className=\"text-center\">\n                <div className=\"animate-spin rounded-full h-10 md:h-12 w-10 md:w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4\"></div>\n                <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">Loading watches with background removal...</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8\">\n              <AnimatePresence>\n                {filteredWatches.map((watch, index) => (\n                  <motion.div\n                    key={`${watch.name}-${index}`}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    exit={{ opacity: 0, scale: 0.9 }}\n                    className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 group\"\n                  >\n                    <div className=\"relative aspect-square mb-4 overflow-hidden rounded-lg\">\n                      <img\n                        src={watch.image}\n                        alt={watch.name}\n                        className=\"w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                    <h3 className=\"text-lg font-serif text-[#1F2937] mb-2\">{watch.name}</h3>\n                    <p className=\"text-[#2D8C88] font-medium mb-4\">${watch.price}</p>\n                    <div className=\"flex justify-between items-center\">\n                      <Link\n                        to={`/virtual-try-on?category=watches&product=${index}`}\n                        className=\"text-[#2D8C88] hover:text-[#F28C38] transition-colors duration-200 flex items-center\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        Try On\n                      </Link>\n                    </div>\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\n        }}\n      >\n        {/* Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\n              Find Your Perfect\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\n                Watch Today\n              </span>\n            </h2>\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\n              Try on our stunning collection of watches virtually with our cutting-edge AR technology. No downloads needed, just pure shopping magic.\n            </p>\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\n              <Link to=\"/virtual-try-on\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\n                  style={{ color: '#2D8C88' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                      />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Try It Now\n                  </span>\n                </button>\n              </Link>\n              <Link to=\"/bracelets\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\n                  style={{ borderColor: '#F28C38' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M4 6h16M4 12h16M4 18h16\"\n                      />\n                    </svg>\n                    Explore Bracelets\n                  </span>\n                </button>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default Watches;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,aAAa,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC;IAAEqB,KAAK,EAAE;EAAsC,CAAC,CAAC;;EAE5F;EACA,MAAMC,gBAAgB,GAAG,CACvB;IAAEC,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAc,CAAC,EAClC;IAAED,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAS,CAAC,EAC9B;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAW,CAAC,EAClC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,EAChC;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,CACpC;;EAED;EACAvB,SAAS,CAAC,MAAM;IACd,MAAMwB,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,WAAW,GAAG,MAAMnB,qBAAqB,CAAC,CAAC;QACjDS,kBAAkB,CAACU,WAAW,CAACC,OAAO,IAAI,EAAE,CAAC;;QAE7C;QACA,MAAMC,YAAY,GAAG,MAAMpB,aAAa,CAAC,SAAS,CAAC;QACnD,IAAIoB,YAAY,EAAE;UAChBR,YAAY,CAAC;YAAEC,KAAK,EAAEO;UAAa,CAAC,CAAC;QACvC,CAAC,MAAM,IAAIF,WAAW,CAACC,OAAO,IAAID,WAAW,CAACC,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;UAChE;UACAT,YAAY,CAAC;YAAEC,KAAK,EAAEK,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAACN;UAAM,CAAC,CAAC;QACvD;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD,CAAC,SAAS;QACRZ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,eAAe,GAAGnB,YAAY,KAAK,KAAK,GAC1CE,eAAe,GACfA,eAAe,CAACkB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,CAACC,QAAQ,CAACvB,YAAY,CAAC,CAAC;EAE5E,oBACEH,OAAA;IAAK2B,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1D5B,OAAA,CAACL,MAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVhC,OAAA;MACE2B,SAAS,EAAC,wFAAwF;MAClGM,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAN,QAAA,gBAEF5B,OAAA;QAAK2B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C5B,OAAA;UACE2B,SAAS,EAAC,8HAA8H;UACxIM,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFhC,OAAA;UACE2B,SAAS,EAAC,mIAAmI;UAC7IM,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D5B,OAAA;UAAK2B,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBACrF5B,OAAA;YAAK2B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5B,OAAA,CAACR,MAAM,CAAC2C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9Bd,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eAEjF5B,OAAA;gBAAM2B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEbhC,OAAA,CAACR,MAAM,CAACkD,EAAE;cACRN,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9Bd,SAAS,EAAC,mGAAmG;cAAAC,QAAA,gBAE7G5B,OAAA;gBAAM2B,SAAS,EAAC,QAAQ;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5ChC,OAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhC,OAAA;gBAAM2B,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,EAAC;cAEzG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACZhC,OAAA,CAACR,MAAM,CAACmD,CAAC;cACPP,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,4HAA4H;cAAAC,QAAA,EACvI;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAEXhC,OAAA,CAACR,MAAM,CAAC2C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvH5B,OAAA,CAACN,IAAI;gBACHmD,EAAE,EAAC,kCAAkC;gBACrClB,SAAS,EAAC,gPAAgP;gBAC1PM,KAAK,EAAE;kBAAEa,eAAe,EAAE;gBAAU,CAAE;gBAAAlB,QAAA,eAEtC5B,OAAA;kBAAM2B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAChD5B,OAAA;oBAAK+C,KAAK,EAAC,4BAA4B;oBAACpB,SAAS,EAAC,iFAAiF;oBAACqB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,gBACvL5B,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1KhC,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,oBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACPhC,OAAA,CAACN,IAAI;gBAACmD,EAAE,EAAC,YAAY;gBAAAjB,QAAA,eACnB5B,OAAA;kBACE2B,SAAS,EAAC,wQAAwQ;kBAClRM,KAAK,EAAE;oBAAEsB,WAAW,EAAE,SAAS;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAA5B,QAAA,eAEpD5B,OAAA;oBAAM2B,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAChD5B,OAAA;sBAAK+C,KAAK,EAAC,4BAA4B;sBAACpB,SAAS,EAAC,iFAAiF;sBAACqB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAtB,QAAA,eACvL5B,OAAA;wBAAMmD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAwB;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,kBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENhC,OAAA,CAACR,MAAM,CAAC2C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEoB,KAAK,EAAE;YAAI,CAAE;YACpClB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEoB,KAAK,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bd,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAE7B5B,OAAA;cAAK2B,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBAExD5B,OAAA;gBAAK2B,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,eAC1E5B,OAAA;kBAAK2B,SAAS,EAAC;gBAAkK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,eAGNhC,OAAA;gBAAK2B,SAAS,EAAC,+EAA+E;gBAC5FM,KAAK,EAAE;kBACLC,UAAU,EAAE,mFAAmF;kBAC/FqB,WAAW,EAAE;gBACf;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFhC,OAAA;gBACE0D,GAAG,EAAEjD,SAAS,CAACE,KAAM;gBACrBgD,GAAG,EAAC,cAAc;gBAClBhC,SAAS,EAAC,2IAA2I;gBACrJM,KAAK,EAAE;kBACL2B,SAAS,EAAE,MAAM;kBACjBC,KAAK,EAAE,MAAM;kBACbC,SAAS,EAAE,SAAS;kBACpBC,SAAS,EAAE,eAAe;kBAC1BC,kBAAkB,EAAE,QAAQ;kBAC5BC,eAAe,EAAE,eAAe;kBAChCC,wBAAwB,EAAE;gBAC5B;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFhC,OAAA;gBACE2B,SAAS,EAAC,uJAAuJ;gBACjKM,KAAK,EAAE;kBAAEa,eAAe,EAAE;gBAAyB;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhC,OAAA;MAAS2B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eAC1C5B,OAAA;QAAK2B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5B,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/B6B,WAAW,EAAE;YAAE9B,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B2B,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB1C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBAEtC5B,OAAA;YAAI2B,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3GhC,OAAA;YAAG2B,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAE/E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGbhC,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/B6B,WAAW,EAAE;YAAE9B,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CwB,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB1C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAErEhB,gBAAgB,CAAC0D,GAAG,CAAEC,QAAQ,iBAC7BvE,OAAA;YAEEwE,OAAO,EAAEA,CAAA,KAAMpE,eAAe,CAACmE,QAAQ,CAAC1D,EAAE,CAAE;YAC5Cc,SAAS,EAAC,uIAAuI;YACjJM,KAAK,EAAE;cACLa,eAAe,EAAE3C,YAAY,KAAKoE,QAAQ,CAAC1D,EAAE,GAAG,SAAS,GAAG,SAAS;cACrE2C,KAAK,EAAErD,YAAY,KAAKoE,QAAQ,CAAC1D,EAAE,GAAG,SAAS,GAAG,SAAS;cAC3D4D,MAAM,EAAE;YACV,CAAE;YAAA7C,QAAA,EAED2C,QAAQ,CAACzD;UAAI,GATTyD,QAAQ,CAAC1D,EAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAGZzB,OAAO,gBACNP,OAAA;UAAK2B,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9D5B,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5B,OAAA;cAAK2B,SAAS,EAAC;YAAyG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/HhC,OAAA;cAAG2B,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAA0C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENhC,OAAA;UAAK2B,SAAS,EAAC,+DAA+D;UAAAC,QAAA,eAC5E5B,OAAA,CAACP,eAAe;YAAAmC,QAAA,EACbN,eAAe,CAACgD,GAAG,CAAC,CAAC9C,KAAK,EAAEkD,KAAK,kBAChC1E,OAAA,CAACR,MAAM,CAAC2C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAI,CAAE;cACpClB,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAE,CAAE;cAClCkB,IAAI,EAAE;gBAAEtC,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAI,CAAE;cACjC9B,SAAS,EAAC,wFAAwF;cAAAC,QAAA,gBAElG5B,OAAA;gBAAK2B,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrE5B,OAAA;kBACE0D,GAAG,EAAElC,KAAK,CAACb,KAAM;kBACjBgD,GAAG,EAAEnC,KAAK,CAACV,IAAK;kBAChBa,SAAS,EAAC;gBAAgG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhC,OAAA;gBAAI2B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAEJ,KAAK,CAACV;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxEhC,OAAA;gBAAG2B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAC,GAAC,EAACJ,KAAK,CAACoD,KAAK;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEhC,OAAA;gBAAK2B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD5B,OAAA,CAACN,IAAI;kBACHmD,EAAE,EAAE,4CAA4C6B,KAAK,EAAG;kBACxD/C,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,gBAEhG5B,OAAA;oBAAK+C,KAAK,EAAC,4BAA4B;oBAACpB,SAAS,EAAC,cAAc;oBAACqB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,gBACpH5B,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1KhC,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,UAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA1BD,GAAGR,KAAK,CAACV,IAAI,IAAI4D,KAAK,EAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BnB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhC,OAAA;MACE2B,SAAS,EAAC,oDAAoD;MAC9DM,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAN,QAAA,gBAGF5B,OAAA;QAAK2B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C5B,OAAA;UAAK2B,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7D5B,OAAA;YAAK2B,SAAS,EAAC;UAAiF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvGhC,OAAA;YAAK2B,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE5B,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/B6B,WAAW,EAAE;YAAE9B,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B2B,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAzC,QAAA,gBAEzB5B,OAAA;YAAI2B,SAAS,EAAC,2EAA2E;YAAAC,QAAA,GAAC,mBAExF,eAAA5B,OAAA;cAAM2B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAAC;YAEpG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLhC,OAAA;YAAG2B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJhC,OAAA;YAAK2B,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3F5B,OAAA,CAACN,IAAI;cAACmD,EAAE,EAAC,iBAAiB;cAAAjB,QAAA,eACxB5B,OAAA;gBACE2B,SAAS,EAAC,uMAAuM;gBACjNM,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAU,CAAE;gBAAA5B,QAAA,gBAE5B5B,OAAA;kBAAM2B,SAAS,EAAC;gBAA6J;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrLhC,OAAA;kBAAM2B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1C5B,OAAA;oBACE+C,KAAK,EAAC,4BAA4B;oBAClCpB,SAAS,EAAC,cAAc;oBACxBqB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,gBAErB5B,OAAA;sBACEmD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACFhC,OAAA;sBAAMmD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,cAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPhC,OAAA,CAACN,IAAI;cAACmD,EAAE,EAAC,YAAY;cAAAjB,QAAA,eACnB5B,OAAA;gBACE2B,SAAS,EAAC,uMAAuM;gBACjNM,KAAK,EAAE;kBAAEsB,WAAW,EAAE;gBAAU,CAAE;gBAAA3B,QAAA,gBAElC5B,OAAA;kBAAM2B,SAAS,EAAC;gBAA6H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJhC,OAAA;kBAAM2B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1C5B,OAAA;oBACE+C,KAAK,EAAC,4BAA4B;oBAClCpB,SAAS,EAAC,cAAc;oBACxBqB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,eAErB5B,OAAA;sBACEmD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,qBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVhC,OAAA,CAACJ,MAAM;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAzWID,OAAO;AAAA4E,EAAA,GAAP5E,OAAO;AA2Wb,eAAeA,OAAO;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}