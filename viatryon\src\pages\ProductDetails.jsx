import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { getProductCollections } from '../data/productCollections';

const ProductDetails = () => {
  const { category, id } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const loadProductData = async () => {
      try {
        // Load product collections with processed images
        const collections = await getProductCollections();

        if (category && id) {
          // Find the product in the collection
          const productId = parseInt(id);
          const foundProduct = collections[category]?.find(p => p.id === productId);

          if (foundProduct) {
            setProduct(foundProduct);
          } else {
            // Handle product not found
            console.error('Product not found');
          }
        }
      } catch (error) {
        console.error('Error loading product data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProductData();
  }, [category, id]);

  // Navigate to virtual try-on with this product
  const handleTryOn = () => {
    navigate(`/virtual-try-on?category=${category}&productId=${product.id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#F9FAFB] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4"></div>
          <p className="text-[#1F2937] font-sans">Loading product with background removal...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-[#F9FAFB]">
        <Navbar />
        <div className="container mx-auto px-6 pt-36 pb-24 text-center">
          <h1 className="text-3xl font-serif text-[#1F2937] mb-6">Product Not Found</h1>
          <p className="text-[#1F2937] mb-8 font-sans">
            Sorry, we couldn't find the product you're looking for.
          </p>
          <Link
            to={`/${category}`}
            className="inline-block bg-white border px-8 py-3 rounded-full transition font-sans font-medium"
            style={{ borderColor: '#2D8C88', color: '#2D8C88' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2D8C88';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'white';
              e.currentTarget.style.color = '#2D8C88';
            }}
          >
            Back to {category}
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F9FAFB]">
      <Navbar />

      {/* Hero Section with Breadcrumb */}
      <section className="pt-20 sm:pt-36 pb-4 bg-white shadow-sm">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex items-center text-xs sm:text-sm font-sans text-gray-500 overflow-x-auto">
            <Link to="/" className="hover:text-[#2D8C88] whitespace-nowrap">Home</Link>
            <span className="mx-1 sm:mx-2">/</span>
            <Link to={`/${category}`} className="hover:text-[#2D8C88] capitalize whitespace-nowrap">{category}</Link>
            <span className="mx-1 sm:mx-2">/</span>
            <span className="text-[#1F2937] truncate">{product.name}</span>
          </div>
        </div>
      </section>

      {/* Product Details */}
      <section className="py-6 sm:py-12">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-12">
            {/* Product Images */}
            <div>
              <div className="bg-white rounded-xl shadow-md p-4 sm:p-8 mb-6 transition-all duration-300 hover:shadow-lg">
                <div className="relative aspect-square bg-[#F9FAFB] rounded-lg flex items-center justify-center mb-4 sm:mb-6 overflow-hidden group">
                  <img
                    src={product.images[currentImageIndex]}
                    alt={product.name}
                    className="max-h-full max-w-full object-contain transition-transform duration-500 group-hover:scale-105"
                  />

                  {/* Image navigation arrows */}
                  <button
                    onClick={() => setCurrentImageIndex(prev => (prev === 0 ? product.images.length - 1 : prev - 1))}
                    className="absolute left-4 bg-white bg-opacity-80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    aria-label="Previous image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#1F2937]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  <button
                    onClick={() => setCurrentImageIndex(prev => (prev === product.images.length - 1 ? 0 : prev + 1))}
                    className="absolute right-4 bg-white bg-opacity-80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    aria-label="Next image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#1F2937]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>

                <div className="flex space-x-2 sm:space-x-4 justify-center overflow-x-auto pb-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-16 h-16 sm:w-20 sm:h-20 rounded-md overflow-hidden border-2 transition-all hover:shadow-md flex-shrink-0 ${
                        currentImageIndex === index ? 'border-[#2D8C88] scale-105' : 'border-transparent'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${product.name} view ${index + 1}`}
                        className="w-full h-full object-contain"
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Additional product info card */}
              <div className="bg-white rounded-xl shadow-md p-8 transition-all duration-300 hover:shadow-lg">
                <h2 className="text-xl font-serif text-[#1F2937] mb-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#F28C38]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Why Choose This {category === 'watches' ? 'Watch' : 'Bracelet'}
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#2D8C88]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-sans font-medium text-[#1F2937]">Premium Quality</h3>
                      <p className="text-sm text-gray-600 font-sans">Crafted with the finest materials for durability and elegance.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#2D8C88]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-sans font-medium text-[#1F2937]">Timeless Design</h3>
                      <p className="text-sm text-gray-600 font-sans">A classic style that complements any outfit and occasion.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-[#2D8C88] bg-opacity-10 rounded-full p-2 mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#2D8C88]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-sans font-medium text-[#1F2937]">Comfort Guaranteed</h3>
                      <p className="text-sm text-gray-600 font-sans">Designed for all-day wear with maximum comfort.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div>
              <div className="bg-white rounded-xl shadow-md p-8 mb-6 transition-all duration-300 hover:shadow-lg">
                {/* Product title and price */}
                <div className="border-b border-gray-100 pb-6 mb-6">
                  <div className="flex items-start justify-between">
                    <h1 className="text-3xl font-serif text-[#1F2937] mb-2">{product.name}</h1>
                    <div className="bg-[#F28C38] bg-opacity-10 rounded-full px-3 py-1 text-sm font-sans font-medium" style={{ color: '#F28C38' }}>
                      Best Seller
                    </div>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center mb-6">
                  <div className="flex text-[#F28C38]">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg key={star} xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-gray-600 font-sans">4.9 (120 reviews)</span>
                </div>

                {/* Description */}
                <div className="mb-8">
                  <h2 className="text-xl font-serif text-[#1F2937] mb-3">Description</h2>
                  <p className="text-[#1F2937] font-sans leading-relaxed">
                    {product.description}
                  </p>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  <h2 className="text-xl font-serif text-[#1F2937] mb-3">Features</h2>
                  <ul className="space-y-3 font-sans text-[#1F2937]">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-[#2D8C88] flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action buttons */}
                <div className="flex flex-col space-y-4">
                  <button
                    onClick={handleTryOn}
                    className="w-full bg-[#2D8C88] text-white text-lg flex items-center justify-center px-8 py-5 rounded-full transition font-sans font-medium hover:bg-[#236e6a] shadow-lg hover:shadow-xl group"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Try On Virtually
                  </button>
                </div>
              </div>

              {/* Specifications */}
              <div className="bg-white rounded-xl shadow-md p-8 transition-all duration-300 hover:shadow-lg">
                <h2 className="text-xl font-serif text-[#1F2937] mb-6 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#2D8C88]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Specifications
                </h2>
                <div className="space-y-3">
                  {Object.entries(product.specifications).map(([key, value], index) => (
                    <div key={index} className="flex justify-between py-3 border-b border-gray-100">
                      <span className="font-sans text-gray-600">{key}</span>
                      <span className="font-sans font-medium text-[#1F2937]">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ProductDetails;
