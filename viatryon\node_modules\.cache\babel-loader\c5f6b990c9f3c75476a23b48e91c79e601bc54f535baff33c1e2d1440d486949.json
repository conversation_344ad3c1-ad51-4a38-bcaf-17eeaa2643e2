{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\client\\\\analytics\\\\Overview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';\nimport { Eye, Users, TrendingUp, Clock, User } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Overview = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [overviewData, setOverviewData] = useState(null);\n  const [timeRange, setTimeRange] = useState('7d');\n  useEffect(() => {\n    const fetchOverviewData = async () => {\n      try {\n        var _timeData$dailyTrends, _timeData$dailyTrends2, _timeData$dailyTrends3;\n        setLoading(true);\n        setError(null);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n\n        // Fetch data from multiple endpoints\n        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }), fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }), fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })]);\n        if (!timeAnalysisResponse.ok || !productPerformanceResponse.ok || !deviceStatsResponse.ok) {\n          throw new Error('Failed to fetch analytics data');\n        }\n        const timeData = await timeAnalysisResponse.json();\n        const productData = await productPerformanceResponse.json();\n        const deviceData = await deviceStatsResponse.json();\n\n        // Calculate total sessions and interactions from product data\n        const totalSessions = productData.reduce((sum, product) => sum + (product.sessions || 0), 0);\n        const totalInteractions = productData.reduce((sum, product) => sum + (product.totalInteractions || 0), 0);\n\n        // Calculate average duration from time analysis data\n        const avgDuration = ((_timeData$dailyTrends = timeData.dailyTrends) === null || _timeData$dailyTrends === void 0 ? void 0 : _timeData$dailyTrends.reduce((sum, day) => sum + (day.avgDuration || 0), 0)) / (((_timeData$dailyTrends2 = timeData.dailyTrends) === null || _timeData$dailyTrends2 === void 0 ? void 0 : _timeData$dailyTrends2.length) || 1);\n\n        // Calculate unique users from device stats\n        const uniqueUsers = deviceData.reduce((sum, device) => sum + (device.sessions || 0), 0);\n\n        // Format the data for the overview\n        const formattedData = {\n          totalSessions,\n          activeClients: uniqueUsers,\n          avgSessionDuration: Math.round(avgDuration),\n          totalInteractions,\n          trends: ((_timeData$dailyTrends3 = timeData.dailyTrends) === null || _timeData$dailyTrends3 === void 0 ? void 0 : _timeData$dailyTrends3.map(trend => ({\n            _id: trend._id,\n            sessions: trend.sessions,\n            avgDuration: Math.round(trend.avgDuration)\n          }))) || []\n        };\n        setOverviewData(formattedData);\n      } catch (err) {\n        console.error('Error fetching overview data:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchOverviewData();\n  }, [timeRange]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-red-800\",\n            children: \"Error loading overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-red-700\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Format duration from seconds to readable format\n  const formatDuration = seconds => {\n    if (!seconds) return '0s';\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.round(seconds % 60);\n    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;\n  };\n\n  // Format numbers with commas\n  const formatNumber = num => {\n    if (!num) return '0';\n    return num.toLocaleString();\n  };\n  const metrics = [{\n    title: 'Total Sessions',\n    value: overviewData === null || overviewData === void 0 ? void 0 : overviewData.totalSessions,\n    icon: /*#__PURE__*/_jsxDEV(Users, {\n      className: \"h-6 w-6 text-blue-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: 'Avg. Duration',\n    value: formatDuration(overviewData === null || overviewData === void 0 ? void 0 : overviewData.avgSessionDuration),\n    icon: /*#__PURE__*/_jsxDEV(Clock, {\n      className: \"h-6 w-6 text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: 'Unique Users',\n    value: overviewData === null || overviewData === void 0 ? void 0 : overviewData.activeClients,\n    icon: /*#__PURE__*/_jsxDEV(User, {\n      className: \"h-6 w-6 text-purple-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setTimeRange(range),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: range\n        }, range, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n      children: metrics.map((metric, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: metric.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900 mt-1\",\n              children: formatNumber(metric.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n            children: metric.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Session Trends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: overviewData === null || overviewData === void 0 ? void 0 : overviewData.trends,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"_id\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"sessions\",\n                stroke: \"#2D8C88\",\n                strokeWidth: 2,\n                dot: {\n                  fill: '#2D8C88'\n                },\n                name: \"Total Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.5\n        },\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Session Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: overviewData === null || overviewData === void 0 ? void 0 : overviewData.trends,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"_id\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"sessions\",\n                fill: \"#2D8C88\",\n                name: \"Total Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(Overview, \"mseylJT8BRtjh6U6VbUhnJyEBA4=\");\n_c = Overview;\nexport default Overview;\nvar _c;\n$RefreshReg$(_c, \"Overview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "Line<PERSON>hart", "Line", "Eye", "Users", "TrendingUp", "Clock", "User", "jsxDEV", "_jsxDEV", "Overview", "_s", "loading", "setLoading", "error", "setError", "overviewData", "setOverviewData", "timeRange", "setTimeRange", "fetchOverviewData", "_timeData$dailyTrends", "_timeData$dailyTrends2", "_timeData$dailyTrends3", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "end", "Date", "start", "setDate", "getDate", "setFullYear", "getFullYear", "timeAnalysisResponse", "productPerformanceResponse", "deviceStatsResponse", "Promise", "all", "fetch", "toISOString", "headers", "ok", "timeData", "json", "productData", "deviceData", "totalSessions", "reduce", "sum", "product", "sessions", "totalInteractions", "avgDuration", "dailyTrends", "day", "length", "uniqueUsers", "device", "formattedData", "activeClients", "avgSessionDuration", "Math", "round", "trends", "map", "trend", "_id", "err", "console", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDuration", "seconds", "minutes", "floor", "remainingSeconds", "formatNumber", "num", "toLocaleString", "metrics", "title", "value", "icon", "range", "onClick", "metric", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "name", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/client/analytics/Overview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  LineChart,\n  Line\n} from 'recharts';\nimport { Eye, Users, TrendingUp, Clock, User } from 'lucide-react';\n\nconst Overview = () => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [overviewData, setOverviewData] = useState(null);\n  const [timeRange, setTimeRange] = useState('7d');\n\n  useEffect(() => {\n    const fetchOverviewData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n\n        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n        \n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n\n        // Fetch data from multiple endpoints\n        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([\n          fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          }),\n          fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          }),\n          fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          })\n        ]);\n\n        if (!timeAnalysisResponse.ok || !productPerformanceResponse.ok || !deviceStatsResponse.ok) {\n          throw new Error('Failed to fetch analytics data');\n        }\n\n        const timeData = await timeAnalysisResponse.json();\n        const productData = await productPerformanceResponse.json();\n        const deviceData = await deviceStatsResponse.json();\n\n        // Calculate total sessions and interactions from product data\n        const totalSessions = productData.reduce((sum, product) => sum + (product.sessions || 0), 0);\n        const totalInteractions = productData.reduce((sum, product) => sum + (product.totalInteractions || 0), 0);\n\n        // Calculate average duration from time analysis data\n        const avgDuration = timeData.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / \n          (timeData.dailyTrends?.length || 1);\n\n        // Calculate unique users from device stats\n        const uniqueUsers = deviceData.reduce((sum, device) => sum + (device.sessions || 0), 0);\n\n        // Format the data for the overview\n        const formattedData = {\n          totalSessions,\n          activeClients: uniqueUsers,\n          avgSessionDuration: Math.round(avgDuration),\n          totalInteractions,\n          trends: timeData.dailyTrends?.map(trend => ({\n            _id: trend._id,\n            sessions: trend.sessions,\n            avgDuration: Math.round(trend.avgDuration)\n          })) || []\n        };\n\n        setOverviewData(formattedData);\n      } catch (err) {\n        console.error('Error fetching overview data:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOverviewData();\n  }, [timeRange]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div className=\"flex\">\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium text-red-800\">Error loading overview</h3>\n            <div className=\"mt-2 text-sm text-red-700\">\n              <p>{error}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Format duration from seconds to readable format\n  const formatDuration = (seconds) => {\n    if (!seconds) return '0s';\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.round(seconds % 60);\n    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;\n  };\n\n  // Format numbers with commas\n  const formatNumber = (num) => {\n    if (!num) return '0';\n    return num.toLocaleString();\n  };\n\n  const metrics = [\n    {\n      title: 'Total Sessions',\n      value: overviewData?.totalSessions,\n      icon: <Users className=\"h-6 w-6 text-blue-500\" />\n    },\n    {\n      title: 'Avg. Duration',\n      value: formatDuration(overviewData?.avgSessionDuration),\n      icon: <Clock className=\"h-6 w-6 text-green-500\" />\n    },\n    {\n      title: 'Unique Users',\n      value: overviewData?.activeClients,\n      icon: <User className=\"h-6 w-6 text-purple-500\" />\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Time Range Selector */}\n      <div className=\"flex justify-end\">\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {['7d', '30d', '90d', '1y'].map((range) => (\n            <button\n              key={range}\n              onClick={() => setTimeRange(range)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                timeRange === range\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {range}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {metrics.map((metric, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-xl shadow-sm p-6\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{metric.title}</p>\n                <p className=\"text-2xl font-semibold text-gray-900 mt-1\">\n                  {formatNumber(metric.value)}\n                </p>\n              </div>\n              <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                {metric.icon}\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Session Trends - Line Chart */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"bg-white rounded-xl shadow-sm p-6\"\n        >\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Session Trends</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart data={overviewData?.trends}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"_id\" />\n                <YAxis />\n                <Tooltip />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"sessions\"\n                  stroke=\"#2D8C88\"\n                  strokeWidth={2}\n                  dot={{ fill: '#2D8C88' }}\n                  name=\"Total Sessions\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </motion.div>\n\n        {/* Session Trends - Bar Chart */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"bg-white rounded-xl shadow-sm p-6\"\n        >\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Session Distribution</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <BarChart data={overviewData?.trends}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"_id\" />\n                <YAxis />\n                <Tooltip />\n                <Bar\n                  dataKey=\"sessions\"\n                  fill=\"#2D8C88\"\n                  name=\"Total Sessions\"\n                />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default Overview; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,mBAAmB,EACnBC,SAAS,EACTC,IAAI,QACC,UAAU;AACjB,SAASC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACFV,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;QAClD;QAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;QAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;QAErE;QACA,MAAMO,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,IAAIC,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,QAAQlB,SAAS;UACf,KAAK,IAAI;YACPmB,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,IAAI;YACPF,KAAK,CAACG,WAAW,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C;UACF;YACEJ,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC;;QAEA;QACA,MAAM,CAACG,oBAAoB,EAAEC,0BAA0B,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChGC,KAAK,CAAC,GAAGf,MAAM,6CAA6CK,KAAK,CAACW,WAAW,CAAC,CAAC,QAAQb,GAAG,CAACa,WAAW,CAAC,CAAC,EAAE,EAAE;UAC1GC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUzB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,EACFuB,KAAK,CAAC,GAAGf,MAAM,mDAAmDK,KAAK,CAACW,WAAW,CAAC,CAAC,QAAQb,GAAG,CAACa,WAAW,CAAC,CAAC,EAAE,EAAE;UAChHC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUzB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,EACFuB,KAAK,CAAC,GAAGf,MAAM,4CAA4CK,KAAK,CAACW,WAAW,CAAC,CAAC,QAAQb,GAAG,CAACa,WAAW,CAAC,CAAC,EAAE,EAAE;UACzGC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUzB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,CACH,CAAC;QAEF,IAAI,CAACkB,oBAAoB,CAACQ,EAAE,IAAI,CAACP,0BAA0B,CAACO,EAAE,IAAI,CAACN,mBAAmB,CAACM,EAAE,EAAE;UACzF,MAAM,IAAIvB,KAAK,CAAC,gCAAgC,CAAC;QACnD;QAEA,MAAMwB,QAAQ,GAAG,MAAMT,oBAAoB,CAACU,IAAI,CAAC,CAAC;QAClD,MAAMC,WAAW,GAAG,MAAMV,0BAA0B,CAACS,IAAI,CAAC,CAAC;QAC3D,MAAME,UAAU,GAAG,MAAMV,mBAAmB,CAACQ,IAAI,CAAC,CAAC;;QAEnD;QACA,MAAMG,aAAa,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5F,MAAMC,iBAAiB,GAAGP,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACE,iBAAiB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;QAEzG;QACA,MAAMC,WAAW,GAAG,EAAAxC,qBAAA,GAAA8B,QAAQ,CAACW,WAAW,cAAAzC,qBAAA,uBAApBA,qBAAA,CAAsBmC,MAAM,CAAC,CAACC,GAAG,EAAEM,GAAG,KAAKN,GAAG,IAAIM,GAAG,CAACF,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAC5F,EAAAvC,sBAAA,GAAA6B,QAAQ,CAACW,WAAW,cAAAxC,sBAAA,uBAApBA,sBAAA,CAAsB0C,MAAM,KAAI,CAAC,CAAC;;QAErC;QACA,MAAMC,WAAW,GAAGX,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAES,MAAM,KAAKT,GAAG,IAAIS,MAAM,CAACP,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;QAEvF;QACA,MAAMQ,aAAa,GAAG;UACpBZ,aAAa;UACba,aAAa,EAAEH,WAAW;UAC1BI,kBAAkB,EAAEC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;UAC3CD,iBAAiB;UACjBY,MAAM,EAAE,EAAAjD,sBAAA,GAAA4B,QAAQ,CAACW,WAAW,cAAAvC,sBAAA,uBAApBA,sBAAA,CAAsBkD,GAAG,CAACC,KAAK,KAAK;YAC1CC,GAAG,EAAED,KAAK,CAACC,GAAG;YACdhB,QAAQ,EAAEe,KAAK,CAACf,QAAQ;YACxBE,WAAW,EAAES,IAAI,CAACC,KAAK,CAACG,KAAK,CAACb,WAAW;UAC3C,CAAC,CAAC,CAAC,KAAI;QACT,CAAC;QAED5C,eAAe,CAACkD,aAAa,CAAC;MAChC,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZC,OAAO,CAAC/D,KAAK,CAAC,+BAA+B,EAAE8D,GAAG,CAAC;QACnD7D,QAAQ,CAAC6D,GAAG,CAACE,OAAO,CAAC;MACvB,CAAC,SAAS;QACRjE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,IAAIN,OAAO,EAAE;IACX,oBACEH,OAAA;MAAKsE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDvE,OAAA;QAAKsE,SAAS,EAAC;MAAiE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;EAEA,IAAItE,KAAK,EAAE;IACT,oBACEL,OAAA;MAAKsE,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7DvE,OAAA;QAAKsE,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBvE,OAAA;UAAKsE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBvE,OAAA;YAAIsE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5E3E,OAAA;YAAKsE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxCvE,OAAA;cAAAuE,QAAA,EAAIlE;YAAK;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,MAAMC,OAAO,GAAGjB,IAAI,CAACkB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGnB,IAAI,CAACC,KAAK,CAACe,OAAO,GAAG,EAAE,CAAC;IACjD,OAAOC,OAAO,GAAG,CAAC,GAAG,GAAGA,OAAO,KAAKE,gBAAgB,GAAG,GAAG,GAAGA,gBAAgB,GAAG;EAClF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,GAAG,IAAK;IAC5B,IAAI,CAACA,GAAG,EAAE,OAAO,GAAG;IACpB,OAAOA,GAAG,CAACC,cAAc,CAAC,CAAC;EAC7B,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE/E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuC,aAAa;IAClCyC,IAAI,eAAEvF,OAAA,CAACL,KAAK;MAAC2E,SAAS,EAAC;IAAuB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAClD,CAAC,EACD;IACEU,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAEV,cAAc,CAACrE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqD,kBAAkB,CAAC;IACvD2B,IAAI,eAAEvF,OAAA,CAACH,KAAK;MAACyE,SAAS,EAAC;IAAwB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnD,CAAC,EACD;IACEU,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE/E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoD,aAAa;IAClC4B,IAAI,eAAEvF,OAAA,CAACF,IAAI;MAACwE,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnD,CAAC,CACF;EAED,oBACE3E,OAAA;IAAKsE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvE,OAAA;MAAKsE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BvE,OAAA;QAAKsE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACP,GAAG,CAAEwB,KAAK,iBACpCxF,OAAA;UAEEyF,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC8E,KAAK,CAAE;UACnClB,SAAS,EAAE,4CACT7D,SAAS,KAAK+E,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;UAAAjB,QAAA,EAEFiB;QAAK,GARDA,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEa,OAAO,CAACpB,GAAG,CAAC,CAAC0B,MAAM,EAAEC,KAAK,kBACzB3F,OAAA,CAAChB,MAAM,CAAC4G,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BzB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAE7CvE,OAAA;UAAKsE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvE,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAGsE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEmB,MAAM,CAACL;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE3E,OAAA;cAAGsE,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EACrDU,YAAY,CAACS,MAAM,CAACJ,KAAK;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN3E,OAAA;YAAKsE,SAAS,EAAC,yEAAyE;YAAAC,QAAA,EACrFmB,MAAM,CAACH;UAAI;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAfDgB,KAAK;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBA,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDvE,OAAA,CAAChB,MAAM,CAAC4G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3B5B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAE7CvE,OAAA;UAAIsE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E3E,OAAA;UAAKsE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBvE,OAAA,CAACT,mBAAmB;YAAC4G,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAA7B,QAAA,eAC7CvE,OAAA,CAACR,SAAS;cAAC6G,IAAI,EAAE9F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,MAAO;cAAAQ,QAAA,gBACpCvE,OAAA,CAACX,aAAa;gBAACiH,eAAe,EAAC;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC3E,OAAA,CAACb,KAAK;gBAACoH,OAAO,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvB3E,OAAA,CAACZ,KAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT3E,OAAA,CAACV,OAAO;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX3E,OAAA,CAACP,IAAI;gBACH+G,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,UAAU;gBAClBE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;kBAAEC,IAAI,EAAE;gBAAU,CAAE;gBACzBC,IAAI,EAAC;cAAgB;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb3E,OAAA,CAAChB,MAAM,CAAC4G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3B5B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAE7CvE,OAAA;UAAIsE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF3E,OAAA;UAAKsE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBvE,OAAA,CAACT,mBAAmB;YAAC4G,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAA7B,QAAA,eAC7CvE,OAAA,CAACf,QAAQ;cAACoH,IAAI,EAAE9F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,MAAO;cAAAQ,QAAA,gBACnCvE,OAAA,CAACX,aAAa;gBAACiH,eAAe,EAAC;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC3E,OAAA,CAACb,KAAK;gBAACoH,OAAO,EAAC;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvB3E,OAAA,CAACZ,KAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT3E,OAAA,CAACV,OAAO;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX3E,OAAA,CAACd,GAAG;gBACFqH,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,SAAS;gBACdC,IAAI,EAAC;cAAgB;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CAvQID,QAAQ;AAAA6G,EAAA,GAAR7G,QAAQ;AAyQd,eAAeA,QAAQ;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}