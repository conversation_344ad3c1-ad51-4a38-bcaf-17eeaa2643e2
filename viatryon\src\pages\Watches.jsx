import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { getProductCollections } from '../data/productCollections';
import { loadHeroImage } from '../utils/imageLoader';

const Watches = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [watchCollection, setWatchCollection] = useState([]);
  const [loading, setLoading] = useState(true);
  const [heroWatch, setHeroWatch] = useState({ image: '/imgs/watches_hero/watch_1_hero.png' });

  // Filter categories
  const filterCategories = [
    { id: 'all', name: 'All Watches' },
    { id: 'mens', name: 'Men\'s' },
    { id: 'womens', name: 'Women\'s' },
    { id: 'luxury', name: 'Luxury' },
    { id: 'new', name: 'New Arrivals' }
  ];

  // Load watch collection with processed images
  useEffect(() => {
    const loadWatches = async () => {
      try {
        const collections = await getProductCollections();
        setWatchCollection(collections.watches || []);

        // Load hero image (pre-processed without background)
        const heroImageUrl = await loadHeroImage('watches');
        if (heroImageUrl) {
          setHeroWatch({ image: heroImageUrl });
        } else if (collections.watches && collections.watches.length > 0) {
          // Fallback to first collection image
          setHeroWatch({ image: collections.watches[0].image });
        }
      } catch (error) {
        console.error('Error loading watch collection:', error);
      } finally {
        setLoading(false);
      }
    };

    loadWatches();
  }, []);

  // Filter watches based on active filter
  const filteredWatches = activeFilter === 'all'
    ? watchCollection
    : watchCollection.filter(watch => watch.categories.includes(activeFilter));

  return (
    <div className="min-h-screen bg-[#F9FAFB] overflow-x-hidden">
      <Navbar />

      {/* Hero Section */}
      <section
        className="relative min-h-[100vh] pt-24 pb-16 md:pt-32 md:pb-24 lg:pt-40 lg:pb-32 overflow-hidden"
        style={{
          background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`,
        }}
      >
        <div className="absolute inset-0 overflow-hidden">
          <div
            className="absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]"
            style={{
              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,
            }}
          />
          <div
            className="absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]"
            style={{
              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,
            }}
          />
        </div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16">
            <div className="lg:w-1/2 text-center lg:text-left">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm mb-6"
              >
                <span className="text-sm font-medium text-[#2D8C88]">Experience Luxury</span>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-serif text-[#1F2937] mb-4 md:mb-6 leading-tight"
              >
                <span className="italic">Discover Our</span>
                <br />
                <span className="font-medium bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent">
                  Watch Collection
                </span>
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-base md:text-lg lg:text-xl text-[#1F2937] mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans font-light leading-relaxed"
              >
                Explore our curated selection of timepieces, from classic elegance to modern innovation, all available for virtual try-on.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 justify-center lg:justify-start mb-8 md:mb-12"
              >
                <Link
                  to="/virtual-try-on?category=watches"
                  className="relative text-white px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center group"
                  style={{ backgroundColor: '#2D8C88' }}
                >
                  <span className="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Try On Virtually
                  </span>
                </Link>
                <Link to="/bracelets">
                  <button
                    className="relative bg-white border-2 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-3.5 rounded-full font-sans font-medium text-sm sm:text-base md:text-lg shadow-md hover:shadow-lg transition-all duration-200 min-h-[44px] flex items-center justify-center w-full sm:w-auto group"
                    style={{ borderColor: '#2D8C88', color: '#2D8C88' }}
                  >
                    <span className="flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-2 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                      </svg>
                      View Bracelets
                    </span>
                  </button>
                </Link>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              className="lg:w-1/2 relative"
            >
              <div className="relative flex items-center justify-center">
                {/* Mobile-specific background */}
                <div className="lg:hidden absolute inset-0 flex items-center justify-center">
                  <div className="w-[18rem] h-[18rem] sm:w-[22rem] sm:h-[22rem] rounded-full bg-gradient-to-br from-[#2D8C88]/10 to-[#F28C38]/10 shadow-lg border border-white/20 backdrop-blur-sm"></div>
                </div>
                
                {/* Desktop background */}
                <div className="hidden lg:block absolute w-[44rem] h-[44rem] rounded-full shadow-inner border"
                  style={{
                    background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,
                    borderColor: 'rgba(242, 140, 56, 0.2)',
                  }}
                />
                <img
                  src={heroWatch.image}
                  alt="Luxury Watch"
                  className="h-[16rem] sm:h-[20rem] md:h-[28rem] lg:h-[36rem] w-auto object-contain drop-shadow-[0_8px_25px_rgba(0,0,0,0.15)] max-w-full transform-gpu"
                  style={{
                    maxHeight: '100%',
                    width: 'auto',
                    objectFit: 'contain',
                    transform: 'translateZ(0)',
                    backfaceVisibility: 'hidden',
                    WebkitTransform: 'translateZ(0)',
                    WebkitBackfaceVisibility: 'hidden'
                  }}
                />
                <div
                  className="absolute -bottom-8 sm:-bottom-12 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-2/3 sm:w-3/4 h-6 sm:h-8 md:h-12 rounded-full blur-xl md:blur-2xl"
                  style={{ backgroundColor: 'rgba(31, 41, 55, 0.15)' }}
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Filterable Gallery Section */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12 md:mb-16"
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-serif text-[#1F2937] mb-4">Explore Our Timepieces</h2>
            <p className="text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base">
              Browse our collection of premium watches, each crafted with precision and available for virtual try-on.
            </p>
          </motion.div>

          {/* Filter Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-wrap justify-center gap-2 md:gap-4 mb-8 md:mb-12"
          >
            {filterCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className="px-4 md:px-8 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-full transition-all flex items-center min-h-[44px] group"
                style={{
                  backgroundColor: activeFilter === category.id ? '#2D8C88' : '#FFFFFF',
                  color: activeFilter === category.id ? '#FFFFFF' : '#1F2937',
                  border: '1px solid #E5E7EB'
                }}
              >
                {category.name}
              </button>
            ))}
          </motion.div>

          {/* Product Grid */}
          {loading ? (
            <div className="flex items-center justify-center py-16 md:py-20">
              <div className="text-center">
                <div className="animate-spin rounded-full h-10 md:h-12 w-10 md:w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4"></div>
                <p className="text-[#1F2937] font-sans text-sm md:text-base">Loading watches with background removal...</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
              <AnimatePresence>
                {filteredWatches.map((watch, index) => (
                  <motion.div
                    key={`${watch.name}-${index}`}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 group"
                  >
                    <div className="relative aspect-square mb-4 overflow-hidden rounded-lg">
                      <img
                        src={watch.image}
                        alt={watch.name}
                        className="w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <h3 className="text-lg font-serif text-[#1F2937] mb-4">{watch.name}</h3>
                    <div className="flex justify-center items-center">
                      <Link
                        to={`/virtual-try-on?category=watches&product=${index}`}
                        className="w-full bg-[#2D8C88] text-white text-sm flex items-center justify-center px-6 py-3 rounded-full transition font-sans font-medium hover:bg-[#236e6a] shadow-md hover:shadow-lg group"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Try On Virtually
                      </Link>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section
        className="py-20 md:py-32 text-white relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,
        }}
      >
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-10">
            <div className="absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white"></div>
            <div className="absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 md:px-6 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight">
              Find Your Perfect
              <span className="block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent">
                Watch Today
              </span>
            </h2>
            <p className="text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans">
              Try on our stunning collection of watches virtually with our cutting-edge AR technology. No downloads needed, just pure shopping magic.
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link to="/virtual-try-on">
                <button
                  className="group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
                  style={{ color: '#2D8C88' }}
                >
                  <span className="absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full"></span>
                  <span className="relative flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 mr-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                      />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Try It Now
                  </span>
                </button>
              </Link>
              <Link to="/bracelets">
                <button
                  className="group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center"
                  style={{ borderColor: '#F28C38' }}
                >
                  <span className="absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full"></span>
                  <span className="relative flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 mr-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                    Explore Bracelets
                  </span>
                </button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Watches;
