import React, { useState, useEffect } from 'react';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminNavbar from '../../components/admin/AdminNavbar';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code,
  X, Copy, Check, BarChart3, Clock, Smartphone, Monitor, Activity,
  Calendar, Target, Zap, MapPin, ChevronRight
} from 'lucide-react';

function generatePassword() {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

const Clients = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [editingClient, setEditingClient] = useState(null);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    newClientsThisMonth: 0,
    activeRate: 0,
    tryOnsGrowth: 0,
    uniqueUsers: 0
  });
  const [uniqueUsersData, setUniqueUsersData] = useState(null);
  const [showDetailsPopup, setShowDetailsPopup] = useState(false);
  const [showCodePopup, setShowCodePopup] = useState(false);
  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);
  const [selectedClientForCode, setSelectedClientForCode] = useState(null);
  const [clientAnalytics, setClientAnalytics] = useState(null);
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);
  const [clientForm, setClientForm] = useState({
    companyName: '',
    contactName: '',
    website: '',
    email: '',
    password: '',
    phone: '',
    industry: '',
    productType: 'watches',
    subscriptionPlan: 'basic'
  });

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  // Fetch clients from backend
  useEffect(() => {
    fetchClients();
  }, [searchQuery, selectedStatus]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedStatus !== 'all') params.append('status', selectedStatus);

      // Fetch clients and unique users data in parallel
      const [clientsResponse, uniqueUsersResponse] = await Promise.all([
        fetch(`${apiUrl}/api/clients?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/admin/unique-users`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (!clientsResponse.ok) {
        const errorData = await clientsResponse.json();
        throw new Error(errorData.message || 'Failed to fetch clients');
      }

      const clientsData = await clientsResponse.json();
      setClients(clientsData.clients || []);

      // Handle unique users data
      let uniqueUsersCount = 0;
      if (uniqueUsersResponse.ok) {
        const uniqueUsersData = await uniqueUsersResponse.json();
        setUniqueUsersData(uniqueUsersData);
        uniqueUsersCount = uniqueUsersData.summary?.totalUniqueUsers || 0;
      }

      setStats({
        newClientsThisMonth: clientsData.stats?.newClientsThisMonth || 0,
        activeRate: clientsData.stats?.activeRate || 0,
        tryOnsGrowth: clientsData.stats?.tryOnsGrowth || 0,
        uniqueUsers: uniqueUsersCount
      });

    } catch (err) {
      console.error('Error fetching clients:', err);
      setError(err.message);
      setClients([]);
      setStats({
        newClientsThisMonth: 0,
        activeRate: 0,
        tryOnsGrowth: 0,
        uniqueUsers: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format last active time
  const formatLastActive = (date) => {
    if (!date) return 'Never';
    const now = new Date();
    const lastActive = new Date(date);
    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks} weeks ago`;
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setClientForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSuggestPassword = () => {
    setClientForm(prev => ({ ...prev, password: generatePassword() }));
  };

  const resetForm = () => {
    setClientForm({
      companyName: '',
      contactName: '',
      website: '',
      email: '',
      password: '',
      phone: '',
      industry: '',
      productType: 'watches',
      subscriptionPlan: 'basic'
    });
    setEditingClient(null);
  };

  const handleAddClient = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientForm)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create client');
      }

      await fetchClients();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error creating client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditClient = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientForm)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update client');
      }

      await fetchClients();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error updating client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async (clientId) => {
    if (!window.confirm('Are you sure you want to delete this client?')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients/${clientId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete client');
      }

      await fetchClients();
    } catch (err) {
      console.error('Error deleting client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const openEditModal = (client) => {
    setEditingClient(client);
    setClientForm({
      companyName: client.companyName || '',
      contactName: client.contactName || '',
      website: client.website || '',
      email: client.email || '',
      password: '', // Don't pre-fill password
      phone: client.phone || '',
      industry: client.industry || '',
      productType: client.productType || 'watches',
      subscriptionPlan: client.subscriptionPlan || 'basic'
    });
    setShowModal(true);
  };

  const openAddModal = () => {
    resetForm();
    setShowModal(true);
  };

  // Fetch client analytics for details popup
  const fetchClientAnalytics = async (clientId) => {
    try {
      setLoadingAnalytics(true);
      const token = localStorage.getItem('token');
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      // Calculate date range for last 30 days
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 30);

      const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([
        fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      const timeData = timeAnalysisResponse.ok ? await timeAnalysisResponse.json() : null;
      const productData = productPerformanceResponse.ok ? await productPerformanceResponse.json() : null;
      const deviceData = deviceStatsResponse.ok ? await deviceStatsResponse.json() : null;

      setClientAnalytics({
        timeAnalysis: timeData,
        productPerformance: productData,
        deviceStats: deviceData
      });
    } catch (error) {
      console.error('Error fetching client analytics:', error);
      setClientAnalytics(null);
    } finally {
      setLoadingAnalytics(false);
    }
  };

  // Handle view details popup
  const handleViewDetails = (client) => {
    setSelectedClientForDetails(client);
    setShowDetailsPopup(true);
    fetchClientAnalytics(client._id);
  };

  // Handle view code popup
  const handleViewCode = (client) => {
    setSelectedClientForCode(client);
    setShowCodePopup(true);
  };

  // Generate integration code
  const generateIntegrationCode = (client) => {
    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;
    return `<!-- ViatrOn Virtual Try-On Integration -->
<script>
function openViaTryon(productImageUrl, productSize = '42', productType = 'watches') {
  const tryonUrl = '${baseUrl}/tryon?' +
    'image=' + encodeURIComponent(productImageUrl) +
    '&client=${client._id}' +
    '&size=' + encodeURIComponent(productSize) +
    '&type=' + encodeURIComponent(productType);

  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
}
</script>

<!-- Try-On Button Example -->
<button
  onclick="openViaTryon('YOUR_PRODUCT_IMAGE_URL', '42', 'watches')"
  style="
    background-color: #2D8C88;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
  "
  onmouseover="this.style.backgroundColor='#236b68'"
  onmouseout="this.style.backgroundColor='#2D8C88'"
>
  Try On Virtually
</button>`;
  };

  // Copy code to clipboard
  const copyCodeToClipboard = () => {
    const code = generateIntegrationCode(selectedClientForCode);
    navigator.clipboard.writeText(code).then(() => {
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-16 transition-all duration-300`}>
        <div className="p-4 md:p-6">
          {/* Page Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
              <p className="text-gray-600">Manage your virtual try-on clients and track their performance.</p>
            </div>
            <button
              className="inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
              onClick={openAddModal}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+{stats.newClientsThisMonth} new</span>
                <span className="text-sm text-gray-600 ml-2">this month</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">{stats.activeRate.toFixed(1)}%</span>
                <span className="text-sm text-gray-600 ml-2">active rate</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-[#2D8C88]" />
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%
                </span>
                <span className="text-sm text-gray-600 ml-2">this month</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Users (by IP)</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : stats.uniqueUsers.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Activity className="h-6 w-6 text-purple-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-blue-600">
                  {uniqueUsersData?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} avg sessions
                </span>
                <span className="text-sm text-gray-600 ml-2">per user</span>
              </div>
            </motion.div>
          </div>

          {/* Add/Edit Client Modal */}
          <AnimatePresence>
            {showModal && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
              >
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden"
                >
                  {/* Header */}
                  <div className="bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4">
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-bold text-white">
                        {editingClient ? 'Edit Client' : 'Add New Client'}
                      </h2>
                      <button
                        className="text-white/80 hover:text-white transition-colors p-1"
                        onClick={() => setShowModal(false)}
                      >
                        <X className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  {/* Form */}
                  <div className="p-6">
                    <form onSubmit={editingClient ? handleEditClient : handleAddClient} className="space-y-5">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Company Name</label>
                          <input
                            type="text"
                            name="companyName"
                            value={clientForm.companyName}
                            onChange={handleFormChange}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter company name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Contact Name</label>
                          <input
                            type="text"
                            name="contactName"
                            value={clientForm.contactName}
                            onChange={handleFormChange}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter contact name"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Email</label>
                          <input
                            type="email"
                            name="email"
                            value={clientForm.email}
                            onChange={handleFormChange}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter email address"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Phone</label>
                          <input
                            type="tel"
                            name="phone"
                            value={clientForm.phone}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter phone number"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Website</label>
                          <input
                            type="url"
                            name="website"
                            value={clientForm.website}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="https://example.com"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Industry</label>
                          <input
                            type="text"
                            name="industry"
                            value={clientForm.industry}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="e.g., Fashion, Jewelry"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Product Type</label>
                          <select
                            name="productType"
                            value={clientForm.productType}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                          >
                            <option value="watches">Watches</option>
                            <option value="bracelets">Bracelets</option>
                            <option value="both">Both</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Subscription Plan</label>
                          <select
                            name="subscriptionPlan"
                            value={clientForm.subscriptionPlan}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                          >
                            <option value="basic">Basic</option>
                            <option value="premium">Premium</option>
                            <option value="enterprise">Enterprise</option>
                          </select>
                        </div>
                      </div>

                      {!editingClient && (
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Password</label>
                          <div className="flex gap-3">
                            <input
                              type="text"
                              name="password"
                              value={clientForm.password}
                              onChange={handleFormChange}
                              required
                              className="flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                              placeholder="Enter password"
                            />
                            <button
                              type="button"
                              onClick={handleSuggestPassword}
                              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                            >
                              Generate
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                        <button
                          type="button"
                          onClick={() => setShowModal(false)}
                          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm"
                        >
                          {editingClient ? 'Update Client' : 'Create Client'}
                        </button>
                      </div>
                    </form>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search clients..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                />
              </div>
              <div className="w-full md:w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>
          </div>

          {/* Clients Table */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Try-Ons</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Conversion</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Status</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Integration</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan="6" className="px-4 py-8 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto"></div>
                      </td>
                    </tr>
                  ) : error ? (
                    <tr>
                      <td colSpan="6" className="px-4 py-8 text-center text-red-600">
                        Error loading clients: {error}
                      </td>
                    </tr>
                  ) : clients.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="px-4 py-8 text-center text-gray-500">
                        No clients found
                      </td>
                    </tr>
                  ) : (
                    clients.map((client) => (
                      <motion.tr
                        key={client._id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                                {client.companyName?.charAt(0) || 'C'}
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{client.companyName}</div>
                              <div className="text-sm text-gray-500">{client.email}</div>
                              <div className="text-sm text-gray-500 lg:hidden">
                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.uniqueUsers || '0'} unique users
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <div className="text-sm font-medium text-gray-900">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>
                          <div className="text-sm text-gray-500">{client.analytics?.productCount || '0'} products</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <div className="text-sm font-medium text-gray-900">{client.analytics?.conversionRate || '0'}%</div>
                          <div className="text-sm text-gray-500">{client.analytics?.uniqueUsers || '0'} unique users</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden md:table-cell">
                          <div className="flex flex-col space-y-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :
                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {client.subscriptionStatus}
                            </span>
                            <span className="text-xs text-gray-500">{formatLastActive(client.analytics?.lastActive)}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {client.subscriptionPlan}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              className="text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors"
                              onClick={() => handleViewDetails(client)}
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                              onClick={() => handleViewCode(client)}
                              title="Integration Code"
                            >
                              <Code className="h-4 w-4" />
                            </button>
                            <button
                              className="text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                              onClick={() => openEditModal(client)}
                              title="Edit Client"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors"
                              onClick={() => handleDeleteClient(client._id)}
                              title="Delete Client"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </motion.tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* View Details Popup */}
          <AnimatePresence>
            {showDetailsPopup && selectedClientForDetails && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
              >
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
                >
                  {/* Header */}
                  <div className="bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white font-bold">
                          {selectedClientForDetails.companyName?.charAt(0) || 'C'}
                        </div>
                        <div>
                          <h2 className="text-xl font-bold text-white">
                            {selectedClientForDetails.companyName}
                          </h2>
                          <p className="text-white/80 text-sm">
                            {selectedClientForDetails.email}
                          </p>
                        </div>
                      </div>
                      <button
                        className="text-white/80 hover:text-white transition-colors p-1"
                        onClick={() => setShowDetailsPopup(false)}
                      >
                        <X className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
                    {loadingAnalytics ? (
                      <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {/* Client Info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center">
                                <Users className="h-5 w-5 text-white" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-blue-600">Total Sessions</p>
                                <p className="text-xl font-bold text-blue-900">
                                  {clientAnalytics?.timeAnalysis?.dailyTrends?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center">
                                <Clock className="h-5 w-5 text-white" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-green-600">Avg Duration</p>
                                <p className="text-xl font-bold text-green-900">
                                  {Math.round(clientAnalytics?.timeAnalysis?.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / (clientAnalytics?.timeAnalysis?.dailyTrends?.length || 1)) || 0}s
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 rounded-lg bg-purple-500 flex items-center justify-center">
                                <BarChart3 className="h-5 w-5 text-white" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-purple-600">Products</p>
                                <p className="text-xl font-bold text-purple-900">
                                  {clientAnalytics?.productPerformance?.length || 0}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 rounded-lg bg-orange-500 flex items-center justify-center">
                                <Smartphone className="h-5 w-5 text-white" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-orange-600">Devices</p>
                                <p className="text-xl font-bold text-orange-900">
                                  {clientAnalytics?.deviceStats?.length || 0}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Recent Activity */}
                        <div className="bg-gray-50 rounded-xl p-6">
                          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <Activity className="h-5 w-5 mr-2 text-[#2D8C88]" />
                            Recent Activity
                          </h3>
                          <div className="space-y-3">
                            {clientAnalytics?.timeAnalysis?.dailyTrends?.slice(-5).map((day, index) => (
                              <div key={index} className="flex items-center justify-between py-2 px-3 bg-white rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className="w-2 h-2 rounded-full bg-[#2D8C88]"></div>
                                  <span className="text-sm font-medium text-gray-900">
                                    {new Date(day.date).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  {day.sessions} sessions • {Math.round(day.avgDuration || 0)}s avg
                                </div>
                              </div>
                            )) || (
                              <p className="text-gray-500 text-center py-4">No recent activity data available</p>
                            )}
                          </div>
                        </div>

                        {/* Device Breakdown */}
                        {clientAnalytics?.deviceStats && clientAnalytics.deviceStats.length > 0 && (
                          <div className="bg-gray-50 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                              <Monitor className="h-5 w-5 mr-2 text-[#2D8C88]" />
                              Device Usage
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {clientAnalytics.deviceStats.map((device, index) => (
                                <div key={index} className="bg-white rounded-lg p-4">
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-900 capitalize">
                                      {device.device || 'Unknown'}
                                    </span>
                                    <span className="text-sm text-gray-600">
                                      {device.sessions} sessions
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* View Code Popup */}
          <AnimatePresence>
            {showCodePopup && selectedClientForCode && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
              >
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden"
                >
                  {/* Header */}
                  <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center">
                          <Code className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h2 className="text-xl font-bold text-white">
                            Integration Code
                          </h2>
                          <p className="text-white/80 text-sm">
                            {selectedClientForCode.companyName}
                          </p>
                        </div>
                      </div>
                      <button
                        className="text-white/80 hover:text-white transition-colors p-1"
                        onClick={() => setShowCodePopup(false)}
                      >
                        <X className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <div className="space-y-6">
                      {/* Instructions */}
                      <div className="bg-blue-50 rounded-xl p-4">
                        <h3 className="text-lg font-semibold text-blue-900 mb-2">
                          How to integrate ViatrOn
                        </h3>
                        <div className="space-y-2 text-sm text-blue-800">
                          <p>1. Copy the code below and paste it into your website's HTML</p>
                          <p>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URLs</p>
                          <p>3. Customize the button styling to match your brand</p>
                          <p>4. Test the integration to ensure it works correctly</p>
                        </div>
                      </div>

                      {/* Code Block */}
                      <div className="relative">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">
                            Integration Code
                          </h3>
                          <button
                            onClick={copyCodeToClipboard}
                            className="inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors"
                          >
                            {copiedCode ? (
                              <>
                                <Check className="h-4 w-4 mr-2" />
                                Copied!
                              </>
                            ) : (
                              <>
                                <Copy className="h-4 w-4 mr-2" />
                                Copy Code
                              </>
                            )}
                          </button>
                        </div>

                        <div className="bg-gray-900 rounded-xl p-4 overflow-x-auto">
                          <pre className="text-green-400 text-sm">
                            <code>{generateIntegrationCode(selectedClientForCode)}</code>
                          </pre>
                        </div>
                      </div>

                      {/* Example Usage */}
                      <div className="bg-gray-50 rounded-xl p-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">
                          Example Usage
                        </h3>
                        <div className="space-y-3">
                          <div className="bg-white rounded-lg p-3 border">
                            <p className="text-sm font-medium text-gray-700 mb-2">For Watches:</p>
                            <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                              openViaTryon('https://example.com/watch.jpg', '42', 'watches')
                            </code>
                          </div>
                          <div className="bg-white rounded-lg p-3 border">
                            <p className="text-sm font-medium text-gray-700 mb-2">For Bracelets:</p>
                            <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                              openViaTryon('https://example.com/bracelet.jpg', '15', 'bracelets')
                            </code>
                          </div>
                        </div>
                      </div>

                      {/* Support Info */}
                      <div className="bg-yellow-50 rounded-xl p-4">
                        <h3 className="text-lg font-semibold text-yellow-900 mb-2">
                          Need Help?
                        </h3>
                        <p className="text-sm text-yellow-800">
                          If you need assistance with the integration, please contact our support team or refer to our documentation.
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </main>
    </div>
  );
};

export default Clients;