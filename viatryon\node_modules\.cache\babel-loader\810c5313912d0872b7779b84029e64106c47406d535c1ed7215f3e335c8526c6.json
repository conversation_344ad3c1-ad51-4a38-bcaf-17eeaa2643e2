{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\client\\\\ClientSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Loader, AlertCircle, CheckCircle } from 'lucide-react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientSettings = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState({\n    type: '',\n    text: ''\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n  const [profile, setProfile] = useState({\n    companyName: '',\n    contactName: '',\n    email: '',\n    phone: '',\n    website: '',\n    industry: '',\n    productType: 'watches'\n  });\n\n  // Load user profile data\n  useEffect(() => {\n    const loadProfile = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n        const response = await axios.get(`${apiUrl}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.data) {\n          const userData = response.data;\n          setProfile({\n            companyName: userData.companyName || '',\n            contactName: userData.contactName || '',\n            email: userData.email || '',\n            phone: userData.phone || '',\n            website: userData.website || '',\n            industry: userData.industry || '',\n            productType: userData.productType || 'watches'\n          });\n        } else {\n          setMessage({\n            type: 'error',\n            text: 'Failed to load profile data'\n          });\n        }\n      } catch (error) {\n        var _error$response, _error$response$data;\n        console.error('Error loading profile:', error);\n        setMessage({\n          type: 'error',\n          text: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Error loading profile data'\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProfile();\n  }, []);\n  const handleInputChange = (field, value) => {\n    setProfile(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear any existing messages when user starts typing\n    if (message.text) {\n      setMessage({\n        type: '',\n        text: ''\n      });\n    }\n  };\n  const handleSave = async () => {\n    try {\n      setSaving(true);\n      setMessage({\n        type: '',\n        text: ''\n      });\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setMessage({\n          type: 'error',\n          text: 'No authentication token found'\n        });\n        return;\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await axios.put(`${apiUrl}/api/auth/update-profile`, profile, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data) {\n        setMessage({\n          type: 'success',\n          text: 'Profile updated successfully!'\n        });\n\n        // Update localStorage with new user data\n        const userData = localStorage.getItem('user');\n        if (userData) {\n          const updatedUser = {\n            ...JSON.parse(userData),\n            ...response.data.user\n          };\n          localStorage.setItem('user', JSON.stringify(updatedUser));\n        }\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error saving profile:', error);\n      setMessage({\n        type: 'error',\n        text: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Error saving profile data'\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const renderProfileContent = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Loader, {\n          className: \"h-8 w-8 animate-spin text-[#2D8C88]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-2 text-gray-600\",\n          children: \"Loading profile...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [message.text && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 rounded-lg flex items-center ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'}`,\n        children: [message.type === 'success' ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 15\n        }, this), message.text]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Company Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: profile.companyName,\n              onChange: e => handleInputChange('companyName', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Contact Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: profile.contactName,\n              onChange: e => handleInputChange('contactName', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Email Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: profile.email,\n              onChange: e => handleInputChange('email', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              value: profile.phone,\n              onChange: e => handleInputChange('phone', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Website URL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              value: profile.website,\n              onChange: e => handleInputChange('website', e.target.value),\n              placeholder: \"https://yourcompany.com\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Industry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: profile.industry,\n              onChange: e => handleInputChange('industry', e.target.value),\n              placeholder: \"e.g., Fashion, Jewelry, Watches\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Primary Product Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: profile.productType,\n              onChange: e => handleInputChange('productType', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"watches\",\n                children: \"Watches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"bracelets\",\n                children: \"Bracelets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"both\",\n                children: \"Both Watches & Bracelets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"This will be the default product type for your embed codes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-20 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Profile Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your company profile and contact information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b border-gray-200 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"h-5 w-5 text-[#2D8C88] mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: \"Profile Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: renderProfileContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              disabled: saving,\n              className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: [saving ? /*#__PURE__*/_jsxDEV(Loader, {\n                className: \"h-4 w-4 mr-2 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Save, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), saving ? 'Saving...' : 'Save Changes']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientSettings, \"2QVlZgPaZI4VayRVQzsAovsJs+I=\");\n_c = ClientSettings;\nexport default ClientSettings;\nvar _c;\n$RefreshReg$(_c, \"ClientSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ClientSidebar", "ClientNavbar", "motion", "Save", "User", "Loader", "AlertCircle", "CheckCircle", "axios", "jsxDEV", "_jsxDEV", "ClientSettings", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "loading", "setLoading", "saving", "setSaving", "message", "setMessage", "type", "text", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "profile", "setProfile", "companyName", "contactName", "email", "phone", "website", "industry", "productType", "loadProfile", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "response", "get", "headers", "data", "userData", "error", "_error$response", "_error$response$data", "console", "handleInputChange", "field", "value", "prev", "handleSave", "put", "updatedUser", "JSON", "parse", "user", "setItem", "stringify", "_error$response2", "_error$response2$data", "renderProfileContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "required", "placeholder", "isOpen", "onClose", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/client/ClientSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Loader, AlertCircle, CheckCircle } from 'lucide-react';\nimport axios from 'axios';\n\nconst ClientSettings = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState({ type: '', text: '' });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  const [profile, setProfile] = useState({\n    companyName: '',\n    contactName: '',\n    email: '',\n    phone: '',\n    website: '',\n    industry: '',\n    productType: 'watches'\n  });\n\n  // Load user profile data\n  useEffect(() => {\n    const loadProfile = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        const response = await axios.get(`${apiUrl}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.data) {\n          const userData = response.data;\n          setProfile({\n            companyName: userData.companyName || '',\n            contactName: userData.contactName || '',\n            email: userData.email || '',\n            phone: userData.phone || '',\n            website: userData.website || '',\n            industry: userData.industry || '',\n            productType: userData.productType || 'watches'\n          });\n        } else {\n          setMessage({ type: 'error', text: 'Failed to load profile data' });\n        }\n      } catch (error) {\n        console.error('Error loading profile:', error);\n        setMessage({ \n          type: 'error', \n          text: error.response?.data?.message || 'Error loading profile data' \n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadProfile();\n  }, []);\n\n  const handleInputChange = (field, value) => {\n    setProfile(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear any existing messages when user starts typing\n    if (message.text) {\n      setMessage({ type: '', text: '' });\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      setSaving(true);\n      setMessage({ type: '', text: '' });\n\n      const token = localStorage.getItem('token');\n\n      if (!token) {\n        setMessage({ type: 'error', text: 'No authentication token found' });\n        return;\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await axios.put(\n        `${apiUrl}/api/auth/update-profile`,\n        profile,\n        {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data) {\n        setMessage({ type: 'success', text: 'Profile updated successfully!' });\n\n        // Update localStorage with new user data\n        const userData = localStorage.getItem('user');\n        if (userData) {\n          const updatedUser = { ...JSON.parse(userData), ...response.data.user };\n          localStorage.setItem('user', JSON.stringify(updatedUser));\n        }\n      }\n    } catch (error) {\n      console.error('Error saving profile:', error);\n      setMessage({ \n        type: 'error', \n        text: error.response?.data?.message || 'Error saving profile data' \n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const renderProfileContent = () => {\n    if (loading) {\n      return (\n        <div className=\"flex items-center justify-center py-12\">\n          <Loader className=\"h-8 w-8 animate-spin text-[#2D8C88]\" />\n          <span className=\"ml-2 text-gray-600\">Loading profile...</span>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Message Display */}\n        {message.text && (\n          <div className={`p-4 rounded-lg flex items-center ${\n            message.type === 'success'\n              ? 'bg-green-50 text-green-800 border border-green-200'\n              : 'bg-red-50 text-red-800 border border-red-200'\n          }`}>\n            {message.type === 'success' ? (\n              <CheckCircle className=\"h-5 w-5 mr-2\" />\n            ) : (\n              <AlertCircle className=\"h-5 w-5 mr-2\" />\n            )}\n            {message.text}\n          </div>\n        )}\n\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company Name *\n              </label>\n              <input\n                type=\"text\"\n                value={profile.companyName}\n                onChange={(e) => handleInputChange('companyName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Contact Name *\n              </label>\n              <input\n                type=\"text\"\n                value={profile.contactName}\n                onChange={(e) => handleInputChange('contactName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Email Address *\n              </label>\n              <input\n                type=\"email\"\n                value={profile.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                value={profile.phone}\n                onChange={(e) => handleInputChange('phone', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Website URL\n              </label>\n              <input\n                type=\"url\"\n                value={profile.website}\n                onChange={(e) => handleInputChange('website', e.target.value)}\n                placeholder=\"https://yourcompany.com\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Industry\n              </label>\n              <input\n                type=\"text\"\n                value={profile.industry}\n                onChange={(e) => handleInputChange('industry', e.target.value)}\n                placeholder=\"e.g., Fashion, Jewelry, Watches\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Primary Product Type\n              </label>\n              <select\n                value={profile.productType}\n                onChange={(e) => handleInputChange('productType', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              >\n                <option value=\"watches\">Watches</option>\n                <option value=\"bracelets\">Bracelets</option>\n                <option value=\"both\">Both Watches & Bracelets</option>\n              </select>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                This will be the default product type for your embed codes\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-20 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Profile Settings</h1>\n            <p className=\"text-gray-600\">Manage your company profile and contact information</p>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            {/* Header */}\n            <div className=\"border-b border-gray-200 px-6 py-4\">\n              <div className=\"flex items-center\">\n                <User className=\"h-5 w-5 text-[#2D8C88] mr-2\" />\n                <h2 className=\"text-lg font-medium text-gray-900\">Profile Information</h2>\n              </div>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-6\">\n              {renderProfileContent()}\n            </div>\n\n            {/* Save Button */}\n            <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end\">\n              <button\n                onClick={handleSave}\n                disabled={saving}\n                className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {saving ? (\n                  <Loader className=\"h-4 w-4 mr-2 animate-spin\" />\n                ) : (\n                  <Save className=\"h-4 w-4 mr-2\" />\n                )}\n                {saving ? 'Saving...' : 'Save Changes'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default ClientSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAC3E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC;IAAEyB,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAE9D,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BX,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMa,UAAU,GAAGX,SAAS,GAAG,cAAc,GAAG,eAAe;EAE/D,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC;IACrC+B,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACApC,SAAS,CAAC,MAAM;IACd,MAAMqC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFlB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMmB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;QAClD;QAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;QAErE,MAAMO,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,GAAGJ,MAAM,cAAc,EAAE;UACxDK,OAAO,EAAE;YACP,eAAe,EAAE,UAAUb,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIW,QAAQ,CAACG,IAAI,EAAE;UACjB,MAAMC,QAAQ,GAAGJ,QAAQ,CAACG,IAAI;UAC9BvB,UAAU,CAAC;YACTC,WAAW,EAAEuB,QAAQ,CAACvB,WAAW,IAAI,EAAE;YACvCC,WAAW,EAAEsB,QAAQ,CAACtB,WAAW,IAAI,EAAE;YACvCC,KAAK,EAAEqB,QAAQ,CAACrB,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEoB,QAAQ,CAACpB,KAAK,IAAI,EAAE;YAC3BC,OAAO,EAAEmB,QAAQ,CAACnB,OAAO,IAAI,EAAE;YAC/BC,QAAQ,EAAEkB,QAAQ,CAAClB,QAAQ,IAAI,EAAE;YACjCC,WAAW,EAAEiB,QAAQ,CAACjB,WAAW,IAAI;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLb,UAAU,CAAC;YAAEC,IAAI,EAAE,OAAO;YAAEC,IAAI,EAAE;UAA8B,CAAC,CAAC;QACpE;MACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;QAAA,IAAAC,eAAA,EAAAC,oBAAA;QACdC,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C/B,UAAU,CAAC;UACTC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,EAAA8B,eAAA,GAAAD,KAAK,CAACL,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBlC,OAAO,KAAI;QACzC,CAAC,CAAC;MACJ,CAAC,SAAS;QACRH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C/B,UAAU,CAACgC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;IACH;IACA,IAAItC,OAAO,CAACG,IAAI,EAAE;MAChBF,UAAU,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;IACpC;EACF,CAAC;EAED,MAAMqC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzC,SAAS,CAAC,IAAI,CAAC;MACfE,UAAU,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;MAElC,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI,CAACF,KAAK,EAAE;QACVf,UAAU,CAAC;UAAEC,IAAI,EAAE,OAAO;UAAEC,IAAI,EAAE;QAAgC,CAAC,CAAC;QACpE;MACF;MAEA,MAAMiB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMO,QAAQ,GAAG,MAAMxC,KAAK,CAACsD,GAAG,CAC9B,GAAGjB,MAAM,0BAA0B,EACnClB,OAAO,EACP;QACEuB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUb,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIW,QAAQ,CAACG,IAAI,EAAE;QACjB7B,UAAU,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAgC,CAAC,CAAC;;QAEtE;QACA,MAAM4B,QAAQ,GAAGd,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC7C,IAAIa,QAAQ,EAAE;UACZ,MAAMW,WAAW,GAAG;YAAE,GAAGC,IAAI,CAACC,KAAK,CAACb,QAAQ,CAAC;YAAE,GAAGJ,QAAQ,CAACG,IAAI,CAACe;UAAK,CAAC;UACtE5B,YAAY,CAAC6B,OAAO,CAAC,MAAM,EAAEH,IAAI,CAACI,SAAS,CAACL,WAAW,CAAC,CAAC;QAC3D;MACF;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACdd,OAAO,CAACH,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C/B,UAAU,CAAC;QACTC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,EAAA6C,gBAAA,GAAAhB,KAAK,CAACL,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBjD,OAAO,KAAI;MACzC,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMmD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAItD,OAAO,EAAE;MACX,oBACEP,OAAA;QAAK8D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD/D,OAAA,CAACL,MAAM;UAACmE,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DnE,OAAA;UAAM8D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAEV;IAEA,oBACEnE,OAAA;MAAK8D,SAAS,EAAC,WAAW;MAAAC,QAAA,GAEvBpD,OAAO,CAACG,IAAI,iBACXd,OAAA;QAAK8D,SAAS,EAAE,oCACdnD,OAAO,CAACE,IAAI,KAAK,SAAS,GACtB,oDAAoD,GACpD,8CAA8C,EACjD;QAAAkD,QAAA,GACApD,OAAO,CAACE,IAAI,KAAK,SAAS,gBACzBb,OAAA,CAACH,WAAW;UAACiE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAExCnE,OAAA,CAACJ,WAAW;UAACkE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACxC,EACAxD,OAAO,CAACG,IAAI;MAAA;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAEDnE,OAAA;QAAA+D,QAAA,gBACE/D,OAAA;UAAI8D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EnE,OAAA;UAAK8D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEa,IAAI,EAAC,MAAM;cACXoC,KAAK,EAAEhC,OAAO,CAACE,WAAY;cAC3BiD,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,aAAa,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAClEa,SAAS,EAAC,kIAAkI;cAC5IS,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEa,IAAI,EAAC,MAAM;cACXoC,KAAK,EAAEhC,OAAO,CAACG,WAAY;cAC3BgD,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,aAAa,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAClEa,SAAS,EAAC,kIAAkI;cAC5IS,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEa,IAAI,EAAC,OAAO;cACZoC,KAAK,EAAEhC,OAAO,CAACI,KAAM;cACrB+C,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,OAAO,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAC5Da,SAAS,EAAC,kIAAkI;cAC5IS,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEa,IAAI,EAAC,KAAK;cACVoC,KAAK,EAAEhC,OAAO,CAACK,KAAM;cACrB8C,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,OAAO,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAC5Da,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEa,IAAI,EAAC,KAAK;cACVoC,KAAK,EAAEhC,OAAO,CAACM,OAAQ;cACvB6C,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,SAAS,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAC9DuB,WAAW,EAAC,yBAAyB;cACrCV,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEa,IAAI,EAAC,MAAM;cACXoC,KAAK,EAAEhC,OAAO,CAACO,QAAS;cACxB4C,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAC/DuB,WAAW,EAAC,iCAAiC;cAC7CV,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAO8D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACEiD,KAAK,EAAEhC,OAAO,CAACQ,WAAY;cAC3B2C,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,aAAa,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAClEa,SAAS,EAAC,kIAAkI;cAAAC,QAAA,gBAE5I/D,OAAA;gBAAQiD,KAAK,EAAC,SAAS;gBAAAc,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCnE,OAAA;gBAAQiD,KAAK,EAAC,WAAW;gBAAAc,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CnE,OAAA;gBAAQiD,KAAK,EAAC,MAAM;gBAAAc,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACTnE,OAAA;cAAG8D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEnE,OAAA;IAAK8D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC/D,OAAA,CAACV,aAAa;MAACmF,MAAM,EAAEtE,aAAc;MAACuE,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClInE,OAAA,CAACT,YAAY;MAACwB,aAAa,EAAEA,aAAc;MAACV,SAAS,EAAEA;IAAU;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpEnE,OAAA;MAAM8D,SAAS,EAAE,GAAG9C,UAAU,oCAAqC;MAAA+C,QAAA,eACjE/D,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzB/D,OAAA;UAAK8D,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/D,OAAA;YAAI8D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEnE,OAAA;YAAG8D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAENnE,OAAA;UAAK8D,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAE5D/D,OAAA;YAAK8D,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjD/D,OAAA;cAAK8D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/D,OAAA,CAACN,IAAI;gBAACoE,SAAS,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDnE,OAAA;gBAAI8D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA;YAAK8D,SAAS,EAAC,KAAK;YAAAC,QAAA,EACjBF,oBAAoB,CAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAGNnE,OAAA;YAAK8D,SAAS,EAAC,gEAAgE;YAAAC,QAAA,eAC7E/D,OAAA;cACE2E,OAAO,EAAExB,UAAW;cACpByB,QAAQ,EAAEnE,MAAO;cACjBqD,SAAS,EAAC,6NAA6N;cAAAC,QAAA,GAEtOtD,MAAM,gBACLT,OAAA,CAACL,MAAM;gBAACmE,SAAS,EAAC;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEhDnE,OAAA,CAACP,IAAI;gBAACqE,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACjC,EACA1D,MAAM,GAAG,WAAW,GAAG,cAAc;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjE,EAAA,CAhTID,cAAc;AAAA4E,EAAA,GAAd5E,cAAc;AAkTpB,eAAeA,cAAc;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}