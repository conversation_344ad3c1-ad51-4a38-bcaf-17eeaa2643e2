{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\client\\\\analytics\\\\ProductPerformance.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { Package, Clock, TrendingUp, Users } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductPerformance = () => {\n  _s();\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedMetric, setSelectedMetric] = useState('sessions');\n  const [loading, setLoading] = useState(true);\n  const [productData, setProductData] = useState([]);\n\n  // Fetch product performance data\n  useEffect(() => {\n    const fetchProductData = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          console.error('No authentication token found');\n          return;\n        }\n        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n        const response = await fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          setProductData(data);\n        } else {\n          console.error('Failed to fetch product performance data');\n        }\n      } catch (error) {\n        console.error('Error fetching product performance:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProductData();\n  }, [timeRange]);\n\n  // Format data for charts\n  const chartData = productData.map(product => ({\n    name: product.productName || 'Unknown Product',\n    sessions: product.sessions || 0,\n    avgDuration: Math.round(product.avgDuration || 0),\n    interactions: product.totalInteractions || 0\n  }));\n\n  // Calculate metrics for display\n  const totalSessions = productData.reduce((sum, product) => sum + (product.sessions || 0), 0);\n  const avgDuration = productData.length > 0 ? Math.round(productData.reduce((sum, product) => sum + (product.avgDuration || 0), 0) / productData.length) : 0;\n  const totalInteractions = productData.reduce((sum, product) => sum + (product.totalInteractions || 0), 0);\n  const totalProducts = productData.length;\n  const metrics = [{\n    title: 'Total Products',\n    value: totalProducts,\n    icon: /*#__PURE__*/_jsxDEV(Package, {\n      className: \"h-6 w-6 text-[#2D8C88]\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: 'Total Sessions',\n    value: totalSessions,\n    icon: /*#__PURE__*/_jsxDEV(Users, {\n      className: \"h-6 w-6 text-blue-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: 'Avg. Duration',\n    value: `${avgDuration}s`,\n    icon: /*#__PURE__*/_jsxDEV(Clock, {\n      className: \"h-6 w-6 text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this)\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 bg-gray-200 rounded w-1/2 mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-1/4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-96 bg-gray-200 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setTimeRange(range),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: range\n        }, range, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n      children: metrics.map((metric, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: metric.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900 mt-1\",\n              children: metric.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n            children: metric.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)\n      }, metric.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: [{\n          id: 'sessions',\n          label: 'Sessions'\n        }, {\n          id: 'avgDuration',\n          label: 'Avg. Duration'\n        }].map(metric => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedMetric(metric.id),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${selectedMetric === metric.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: metric.label\n        }, metric.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      className: \"bg-white rounded-xl shadow-sm p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Product Performance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-96\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            data: chartData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: selectedMetric,\n              fill: \"#2D8C88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Product Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Avg. Duration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: chartData.map((product, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: product.sessions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: [product.avgDuration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductPerformance, \"ez48Y86UnJlVG9xHYj6u0QG9rP0=\");\n_c = ProductPerformance;\nexport default ProductPerformance;\nvar _c;\n$RefreshReg$(_c, \"ProductPerformance\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON><PERSON>", "Bar", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Package", "Clock", "TrendingUp", "Users", "jsxDEV", "_jsxDEV", "ProductPerformance", "_s", "timeRange", "setTimeRange", "selectedMetric", "setSelectedMetric", "loading", "setLoading", "productData", "setProductData", "fetchProductData", "token", "localStorage", "getItem", "console", "error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "end", "Date", "start", "setDate", "getDate", "setFullYear", "getFullYear", "response", "fetch", "toISOString", "headers", "ok", "data", "json", "chartData", "map", "product", "name", "productName", "sessions", "avgDuration", "Math", "round", "interactions", "totalInteractions", "totalSessions", "reduce", "sum", "length", "totalProducts", "metrics", "title", "value", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "i", "range", "onClick", "metric", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "id", "label", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "fill", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/client/analytics/ProductPerformance.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  <PERSON><PERSON>hart,\n  Bar,\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\nimport { Package, Clock, TrendingUp, Users } from 'lucide-react';\n\nconst ProductPerformance = () => {\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedMetric, setSelectedMetric] = useState('sessions');\n  const [loading, setLoading] = useState(true);\n  const [productData, setProductData] = useState([]);\n\n  // Fetch product performance data\n  useEffect(() => {\n    const fetchProductData = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (!token) {\n          console.error('No authentication token found');\n          return;\n        }\n\n        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n\n        const response = await fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          setProductData(data);\n        } else {\n          console.error('Failed to fetch product performance data');\n        }\n      } catch (error) {\n        console.error('Error fetching product performance:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProductData();\n  }, [timeRange]);\n\n  // Format data for charts\n  const chartData = productData.map(product => ({\n    name: product.productName || 'Unknown Product',\n    sessions: product.sessions || 0,\n    avgDuration: Math.round(product.avgDuration || 0),\n    interactions: product.totalInteractions || 0\n  }));\n\n  // Calculate metrics for display\n  const totalSessions = productData.reduce((sum, product) => sum + (product.sessions || 0), 0);\n  const avgDuration = productData.length > 0 \n    ? Math.round(productData.reduce((sum, product) => sum + (product.avgDuration || 0), 0) / productData.length)\n    : 0;\n  const totalInteractions = productData.reduce((sum, product) => sum + (product.totalInteractions || 0), 0);\n  const totalProducts = productData.length;\n\n  const metrics = [\n    {\n      title: 'Total Products',\n      value: totalProducts,\n      icon: <Package className=\"h-6 w-6 text-[#2D8C88]\" />\n    },\n    {\n      title: 'Total Sessions',\n      value: totalSessions,\n      icon: <Users className=\"h-6 w-6 text-blue-500\" />\n    },\n    {\n      title: 'Avg. Duration',\n      value: `${avgDuration}s`,\n      icon: <Clock className=\"h-6 w-6 text-green-500\" />\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            {[1, 2, 3, 4].map((i) => (\n              <div key={i} className=\"bg-white rounded-xl shadow-sm p-6\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-4\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n              </div>\n            ))}\n          </div>\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <div className=\"h-6 bg-gray-200 rounded w-1/4 mb-4\"></div>\n            <div className=\"h-96 bg-gray-200 rounded\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Time Range Selector */}\n      <div className=\"flex justify-end\">\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {['7d', '30d', '90d', '1y'].map((range) => (\n            <button\n              key={range}\n              onClick={() => setTimeRange(range)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                timeRange === range\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {range}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {metrics.map((metric, index) => (\n          <motion.div\n            key={metric.title}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-white rounded-xl shadow-sm p-6\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{metric.title}</p>\n                <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{metric.value}</p>\n              </div>\n              <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                {metric.icon}\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Metric Selector */}\n      <div className=\"flex justify-end\">\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {[\n            { id: 'sessions', label: 'Sessions' },\n            { id: 'avgDuration', label: 'Avg. Duration' }\n          ].map((metric) => (\n            <button\n              key={metric.id}\n              onClick={() => setSelectedMetric(metric.id)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                selectedMetric === metric.id\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {metric.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Product Performance Chart */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n        className=\"bg-white rounded-xl shadow-sm p-6\"\n      >\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Product Performance</h3>\n        <div className=\"h-96\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <BarChart data={chartData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"name\" />\n              <YAxis />\n              <Tooltip />\n              <Bar dataKey={selectedMetric} fill=\"#2D8C88\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      </motion.div>\n\n      {/* Product Details Table */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n        className=\"bg-white rounded-xl shadow-sm overflow-hidden\"\n      >\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Product Details</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Product</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Sessions</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Avg. Duration</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {chartData.map((product, index) => (\n                <tr key={index} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{product.name}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{product.sessions}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{product.avgDuration}s</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProductPerformance; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AACjB,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,UAAU,CAAC;EAChE,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAI,CAACF,KAAK,EAAE;UACVG,OAAO,CAACC,KAAK,CAAC,+BAA+B,CAAC;UAC9C;QACF;QAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;QAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;QAErE;QACA,MAAMO,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,IAAIC,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,QAAQtB,SAAS;UACf,KAAK,IAAI;YACPuB,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,IAAI;YACPF,KAAK,CAACG,WAAW,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C;UACF;YACEJ,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC;QAEA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGX,MAAM,mDAAmDK,KAAK,CAACO,WAAW,CAAC,CAAC,QAAQT,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,EAAE;UACvIC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUtB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAImB,QAAQ,CAACI,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;UAClC3B,cAAc,CAAC0B,IAAI,CAAC;QACtB,CAAC,MAAM;UACLrB,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;QAC3D;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMmC,SAAS,GAAG7B,WAAW,CAAC8B,GAAG,CAACC,OAAO,KAAK;IAC5CC,IAAI,EAAED,OAAO,CAACE,WAAW,IAAI,iBAAiB;IAC9CC,QAAQ,EAAEH,OAAO,CAACG,QAAQ,IAAI,CAAC;IAC/BC,WAAW,EAAEC,IAAI,CAACC,KAAK,CAACN,OAAO,CAACI,WAAW,IAAI,CAAC,CAAC;IACjDG,YAAY,EAAEP,OAAO,CAACQ,iBAAiB,IAAI;EAC7C,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,aAAa,GAAGxC,WAAW,CAACyC,MAAM,CAAC,CAACC,GAAG,EAAEX,OAAO,KAAKW,GAAG,IAAIX,OAAO,CAACG,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5F,MAAMC,WAAW,GAAGnC,WAAW,CAAC2C,MAAM,GAAG,CAAC,GACtCP,IAAI,CAACC,KAAK,CAACrC,WAAW,CAACyC,MAAM,CAAC,CAACC,GAAG,EAAEX,OAAO,KAAKW,GAAG,IAAIX,OAAO,CAACI,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGnC,WAAW,CAAC2C,MAAM,CAAC,GAC1G,CAAC;EACL,MAAMJ,iBAAiB,GAAGvC,WAAW,CAACyC,MAAM,CAAC,CAACC,GAAG,EAAEX,OAAO,KAAKW,GAAG,IAAIX,OAAO,CAACQ,iBAAiB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACzG,MAAMK,aAAa,GAAG5C,WAAW,CAAC2C,MAAM;EAExC,MAAME,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAEH,aAAa;IACpBI,IAAI,eAAEzD,OAAA,CAACL,OAAO;MAAC+D,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACrD,CAAC,EACD;IACEP,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAEP,aAAa;IACpBQ,IAAI,eAAEzD,OAAA,CAACF,KAAK;MAAC4D,SAAS,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAClD,CAAC,EACD;IACEP,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAGZ,WAAW,GAAG;IACxBa,IAAI,eAAEzD,OAAA,CAACJ,KAAK;MAAC8D,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnD,CAAC,CACF;EAED,IAAIvD,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK0D,SAAS,EAAC,KAAK;MAAAK,QAAA,eAClB/D,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAK,QAAA,gBAC5B/D,OAAA;UAAK0D,SAAS,EAAC,oEAAoE;UAAAK,QAAA,EAChF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACxB,GAAG,CAAEyB,CAAC,iBAClBhE,OAAA;YAAa0D,SAAS,EAAC,mCAAmC;YAAAK,QAAA,gBACxD/D,OAAA;cAAK0D,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D9D,OAAA;cAAK0D,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D9D,OAAA;cAAK0D,SAAS,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAH7CE,CAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIN,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9D,OAAA;UAAK0D,SAAS,EAAC,mCAAmC;UAAAK,QAAA,gBAChD/D,OAAA;YAAK0D,SAAS,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1D9D,OAAA;YAAK0D,SAAS,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAK0D,SAAS,EAAC,WAAW;IAAAK,QAAA,gBAExB/D,OAAA;MAAK0D,SAAS,EAAC,kBAAkB;MAAAK,QAAA,eAC/B/D,OAAA;QAAK0D,SAAS,EAAC,mDAAmD;QAAAK,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACxB,GAAG,CAAE0B,KAAK,iBACpCjE,OAAA;UAEEkE,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAAC6D,KAAK,CAAE;UACnCP,SAAS,EAAE,4CACTvD,SAAS,KAAK8D,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;UAAAF,QAAA,EAEFE;QAAK,GARDA,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAK0D,SAAS,EAAC,sDAAsD;MAAAK,QAAA,EAClET,OAAO,CAACf,GAAG,CAAC,CAAC4B,MAAM,EAAEC,KAAK,kBACzBpE,OAAA,CAAChB,MAAM,CAACqF,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QACnCV,SAAS,EAAC,mCAAmC;QAAAK,QAAA,eAE7C/D,OAAA;UAAK0D,SAAS,EAAC,mCAAmC;UAAAK,QAAA,gBAChD/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAG0D,SAAS,EAAC,mCAAmC;cAAAK,QAAA,EAAEI,MAAM,CAACZ;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE9D,OAAA;cAAG0D,SAAS,EAAC,2CAA2C;cAAAK,QAAA,EAAEI,MAAM,CAACX;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACN9D,OAAA;YAAK0D,SAAS,EAAC,yEAAyE;YAAAK,QAAA,EACrFI,MAAM,CAACV;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAdDK,MAAM,CAACZ,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeP,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9D,OAAA;MAAK0D,SAAS,EAAC,kBAAkB;MAAAK,QAAA,eAC/B/D,OAAA;QAAK0D,SAAS,EAAC,mDAAmD;QAAAK,QAAA,EAC/D,CACC;UAAEa,EAAE,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAW,CAAC,EACrC;UAAED,EAAE,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAgB,CAAC,CAC9C,CAACtC,GAAG,CAAE4B,MAAM,iBACXnE,OAAA;UAEEkE,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAAC6D,MAAM,CAACS,EAAE,CAAE;UAC5ClB,SAAS,EAAE,4CACTrD,cAAc,KAAK8D,MAAM,CAACS,EAAE,GACxB,yBAAyB,GACzB,mCAAmC,EACtC;UAAAb,QAAA,EAEFI,MAAM,CAACU;QAAK,GARRV,MAAM,CAACS,EAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASR,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA,CAAChB,MAAM,CAACqF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BjB,SAAS,EAAC,mCAAmC;MAAAK,QAAA,gBAE7C/D,OAAA;QAAI0D,SAAS,EAAC,wCAAwC;QAAAK,QAAA,EAAC;MAAmB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/E9D,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAK,QAAA,eACnB/D,OAAA,CAACN,mBAAmB;UAACoF,KAAK,EAAC,MAAM;UAACC,MAAM,EAAC,MAAM;UAAAhB,QAAA,eAC7C/D,OAAA,CAACf,QAAQ;YAACmD,IAAI,EAAEE,SAAU;YAAAyB,QAAA,gBACxB/D,OAAA,CAACT,aAAa;cAACyF,eAAe,EAAC;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC9D,OAAA,CAACX,KAAK;cAAC4F,OAAO,EAAC;YAAM;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxB9D,OAAA,CAACV,KAAK;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACT9D,OAAA,CAACR,OAAO;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX9D,OAAA,CAACd,GAAG;cAAC+F,OAAO,EAAE5E,cAAe;cAAC6E,IAAI,EAAC;YAAS;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb9D,OAAA,CAAChB,MAAM,CAACqF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BjB,SAAS,EAAC,+CAA+C;MAAAK,QAAA,gBAEzD/D,OAAA;QAAK0D,SAAS,EAAC,oCAAoC;QAAAK,QAAA,eACjD/D,OAAA;UAAI0D,SAAS,EAAC,mCAAmC;UAAAK,QAAA,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACN9D,OAAA;QAAK0D,SAAS,EAAC,iBAAiB;QAAAK,QAAA,eAC9B/D,OAAA;UAAO0D,SAAS,EAAC,qCAAqC;UAAAK,QAAA,gBACpD/D,OAAA;YAAO0D,SAAS,EAAC,YAAY;YAAAK,QAAA,eAC3B/D,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAI0D,SAAS,EAAC,gFAAgF;gBAAAK,QAAA,EAAC;cAAO;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3G9D,OAAA;gBAAI0D,SAAS,EAAC,gFAAgF;gBAAAK,QAAA,EAAC;cAAQ;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5G9D,OAAA;gBAAI0D,SAAS,EAAC,gFAAgF;gBAAAK,QAAA,EAAC;cAAa;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9D,OAAA;YAAO0D,SAAS,EAAC,mCAAmC;YAAAK,QAAA,EACjDzB,SAAS,CAACC,GAAG,CAAC,CAACC,OAAO,EAAE4B,KAAK,kBAC5BpE,OAAA;cAAgB0D,SAAS,EAAC,kBAAkB;cAAAK,QAAA,gBAC1C/D,OAAA;gBAAI0D,SAAS,EAAC,+DAA+D;gBAAAK,QAAA,EAAEvB,OAAO,CAACC;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjG9D,OAAA;gBAAI0D,SAAS,EAAC,mDAAmD;gBAAAK,QAAA,EAAEvB,OAAO,CAACG;cAAQ;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzF9D,OAAA;gBAAI0D,SAAS,EAAC,mDAAmD;gBAAAK,QAAA,GAAEvB,OAAO,CAACI,WAAW,EAAC,GAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHtFM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAhPID,kBAAkB;AAAAkF,EAAA,GAAlBlF,kBAAkB;AAkPxB,eAAeA,kBAAkB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}